package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import com.simbest.boot.yjtxc.apply.service.IFourFDDBaseStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:55
 * @describe
 */
@Api(description = "4GFDD基站站号")
@Slf4j
@RestController
@RequestMapping(value = "/action/fourFDDBaseStation")
public class FourFDDBaseStationController extends LogicController<FourFDDBaseStation, String> {

    private IFourFDDBaseStationService service;

    public FourFDDBaseStationController(IFourFDDBaseStationService service) {
        super(service);
        this.service = service;
    }


    /**
     * 维护4GTDD基站站号
     * @return
     */
    @ApiOperation(value = "维护4GFDD基站站号", notes = "维护4GFDD基站站号")
    @PostMapping({"/updateFourFDDBaseStation", "/updateFourFDDBaseStation/sso"})
    public JsonResponse updateFourFDDBaseStation(@RequestBody FourFDDBaseStation fourFDDBaseStation){
        return service.updateFourFDDBaseStation(fourFDDBaseStation);
    }

    /**
     * 查询4GFDD基站站号
     * @param pmInsId
     * @return
     */
    @ApiOperation(value = "查询4GFDD基站站号", notes = "查询4GFDD基站站号")
    @PostMapping({"/findByPmInsIdFourFDDBaseStation", "/findByPmInsIdFourFDDBaseStation/sso"})
    public JsonResponse findByPmInsIdFourFDDBaseStation(@RequestParam String pmInsId){
        return service.findByPmInsIdFourFDDBaseStation(pmInsId);
    }

    /**
     * 维护4GTDD基站站号
     * @return
     */
    @ApiOperation(value = "维护4GFDD基站站号", notes = "维护4GFDD基站站号")
    @PostMapping({"/saveFourFDDBaseStation", "/saveFourFDDBaseStation/sso"})
    public JsonResponse saveFourFDDBaseStation(@RequestBody List<FourFDDBaseStation> fourFDDBaseStationlist,@RequestParam String pmInsId){
        return service.saveFourFDDBaseStation(fourFDDBaseStationlist,pmInsId);
    }

}
