<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .fontStyle {
            width: 160px;
            display: inline-block;
        }
    </style>
</head>
<body class="page_body">
<ul id="orgTree"></ul>
<div class="role orgC"></div>
<script type="text/javascript">
    var isSon = Boolean
    var gps=getQueryString();
    $(function(){
        $("#orgTree").tree({
            url:web.rootdir+"uums/sys/org/findPOrgAndCityOrg?appcode="+web.appCode,
            checkbox:true,//是否在每一个借点之前都显示复选框
            lines:true,//是否显示树控件上的虚线
            treeId:'orgCode',
            treePid:'parentOrgCode',
            cascadeCheck:false,
            onlyone:gps.multi==0?true:false,//不要乱配
            fileds:'orgCode|id,orgName|text,parentOrgCode,displayName,belongCompanyCode,treeType',
            animate:true,//节点在展开或折叠的时候是否显示动画效果
            onLoadSuccess:function(node, data){
                $("#orgTree").tree("insert", {
                    before: $("#orgTree").tree("find", data[0].children[0].children[0].id).target,
                    data:[{
                        id: "allDepart",
                        text: "公司各部门、各中心"
                    }]
                })
                $("#orgTree").tree("insert", {
                    before: $("#orgTree").tree("find", data[0].children[1].id).target,
                    data:[{
                        id: "allCom",
                        text: "各分公司"
                    }]
                })
                treeLoadSuccess();
            },
            onBeforeCheck:function(node,checked,a){
                if(gps.multi==0){
                    var nodes=$("#orgTree").tree("getChecked");
                    for(var i in nodes){
                        //var nodei=$("#orgTree").tree("find",nodes[i].id);
                        $("#orgTree").tree("uncheck",nodes[i].target);
                    }
                    $(".role").html("");
                }
            },
            onCheck:function(node,checked){
                if(node.id == "allDepart") {
                    // isSon = false
                    var siblings = $("#orgTree").tree("getParent", node.target).children
                    if(checked) {
                        for (var i in siblings) {
                            $("#orgTree").tree("check", $("#orgTree").tree("find", siblings[i].id).target)
                        }
                    }
                    if(!checked) {
                        //如果该节点是半选状态，不将其子节点取消选中
                        if($($("#orgTree").tree("find", "allDepart").target).attr('checkhalf')=='false'){
                            for (var i in siblings) {
                                $("#orgTree").tree("uncheck", $("#orgTree").tree("find", siblings[i].id).target)
                            }
                        }
                    }
                }else if(node.id == "allCom"){
                    var siblings = $("#orgTree").tree("getParent", node.target).children
                    siblings = siblings.slice(1)
                    if(checked) {
                        for (var i in siblings) {
                            $("#orgTree").tree("check", $("#orgTree").tree("find", siblings[i].id).target)
                        }
                    }
                    if(!checked) {
                        //如果该节点是半选状态，不将其子节点取消选中
                        if($($("#orgTree").tree("find", "allCom").target).attr('checkhalf')=='false'){
                            for (var i in siblings) {
                                $("#orgTree").tree("uncheck", $("#orgTree").tree("find", siblings[i].id).target)
                            }
                        }
                    }
                }else {
                    if(checked){
                        if($(".role a#"+node.id).length==0) $(".role").append("<a displayName='"+node.displayName+"' id='"+node.orgCode+"'"+(gps.type=="duty"?(" companyCode="+node.belongCompanyCode):"")+"><font class='over_point fontStyle'>"+node.text+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                        var siblings = $("#orgTree").tree("getParent", node.target).children
                        if(node.parentOrgCode == "00000000000000000000") siblings = siblings.slice(1)
                        if(myEvery(siblings, "checked")) {
                            if(node.parentOrgCode == "00000000000000000000") {
                                //此时已全部选中，将父节点标记为不是半选中
                                $($("#orgTree").tree("find", "allCom").target).attr('checkhalf',false)
                                $("#orgTree").tree("check", $("#orgTree").tree("find", "allCom").target)
                            } else {
                                //此时已全部选中，将父节点标记为不是半选中
                                $($("#orgTree").tree("find", "allDepart").target).attr('checkhalf',false)
                                $("#orgTree").tree("check", $("#orgTree").tree("find", "allDepart").target)
                            }
                        }
                    }else{
                        // isSon = true
                        $(".role a#"+node.orgCode).remove();
                        if(node.parentOrgCode == "00000000000000000000") {
                            //将父节点标记为半选中
                            $($("#orgTree").tree("find", "allCom").target).attr('checkhalf',true)
                            $("#orgTree").tree("uncheck", $("#orgTree").tree("find", "allCom").target)
                        } else {
                            //将父节点标记为半选中
                            $($("#orgTree").tree("find", "allDepart").target).attr('checkhalf',true)
                            $("#orgTree").tree("uncheck", $("#orgTree").tree("find", "allDepart").target)
                        }
                    }
                }
            }
        });
        //删除已选
        $(document).on("click",".role a i",function(){
            var id=$(this).parent().attr("id");
            $(this).parent().remove();
            var nodei=$("#orgTree").tree("find",id);
            if(nodei) $("#orgTree").tree("uncheck",nodei.target);
        });
    });
    //数据加载成功
    function treeLoadSuccess(){
        if(gps.feedBack == "1") {
            var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
            for(var i in chooseRow){
                if ($(".role a#" + chooseRow[i].id).length == 0) $(".role").append("<a displayName='" + chooseRow[i].displayName + "' id='" + chooseRow[i].orgCode + "'"+(gps.type=="duty"?(" companyCode="+chooseRow[i].belongCompanyCode):"")+"><font class='over_point fontStyle'>" + chooseRow[i].text + "</font><i class='iconfont fr'>&#xe6ef;</i></a>");
                var nodei=$("#orgTree").tree("find",chooseRow[i].orgCode);
                if(nodei) {
                    $("#orgTree").tree("check", nodei.target);
                }
            }
        }
    };
    window.getchoosedata=function(){
        var datas=[];
        $(".role a").each(function(i,v){
            var data={};
            data.orgCode=$(v).attr("id");
            data.displayName=$(v).attr("displayName");
            data.text=$(v).children("font").html();
            if (gps.type=="duty") {
                data.belongCompanyCode=$(v).attr("companyCode");
            }
            datas.push(data);
        });
        return {"data":datas,"state":1,"type":gps.type};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
    // 验证是不是每一项都是符合要求的  相当于arr.every
    function myEvery(data, key) {
        var validate = true
        for (var i in data) {
            if(data[i].id == "allDepart" || data[i].id == "allCom") {
                continue
            }
            if(!data[i][key]) {
                validate = false
                break
            }
        }
        return validate
    }
</script>
</body>
</html>
