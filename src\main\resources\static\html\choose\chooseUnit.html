<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <link href="../../fonts/iconfont/iconfont.css?v=$svn.revision"
          th:href="@{/fonts/iconfont/iconfont.css?v=$svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}"
          rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
          th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/kindeditor/kindeditor.min.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/kindeditor.min.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}"
            type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
            th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}"
            type="text/javascript"></script>
    <style type="text/css">
        .arrow {
            width: 100px;
            height: 100%;
            position: absolute;
            top: 0;
            right: 230px;
            background-color: #fff;
            z-index: 1000;
        }
        .arrowTop,
        .arrowBottom {
            width: 100%;
            height: 50%;
            display: flex;
            align-content: center;
            justify-content: center;
            flex-wrap: wrap;
        }

        .toadd{
            width: 80%;
            height: 2rem;
            background-color: #00b4f1;
            color: white;
            text-align: center;
            line-height: 2rem;
            font-size: 16px;
            margin-bottom: 2rem;
            cursor: pointer;
        }

        .ztree li ul.line{height:auto;}
        .cooperationContent,
        .copyContent {
            height: 86%;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-top: 2px solid #00b4f1;
            border-radius: 3px;
            padding: 5px;
        }
        .right {
            height: 100%;
            width: 230px;
            position: fixed;
            right: 0;
            top: 0;
            background-color: white;
        }
        .right_top,.right_bottom{
            width: 100%;
            height: 50%;
        }
        .right_title {
            font-size: 14px;
            text-align: center;
        }
        #cooperation,
        #copy {
            width: 100%;
            height: 100%;
            overflow-y: auto;
            border: 0;
        }
    </style>
</head>
<body class="page_body">
<ul id="orgTree"></ul>
<!--<div class="role orgC"></div>-->
<div class="arrow">
    <div class="arrowTop">
        <div class="toadd" onclick="addData('cooperation')">
            增加  >>
        </div>
        <div class="toadd" onclick="delData('cooperation')">
            删除  <<
        </div>
    </div>
    <div class="arrowBottom">
        <div class="toadd" onclick="addData('copy')">
            增加  >>
        </div>
        <div class="toadd" onclick="delData('copy')">
            删除  <<
        </div>
    </div>
</div>
<div class="right">
    <div class="right_top">
        <div class="right_title">主送-已选单位</div>
        <div class="cooperationContent">
            <select id="cooperation" multiple="multiple"></select>
        </div>
    </div>
    <div class="right_bottom">
        <div class="right_title">抄送-已选单位</div>
        <div class="copyContent">
            <select id="copy" multiple="multiple"></select>
        </div>
    </div>
</div>
<script type="text/javascript">
    var gps = getQueryString();
    var checkedNode = {}
    $(function () {
        treeLoadSuccess2()
        $("#orgTree").tree({
            url: web.rootdir + "uums/sys/org/findPOrgAndCityOrg?appcode=" + web.appCode,
            checkbox: false,//是否在每一个借点之前都显示复选框
            lines: true,//是否显示树控件上的虚线
            treeId: 'orgCode',
            treePid: 'parentOrgCode',
            cascadeCheck: false,
            onlyone: gps.multi == 0 ? true : false,//不要乱配
            fileds: 'orgCode|id,orgName|text,parentOrgCode,displayName,belongCompanyCode,treeType',
            animate: true,//节点在展开或折叠的时候是否显示动画效果
            onLoadSuccess: function (node, data) {
                treeLoadSuccess2();
            },
            onClick: function (node) {
                // if (node.children) {
                //     if (node.children.length == 0) top.mesAlert("提示信息", "该组织无下级组织！", 'info');
                // } else {
                //     ajaxgeneral({
                //         url: "uums/sys/org/findSonByParentOrgId?appcode=" + web.appCode + "&orgCode=" + node.id,
                //         data: {"appCode": web.appCode, "orgCode": node.id},
                //         contentType: "application/json; charset=utf-8",
                //         success: function (data) {
                //             if (data.data.length == 0) {
                //                 top.mesShow("温馨提示", "该组织无下级数据！", 2000);
                //             } else {
                //                 for (var i in data.data) {
                //                     data.data[i].text = data.data[i].orgName;
                //                     data.data[i].id = data.data[i].orgCode;
                //                     data.data[i].displayName = data.data[i].displayName;
                //                 }
                //                 $("#orgTree").tree("append", {
                //                     parent: node.target,
                //                     data: data.data
                //                 });
                //                 treeLoadSuccess2();
                //             }
                //         }
                //     });
                // }
            },
            // onBeforeCheck: function (node, checked, a) {
            //     if (gps.multi == 0) {
            //         var nodes = $("#orgTree").tree("getChecked");
            //         for (var i in nodes) {
            //             //var nodei=$("#orgTree").tree("find",nodes[i].id);
            //             $("#orgTree").tree("uncheck", nodes[i].target);
            //         }
            //         $(".role").html("");
            //     }
            // },
            // onCheck: function (node, checked) {
            //     if (checked) {
            //
            //         if ($(".role a#" + node.id).length == 0) $(".role").append("<a displayName='" + node.displayName + "' id='" + node.orgCode + "'" + (gps.type == "duty" ? (" companyCode=" + node.belongCompanyCode) : "") + "><font>" + node.text + "</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            //     } else {
            //         $(".role a#" + node.orgCode).remove();
            //     }
            // },
            // 节点被选中前触发，返回 false 则取消选择动作。
            onBeforeSelect:function(node){
                if(node.treeType=="org") return false;
                checkedNode = node
            },
        });
        //删除已选
        $(document).on("click", ".role a i", function () {
            var id = $(this).parent().attr("id");
            $(this).parent().remove();
            var nodei = $("#orgTree").tree("find", id);
            if (nodei) $("#orgTree").tree("uncheck", nodei.target);
        });
    });

    //数据加载成功
    function treeLoadSuccess() {
        var chooseRow = top.chooseWeb[gps.name] ? top.chooseWeb[gps.name].data : [];
        for (var i in chooseRow) {
            if ($(".role a#" + chooseRow[i].id).length == 0) $(".role").append("<a displayName='" + chooseRow[i].displayName + "' id='" + chooseRow[i].id + "'" + (gps.type == "duty" ? (" companyCode=" + chooseRow[i].belongCompanyCode) : "") + "><font>" + chooseRow[i].text + "</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            var nodei = $("#orgTree").tree("find", chooseRow[i].id);
            if (nodei) {
                $("#orgTree").tree("check", nodei.target);
            }
        }
    };

    //添加主办或抄送
    function addData(domId) {
        if(checkedNode.id) {
            var isIncludes = true
            $("#"+domId+" option").each(function (i, v) {
                if($(v).val() == checkedNode.id) {
                    isIncludes = false
                }
            })
            if(isIncludes) {
                let addlengths =  $("#"+domId).children()
                if (domId == 'cooperation'){
                    if (addlengths.length == 0){
                        $("#"+domId).append("<option value='"+checkedNode.orgCode+"'>"+checkedNode.orgName+"</option>")
                    }else {
                        top.mesShow("温馨提示", "主办单位只能添加一个！", 2000);
                    }
                }else {
                    $("#"+domId).append("<option value='"+checkedNode.orgCode+"'>"+checkedNode.orgName+"</option>")
                }


            }
        }
    }

    // 删除主办或抄送
    function delData(domId) {
        var checkedVal = $("#"+domId).val()
        for (var i in checkedVal) {
            $("#"+domId+" option").each(function (j, v) {
                if($(v).val() == checkedVal[i]) {
                    $(v).remove()
                }
            })
        }
    }

    //数据加载成功
    function treeLoadSuccess2(){
        if(top.coopercooperAndCopyUsers) {
            var cooper = top.cooperAndCopyUsers.cooperationData
            var copy = top.cooperAndCopyUsers.copyData
            if(cooper) {
                var htmlCooper = ""
                for(var i in cooper) {
                    htmlCooper += "<option value='"+cooper[i].id+"'>"+cooper[i].name+"</option>"
                }
                $("#cooperation").html(htmlCooper)
            }
            if(copy) {
                var htmlCopy = ""
                for(var i in copy) {
                    htmlCopy += "<option value='"+copy[i].id+"'>"+copy[i].name+"</option>"
                }
                $("#copy").html(htmlCopy)
            }
        }
    };

    // window.getchoosedata = function () {
    //     var datas = [];
    //     $(".role a").each(function (i, v) {
    //         var data = {};
    //         data.orgCode = $(v).attr("id");
    //         data.displayName = $(v).attr("displayName");
    //         data.text = $(v).children("font").html();
    //         if (gps.type == "duty") {
    //             data.belongCompanyCode = $(v).attr("companyCode");
    //         }
    //         datas.push(data);
    //     });
    //     return {"data": datas, "state": 1, "type": gps.type};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    // };

    window.getchoosedata=function(){
        var datas=[];
        $("#cooperation option").each(function(i,v){
            datas.push({
                id: $(v).val(),
                name: $(v).text()
            })
        });
        var copyDatas = []
        $("#copy option").each(function(i,v){
            copyDatas.push({
                id: $(v).val(),
                name: $(v).text()
            });
        });
        return {"data": {cooperationData: datas, copyData: copyDatas},"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
    };
</script>
</body>
</html>
