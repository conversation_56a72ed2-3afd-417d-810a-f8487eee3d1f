package com.simbest.boot.yjtxc.util;

import com.google.common.collect.Maps;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.wf.login.WorkFlowBpsLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <strong>Title :BpsLoginUtil </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2022/7/7</strong><br>
 * <strong>Modify on : 2022/7/7</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Component
public class BpsLoginUtil {

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Autowired
    private WorkFlowBpsLoginService workFlowBpsLoginService;

    public void bpsLogin(String username) {
        boolean bpsTenant = Boolean.valueOf(bpsConfig.bpsTenant);
        String bpsTenantId = bpsConfig.bpsTenantId;
        Map<String,Object> map = Maps.newConcurrentMap();
        map.put("tenant", bpsTenant );
        map.put("userName",  username );
        map.put("tenantId", bpsTenantId );
        workFlowBpsLoginService.bpsLogin( map );
    }

}
