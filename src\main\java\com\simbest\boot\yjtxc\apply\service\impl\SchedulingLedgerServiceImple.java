package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.model.UavAerialConfigurationStation;
import com.simbest.boot.yjtxc.apply.repository.SchedulingLedgerRepository;
import com.simbest.boot.yjtxc.apply.service.IEmergencyEquipmentService;
import com.simbest.boot.yjtxc.apply.service.ISchedulingLedgerService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:51
 * @describe 应急车调度台账
 */
@Slf4j
@Service
public class SchedulingLedgerServiceImple extends LogicService<SchedulingLedger, String> implements ISchedulingLedgerService {

    private SchedulingLedgerRepository schedulingLedgerRepository;

    public SchedulingLedgerServiceImple(SchedulingLedgerRepository schedulingLedgerRepository) {
        super(schedulingLedgerRepository);
        this.schedulingLedgerRepository = schedulingLedgerRepository;
    }

    @Autowired
    private IEmergencyEquipmentService iEmergencyEquipmentService;


    @Autowired
    private ISysOperateLogService sysOperateLogService;

    final String param1 = "action/schedulingLedger/";

    /**
     * 保存台账
     *
     * @param applicationForm
     * @return

    @Override
    public Boolean saveParameter(ApplicationForm applicationForm) {

        Boolean flag = false;
        try {
            List<SchedulingLedger> schedulingLedgerList = CollectionUtil.newLinkedList();
            //设备开始使用时间
            String startTime = applicationForm.getStartTime();
            //设备结束使用时间
            String endTime = applicationForm.getEndTime();
            //算出时差
            int betweenDay = (int) ((DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.DAY)) * 24);
            String processType = applicationForm.getProcessType();
            //获取保障地市
            String applicantUnit = applicationForm.getApplicantUnit().replaceAll("分公司", "");
            if (Constants.PROCESS_TYPE_C.equals(processType)) {
                applicantUnit = applicationForm.getSpecificCity();
            }
            List<EmergencyEquipment> emergencyEquipmentList = iEmergencyEquipmentService.findByPmInsId(applicationForm.getPmInsId());
            Iterator<EmergencyEquipment> iterator = emergencyEquipmentList.iterator();
            while (iterator.hasNext()) {
                EmergencyEquipment emergencyEquipment = iterator.next();
                //获取车牌号
                String licensePlate = emergencyEquipment.getLicensePlate();
                //获取设备厂家
                String equipmentManufacturer = emergencyEquipment.getEquipmentManufacturer();
                //应急车配置类型
                String carConfiguRation = emergencyEquipment.getCarConfiguRation();
                //车辆归属地市
                String cities = emergencyEquipment.getCities();//.replaceAll("分公司", "");
                //new台账类
                SchedulingLedger schedulingLedger = new SchedulingLedger();
                if (Constants.WURENJI.equals(licensePlate) || Constants.WEIXING.equals(licensePlate)) {
                    //根据无人机或者卫星和地市查找信息
                    schedulingLedger = schedulingLedgerRepository.findByLicensePlateAndCities(licensePlate, cities);
                } else {
                    schedulingLedger = schedulingLedgerRepository.findByLicensePlate(licensePlate);
                }

                //如果不为空说明车辆被使用过，就更新出车次数、出车时长、出车地市，否则新增台账数据
                if (ObjectUtil.isNotEmpty(schedulingLedger)) {
                    //获取出车地市
                    String carCities = schedulingLedger.getCarCities();
                    //获取出车次数
                    int carNumber = schedulingLedger.getCarNumber();
                    //获取出车时长
                    int carTime = schedulingLedger.getCarTime();
                    //增加出车地市
                    if (!carCities.contains(applicantUnit)) {
                        carCities = carCities + applicantUnit;
                    }
                    //增加出车时长
                    carTime = carTime + betweenDay;
                    //增加出车次数
                    carNumber = Integer.valueOf(carNumber + 1);
                    schedulingLedger.setCarCities(carCities);
                    schedulingLedger.setCarNumber(carNumber);
                    schedulingLedger.setCarTime(carTime);
                    update(schedulingLedger);
                } else {
                    SchedulingLedger schedulingLedger1 = new SchedulingLedger();
                    //设置保障地市
                    schedulingLedger1.setCarCities(applicantUnit);
                    //出车次数
                    schedulingLedger1.setCarNumber(1);
                    //出车累计时长
                    schedulingLedger1.setCarTime(betweenDay);
                    //使用车辆归属地
                    schedulingLedger1.setPlaceOwnership(cities);
                    //厂家
                    schedulingLedger1.setEquipmentManufacturer(equipmentManufacturer);
                    //车牌号
                    schedulingLedger1.setLicenseNumber(licensePlate);
                    //应急车配置
                    schedulingLedger1.setVehicleConfiguration(carConfiguRation);
                    insert(schedulingLedger1);
                    //schedulingLedgerList.add(schedulingLedger1);
                }
            }
            //saveAll(schedulingLedgerList);
            flag = true;
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return flag;
    }*/

    /**
     * 条件查询应急车调度台账
     *
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return

    @Override
    public JsonResponse findBySchedulingLedger(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap) {

        Page<SchedulingLedger> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");
        try {
            //获取地市
            String placeOwnership = cn.hutool.core.map.MapUtil.getStr(paramMap, "placeOwnership");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //应急车配置类型
            String vehicleConfiguration = MapUtil.getStr(paramMap, "vehicleConfiguration");
            //车牌号
            String licenseNumber = MapUtil.getStr(paramMap, "licenseNumber");
            //开始使用时间
            String startTime = MapUtil.getStr(paramMap, "startTime");
            //结束使用时间
            String endTime = MapUtil.getStr(paramMap, "endTime");
            Specification<SchedulingLedger> specification = (root, query, criteriaBuilder) -> {
                List<javax.persistence.criteria.Predicate> predicateList = Lists.newArrayList();

                if (true) {
                    predicateList.add(criteriaBuilder.equal(root.get("enabled"), true));
                }
                if (StringUtils.isNotEmpty(placeOwnership)) {
                    predicateList.add(criteriaBuilder.equal(root.get("placeOwnership"), placeOwnership));
                }
                if (StringUtils.isNotEmpty(equipmentManufacturer)) {
                    predicateList.add(criteriaBuilder.equal(root.get("equipmentManufacturer"), equipmentManufacturer));
                }
                if (StringUtils.isNotEmpty(vehicleConfiguration)) {
                    predicateList.add(criteriaBuilder.equal(root.get("vehicleConfiguration"), vehicleConfiguration));
                }
                if (StringUtils.isNotEmpty(licenseNumber)) {
                    predicateList.add(criteriaBuilder.like(root.get("licenseNumber"), "%" + licenseNumber + "%"));
                }
//                if (StrUtil.isNotEmpty(startTime)) {
//                    predicateList.add(criteriaBuilder.greaterThanOrEqualTo(root.get("startTime").as(String.class), startTime + " " + "00:00:00"));
//                }
//                if (StrUtil.isNotEmpty(endTime)) {
//                    predicateList.add(criteriaBuilder.lessThanOrEqualTo(root.get("endTime").as(String.class), endTime + " " + "23:59:59"));
//                }
                javax.persistence.criteria.Predicate[] predicates = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicates));
            };
            findAllGarageConfiguration = schedulingLedgerRepository.findAll(specification, pageable);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);
    } */
}
