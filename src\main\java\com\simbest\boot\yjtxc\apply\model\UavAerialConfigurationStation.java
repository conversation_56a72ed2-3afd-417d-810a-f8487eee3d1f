package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 14:45
 * @describe 无人机高空配置库
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_uav_confi")
@ApiModel(value = "无人机高空配置库")
public class UavAerialConfigurationStation extends WfFormModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "uc") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    @ExcelVOAttribute(name = "地市", column = "A")
    private String cities;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    private String citiesCode;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    @ExcelVOAttribute(name = "设备厂家", column = "B")
    private String equipmentManufacturer;

    @Column(length = 100)
    @ExcelVOAttribute(name = "无人机高空站配置库编号", column = "C")
    @ApiModelProperty(value = "无人机高空站配置库编号")
    protected String licensePlate;

    @Column(length = 140)
    @ExcelVOAttribute(name = "无人机高空站类型", column = "F")
    @ApiModelProperty(value = "无人机高空站类型")
    private String carConfiguRation;

    @Column(length = 140)
    @ExcelVOAttribute(name = "无人机高空站配置父类", column = "H")
    @ApiModelProperty(value = "无人机高空站配置父类")
    private String carConfiguRationParent;

    @Column(length = 100)
    @ApiModelProperty(value = "无人机高空基站负责人")
    @ExcelVOAttribute(name = "无人机高空基站负责人", column = "D")
    private String uavStationLeader;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人手机号")
    @ExcelVOAttribute(name = "负责人手机号", column = "E")
    private String uavStationLeaderPhone;

    @Column(length = 40)
    @ApiModelProperty(value = "调度状态")
    private String schedulingCondition;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;

}
