<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#processTable",
                "querycmd":"action/involvesPersonnel/findByAllInvolvesPersonnelConfiguration?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "fitColumns": false,
                "frozenColumns":[],
                "columns":[
                    [
                        { title: "地市公司", field: "appCities", width: 150, rowspan: 2, align: "center"},
                        { title: "应急车申请人", width: 150,tooltip:true,colspan: 6, align: "center" },
                        { title: "网络部经理", width: 150,tooltip:true,colspan: 3, align: "center" },
                        { title: "应急车负责人（开站人）", width: 150,tooltip:true,colspan: 6, align: "center" },
                       /* { title: "网络部经理", width: 150,tooltip:true,colspan: 3, align: "center" },*/
                        { title: "应急配置类型", field: "emerConfiType", width: 150,tooltip:true, rowspan: 2, align: "center" },
                        { title: "对应车牌号", field: "correspondsNumber", width: 150,tooltip:true, rowspan: 2, align: "center" },
                        { title: "设备厂家", field: "equipmentManu", width: 150,tooltip:true, rowspan: 2, align: "center" },
                        { title: "操作", field: "opt", width: 150, rowspan: 2, align: "center",
                            formatter: function(value, row, index) {
                                return "<a href='#' id='"+row.id+"' onclick='deleteList(this)'>删除</a>"
                                // return "<a href='#' id='"+row.id+"' onclick='editList(this)'>修改</a>" +" "+ "<a href='#' id='"+row.id+"' onclick='deleteList(this)'>删除</a>"
                            }
                        }
                    ],
                    [
                        { title: "姓名（A角）", field: "appUsernameA", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "appphoneNumA", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "appLinkEmailA", width: 150,tooltip:true, align: "center" },
                        { title: "姓名（B角）", field: "appTruenameB", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "appPhoneNumB", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "appLinkEmailB", width: 150,tooltip:true, align: "center" },
                        { title: "姓名", field: "appTruenameVehicle", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "appPhoneVehicle", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "appLinkEmailVehicle", width: 150,tooltip:true, align: "center" },
                        { title: "姓名（A角）", field: "headTruenameA", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "headPhoneNumA", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "headLinkEmailA", width: 150,tooltip:true, align: "center" },
                        { title: "姓名（B角）", field: "headTruenameB", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "headPhoneNumB", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "headLinkEmailB", width: 150,tooltip:true, align: "center" }
                      /*  { title: "姓名", field: "headTruenameVehicle", width: 100,tooltip:true, align: "center" },
                        { title: "电话", field: "headPhoneNumVehicle", width: 150,tooltip:true, align: "center" },
                        { title: "邮件", field: "headLinkEmailVehicle", width: 150,tooltip:true, align: "center" }*/
                    ]
                ]
            }
        };

        $(function(){
            loadGrid(pageparam);
            $(".import").click(function() {
                top.dialogP("html/sysManagement/import.html?type=process", window.name, "模板导入", "importCB", false, "maximized", "maximized")
            })
            $(".downLoad").click(function () {
                $("#downloadForm").attr("action", web.rootdir + "action/involvesPersonnel/downloadTemplate");
                $("#downloadForm .downloadSubmit").trigger("click");
            })
            $(".resetForm").click(function() {
                formreset("processTableQueryForm")
                $(".searchtable").trigger("click")
            })
        });
        function editList(param) {
            top.dialogP("html/sysManagement/processAddForm.html?id="+$(param).attr("id"), window.name, "修改", "addCB", false, 1000, 500)
        }
        function addCB(data) {
            var param = data.data
            ajaxgeneral({
                url: "action/involvesPersonnel/edit",
                data: param,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#processTable").datagrid("reload")
                }
            })
        }
        function importCB(data) {
            ajaxgeneral({
                url: "action/involvesPersonnel/saveInvolvesPersonnelConfiguration",
                data: data.data,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#processTable").datagrid("reload")
                }
            })
        }
        function deleteList(param) {
            $.messager.confirm("温馨提示!", "确认删除吗？", function(r) {
                if(r) {
                    ajaxgeneral({
                        url: "action/involvesPersonnel/deleteById?id="+$(param).attr("id"),
                        success: function(data) {
                            $("#processTable").datagrid("reload")
                        }
                    })
                }
            })
        }
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="processTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">地市公司：</td>
            <td width="150">
                <input name="cities" type="text" value="" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                    <a class="btn fl ml15 a_success resetForm"><font>重置</font></a>
                </div>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fr a_warning ml15 import"><font>模板导入</font></a>
                    <a class="btn fr a_success downLoad"><font>模板下载</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="processTable"><table id="processTable"></table></div>
<form id="downloadForm" class="hide" method="post">
    <input type="submit" class="downloadSubmit"/>
</form>
</body>
</html>
