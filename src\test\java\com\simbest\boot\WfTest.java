package com.simbest.boot;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public class WfTest {
    @Autowired
    private static IProcessInstanceService processInstanceService;



    /*public static void main(String[] args) {
        List<Map<String, Object>> list = CollectionUtil.newArrayList();
        Map<String, Object> map = Maps.newHashMap();
        map.put("name", "张三");
        map.put("age", 10);
        map.put("address", "郑州");
        map.put("id", 1);
        Map<String, Object> map1 = Maps.newHashMap();
        map1.put("name", "李四");
        map1.put("age", 20);
        map1.put("address", "北京");
        map1.put("id", 2);
        list.add(map);
        list.add(map1);
        map1 = Maps.newHashMap();
        map1.put("name", "李四");
        map1.put("age", 20);
        map1.put("address", "北京");
        map1.put("id", 3);
        list.add(map1);
        List<Map<String,Object>> tmpList = list.stream().filter(map2 -> StrUtil.equals(MapUtil.getStr(map2,"name"),"张三")).collect(Collectors.toList());
        List lis = Lists.newArrayList();
        list.stream().forEach(map2 -> {
            if(StrUtil.equals(MapUtil.getStr(map2,"name"),"李四")){
                lis.add(map2);
            }
        });

        List<Map<String, Object>> collect = list.stream().filter(map2 -> StrUtil.equals(MapUtil.getStr(map2, "age"), "20")).collect(Collectors.toList());
        System.out.println(collect);

        List<Map<String,Object>> mapList =list.stream().sorted(Comparator.comparing(map2 -> MapUtil.getInt(map2,"age"))).collect(Collectors.toList());

        List<Map<String, Object>> mapId = list.stream().sorted(Comparator.comparing(map0->MapUtil.getInt(map0,"id"))).collect(Collectors.toList());
        System.out.println("mapId:"+mapId);

        Map<String,List<Map<String,Object>>> mapList1 = list.stream().collect(Collectors.groupingBy(map2 -> MapUtil.getStr(map2,"name")));
        Map<String, List<Map<String, Object>>> name = list.stream().collect(Collectors.groupingBy(map2 -> MapUtil.getStr(map2, "name")));
        System.out.println("name:"+name);
//        System.out.println(tmpList);
//        System.out.println(lis);
//        System.out.println(mapList);
//        System.out.println(list.stream().distinct().collect(Collectors.toList()));
//        System.out.println(mapList1);

        System.out.println(list.stream().map(map2 -> MapUtil.getStr(map2,"name")).collect(Collectors.joining(",")));
    }*/


    public static void main(String[] args) {
        //String currentTimestamp = DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss");
        //String endTime = "2023-11-15 14:56";
        //String nowTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");
        //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        //LocalDateTime endTimeLocal = LocalDateTime.parse(endTime, formatter);
        //LocalDateTime nowTimeLocal = LocalDateTime.parse(nowTime, formatter);
        //if (endTimeLocal.isAfter(nowTimeLocal)) {
        //    System.out.println("12313");
        //}
        String s1 = "2023-11-01";
        System.out.println(s1.length());
        if(s1.length() == 10){
            System.out.println("12313");
        }

    }


}
