package com.simbest.boot.yjtxc.attachment.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.beans.Transient;
import java.util.List;

@Repository
public interface FileExtendRepository  extends LogicRepository<SysFile,String> {

    @Transient
    @Modifying
    @Query(
            value = "update  SYS_FILE t set t.pm_ins_id =:pmInsId,t.enabled=1,t.removed_time=null where t.id =:id",
            nativeQuery = true
    )
    int updatePmInsId ( @Param ( "pmInsId" ) String pmInsId, @Param ( "id" ) String id );

    @Modifying
    @Query(value = "update sys_file t set t.enabled=0,t.removed_time=SYSDATE where t.pm_ins_id =:pmInsId",nativeQuery = true)
    int deleteByPmInsId(@Param("pmInsId") String pmInsId);

    @Transient
    @Query(
            value = "select * from SYS_FILE t where t.pm_ins_id =:pmInsId and t.removed_time is null and t.pm_ins_type_part = :typePart",
            nativeQuery = true
    )
    List<SysFile> getPartFile ( @Param ( "pmInsId" ) String pmInsId, @Param ( "typePart" ) String typePart );

    @Transient
    @Query(
            value = "select * from SYS_FILE t where t.id in :id and t.removed_time is null",
            nativeQuery = true
    )
    List<SysFile> getFileById(@Param("id") String[] id);

    @Transient
    @Modifying
    @Query(
            value = "update  SYS_FILE t set t.pm_ins_id =:pmInsId,t.enabled=1,t.removed_time=null,t.pm_ins_type_part=2 where t.id =:id",
            nativeQuery = true
    )
    void updatePmInsIdTwo(@Param ("pmInsId") String pmInsId, @Param ("id") String id );


    @Transient
    @Query(
            value = "select * from SYS_FILE t where t.pm_ins_id  = ?   and  t.enabled = '1' ",
            nativeQuery = true
    )
    List<SysFile> finListSysFile(String formId);
}
