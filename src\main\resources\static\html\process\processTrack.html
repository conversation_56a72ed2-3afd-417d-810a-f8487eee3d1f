﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>流程跟踪</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">

        function loadProcessTrack(){
            var h = $(window).height();
            $(".trackContainer").height(h-20);
            // 流程跟踪
            var urld={"processInstId":gps.processInstId};
            var pageparam={
                "listtable":{
                    "listname":"#trackTable",//table列表的id名称，需加#
                    "querycmd":"action/queryProcessHistory/getWorkItems",//table列表的查询命令+(gps.from?"/sso":"")
                    //"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
                    "nowrap": true,//把数据显示在一行里,默认true
                    "queryParams":urld,
                    "styleClass":"noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
                    "pagination":false,
                    "dataName":"approval",
                    "frozenColumns":[],//固定在左侧的列
                    "columns":[[//列
                        { title: "环节名称", field: "activityInstName", width: 120 ,tooltip:true},
                        { title: "工作项审批人", field: "workItemApproverUser", width: 220,tooltip:true },
                        { title: "提交时间", field: "endTime", width: 100 },//排序sortable: true,
                        { title: "后续环节", field: "nextactivity", width: 180 ,tooltip:true}//排序sortable: true
                    ] ],
                    "rowStyler":function(index,row){
                        if(row.currentState=="12") return 'rowGreen';
                        if(row.currentState=="10") return 'rowRed';
                    },
                    "onLoadSuccess":function(data){
                        if(data.data.copy && data.data.copy.length>0){
                            $(".copyTrackTable,.copyT").show();
                            var copyparam={
                                "listtable":{
                                    "listname":"#copyTrackTable",//table列表的id名称，需加#
                                    "nowrap": true,//把数据显示在一行里,默认true
                                    "data":data,
                                    "styleClass":"noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
                                    "pagination":false,
                                    "dataName":"copy",
                                    "frozenColumns":[],//固定在左侧的列
                                    "columns":[[//列
                                        { title: "抄送环节名称", field: "workItemName", width: 120 ,tooltip:true},
                                        { title: "发送人", field: "sendUserName", width: 220 },
                                        { title: "抄送时间", field: "modifiedTime", width: 100 },//排序sortable: true,
                                        { title: "抄送人", field: "recipientName", width: 180 }//排序sortable: true
                                    ] ],
                                    "rowStyler":function(index,row){
                                        if(row.status=="1") return 'rowGreen';
                                        if(row.status=="0") return 'rowRed';
                                    }
                                }
                            };
                            loadGrid(copyparam);
                        }
                        if(data.data.checkup && data.data.checkup.length>0){
                            $(".checkupTrackTable,.checkupT").show();
                            top.dialogSetTitle("processTrack",$(".checkupT").text());
                            $(".checkupT b").text("流程跟踪");
                            var checkupparam={
                                "listtable":{
                                    "listname":"#checkupTrackTable ",//table列表的id名称，需加#
                                    "nowrap": true,//把数据显示在一行里,默认true
                                    "data":data,
                                    "styleClass":"noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
                                    "pagination":false,
                                    "dataName":"checkup",
                                    "frozenColumns":[],//固定在左侧的列
                                    "columns":[[//列
                                        { title: "环节名称", field: "activityInstName", width: 140 ,tooltip:true},
                                        { title: "工作项审批人", field: "workItemApproverUser", width: 250,tooltip:true },
                                        { title: "提交时间", field: "endTime", width: 100 },//排序sortable: true,
                                        { title: "后续环节", field: "nextactivity", width: 150 ,tooltip:true}//排序sortable: true
                                    ] ],
                                    "rowStyler":function(index,row){
                                        if(row.currentState=="12") return 'rowGreen';
                                        if(row.currentState=="10") return 'rowRed';
                                    }
                                }
                            };
                            loadGrid(checkupparam);
                        }
                    }
                }
            };
            loadGrid(pageparam);

            // 流程图
            var href={"processinstid":gps.processInstId,"tenantId":web.appCode};
            var url='http://***********:8888/nbps/processGraphic.jsp';
            if(window.location.href.indexOf("************")>-1 || window.location.href.indexOf("************")>-1 || window.location.href.indexOf("************")>-1){
                url='http://************:8088/nbps/processGraphic.jsp';
            }
            url=tourl(url,href);//正式:http://************:8088/nbps/processGraphic.jsp  测试:http://***********:8888/nbps/processGraphic.jsp
            $("#processImgF").attr("src",url);
            // 流程明细-视图切换
            $(document).on("click",".initArrow button",function(){
                var type = $(this).attr("operate");
                if(type=="left"){//显示左侧/左侧全屏
                    if($(".initRight").hasClass("fullWidth")){
                        $(".leftArrow").removeClass("noBorder");
                        $(".initRight").removeClass("fullWidth");
                        $(".initLeft").show();
                        $(".rightArrow").show();
                    }else{
                        $(".initLeft").addClass(" fullWidth");
                        $(".initRight").hide();
                        $(".leftArrow").hide();
                    }
                }else if(type=="right"){//显示右侧/右侧全屏
                    if($(".initLeft").hasClass("fullWidth")){
                        $(".initLeft").removeClass("fullWidth");
                        $(".initRight").show();
                        $(".leftArrow").show();
                    }else{
                        $(".leftArrow").addClass(" noBorder");
                        $(".initRight").addClass(" fullWidth");
                        $(".initLeft").hide();
                        $(".rightArrow").hide();
                    }
                }
                $("#trackTable").datagrid("resize");
            });
            // 全屛展示流程图
            $(document).on("click","#fullscreen",function(){
                $("<div class='fullProcessImg'><span onclick='cancelFullscreen()'>取消全屛</span><iframe id='processImgF' name='processImgF' src='"+url+"' frameborder='0' scrolling='auto'</iframe></div>").appendTo($("body",parent.document));
            });
        };

        $(function(){
            loadProcessTrack();
        });
    </script>
</head>
<body>
<!--table-->
<div class="trackTable"><table id="trackTable"></table></div>
<h6 class="hide copyT bor_b txtc p10 col_h"><b>抄送跟踪</b></h6>
<!--table-->
<div class="copyTrackTable hide"><table id="copyTrackTable"></table></div>
</body>
</html>
