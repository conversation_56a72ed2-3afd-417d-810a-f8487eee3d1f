package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 15:07
 * @describe 卫星便携站配置库
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_satellite_portable")
@ApiModel(value = "卫星便携站配置库")
public class SatellitePortableLibrary extends WfFormModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "sp") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    @ExcelVOAttribute(name = "地市", column = "A")
    private String cities;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    private String citiesCode;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    @ExcelVOAttribute(name = "设备厂家", column = "B")
    private String equipmentManufacturer;


    @Column(length = 100)
    @ExcelVOAttribute(name = "卫星便携站编号", column = "C")
    @ApiModelProperty(value = "卫星便携站编号")
    protected String licensePlate;


    @Column(length = 100)
    @ApiModelProperty(value = "卫星编写站负责人")
    @ExcelVOAttribute(name = "卫星编写站负责人", column = "D")
    private String chiefWritingStation;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人手机号")
    @ExcelVOAttribute(name = "负责人手机号", column = "E")
    private String uavStationLeaderPhone;

    @Column(length = 40)
    @ApiModelProperty(value = "调度状态")
    private String schedulingCondition;


    @Column(length = 140)
    @ApiModelProperty(value = "卫星便携站类型")
    @ExcelVOAttribute(name = "卫星便携站类型", column = "F")
    private String carConfiguRation;


    @Column(length = 140)
    @ApiModelProperty(value = "卫星便携站类型父类")
    @ExcelVOAttribute(name = "卫星便携站类型父类", column = "G")
    private String carConfiguRationParent;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;



}
