package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FiveBaseStation;

import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:04
 * @describe 5G基站站号
 */
public interface IFiveBaseStationService extends ILogicService<FiveBaseStation, String> {

    /**
     *
     * @param pmInsId
     */
    void deleteByFiveBaseStationPmInsId(String pmInsId);

    /**
     * 维护5G基站号
     * @param fiveBaseStation
     * @return
     */
    JsonResponse updateFiveBaseStation(FiveBaseStation fiveBaseStation);

    /**
     * 查询5G基站号
     * @param pmInsId
     * @return
     */
    JsonResponse findByPmInsIdFiveBaseStation(String pmInsId);

    /**
     * 批量添加5G基站号
     * @param fiveBaseStationList
     * @return
     */
    JsonResponse saveFiveBaseStation(List<FiveBaseStation> fiveBaseStationList,String pmInsId);
}
