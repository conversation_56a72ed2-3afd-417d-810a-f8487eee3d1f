package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.model.UsageofVehicle;
import com.simbest.boot.yjtxc.apply.service.IUsageofVehicleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Map;

/**
 * 车辆使用情况
 */

@Api(description = "应急车调度台账")
@Slf4j
@RestController
@RequestMapping(value = "/action/usageofVehicle")
public class UsageofVehicleController extends LogicController<UsageofVehicle, String> {

       private IUsageofVehicleService  usageofVehicleService ;

    public UsageofVehicleController(IUsageofVehicleService  usageofVehicleService) {

        super(usageofVehicleService);
        this.usageofVehicleService = usageofVehicleService;
    }



    /**
     * 车辆使用情况查询
     *
     * @param page
     * @param size
     * @param params
     * @return
     */
    @ApiOperation(value = "车辆使用情况查询", notes = "车辆使用情况查询")
    @PostMapping(value = {"/findByParameterQuery", "/api/findByParameterQuery", "/findByParameterQuery/sso"})
    public JsonResponse findByParameterQuery(@RequestParam(required = false, defaultValue = "1") int page,
                                             @RequestParam(required = false, defaultValue = "10") int size,
                                             @RequestBody Map<String, Object> params) {
        return usageofVehicleService.findByParameterQuery(page, size, params);
    }

    /**
     * 导出
     *
     * @param response
     * @param request
     * @param emergencyEquipment
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "信息导出", notes = "信息导出工")
    @PostMapping(value = {"/exportOrder", "/api/exportOrder", "/exportOrder/sso"})

    public JsonResponse exportOrder(HttpServletResponse response, HttpServletRequest request, EmergencyEquipment emergencyEquipment) throws ParseException {
        usageofVehicleService.exportOrder(request, response, emergencyEquipment);
        return JsonResponse.defaultSuccessResponse();
    }
}
