
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>导入操作</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <!--    <link href="${ctx}../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet"/>-->
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <style>

    </style>
    <script type="text/javascript">
        var gps = getQueryString()
        var carCols = [
            { title: "地市", field: "cities", width: 100, align: "center"},
            { title: "应急设备分类", field: "carConfiguRationParent", width: 100, tooltip:true, align: "center" },
            { title: "应急设备子类", field: "carConfiguRation", width: 100, tooltip:true, align: "center" },
            { title: "设备厂家", field: "equipmentManufacturer", width: 100, tooltip:true, align: "center" },
            { title: "应急设备车牌号", field: "licensePlate", width: 100, tooltip:true, align: "center" },
            { title: "司机", field: "theDriver", width: 100, tooltip:true, align: "center" },
            { title: "司机手机号", field: "theDriverPhone", width: 100, tooltip:true, align: "center" },
            { title: "负责开站人员", field: "openingStation", width: 100, tooltip:true, align: "center" },
            { title: "开站人员手机号", field: "openingStationPhone", width: 100, tooltip:true, align: "center" },
            { title: "调度状态", field: "schedulingCondition", width: 100, tooltip:true, align: "center" }
        ]
        var carCols1 = [
            { title: "地市", field: "cities", width: 150, align: "center"},
            { title: "应急设备分类", field: "carConfiguRationParent", width: 150, align: "center" },
            { title: "无人机高空基站类型", field: "carConfiguRation", width: 150, align: "center" },
            { title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center"},
            { title: "车牌号", field: "licensePlate", width: 150, align: "center"},
            { title: "无人机高空基站负责人", field: "uavStationLeader", width: 150, align: "center"},
            { title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center"},
            { title: "调度状态", field: "schedulingCondition", width: 150, align: "center"}

        ]
        var carCols2 = [
            { title: "地市", field: "cities", width: 150, align: "center"},
            { title: "应急设备分类", field: "carConfiguRationParent", width: 150, align: "center" },
            { title: "无人机高空基站类型", field: "carConfiguRation", width: 150, align: "center" },
            { title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center"},
            { title: "车牌号", field: "licensePlate", width: 150, align: "center"},
            { title: "卫星便携站负责人", field: "chiefWritingStation", width: 150, align: "center"},
            { title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center"},
            { title: "调度状态", field: "schedulingCondition", width: 150, align: "center"}

        ]
        function uavCols(param) {
            return [
                { title: "地市", field: "cities", width: 150, align: "center"},
                { title: "应急设备分类", field: "carConfiguRationParent", width: 150, align: "center" },
                { title: "应急设备子类", field: "carConfiguRation", width: 150, align: "center" },
                { title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center"},
                { title: "车牌号", field: "licensePlate", width: 150, align: "center"},
                { title: param.title, field: param.field, width: 150, align: "center"},
                { title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center"},
                { title: "调度状态", field: "schedulingCondition", width: 150, align: "center"}

            ]
        }
        function initTable(carConfiguRation, carConfiguRationParent) {
            var requestData = {
                cities: gps.city,
                carType: gps.type,
                processType: gps.processType,
                startTime: gps.startTime,
                endTime: gps.endTime,
                pmInsId: gps.pmInsId
            };

            // 如果传入了carConfiguRation参数，则添加到请求数据中
            if (carConfiguRation) {
                requestData.carConfiguRation = carConfiguRation;
            }

            // 如果传入了carConfiguRationParent参数，则添加到请求数据中
            if (carConfiguRationParent) {
                requestData.carConfiguRationParent = carConfiguRationParent;
            }

            ajaxgeneral({
                url: "action/applicationForm/findAllCarTypes",
                contentType: "application/json; charset=utf-8",
                data: requestData,
                success: function(data) {
                    var cols = []
                    if(gps.type == "卫星车" || gps.type == "大型应急车") {
                        cols = carCols
                    } else if(gps.type == "无人机高空站") {
                        cols = carCols1
                        // cols = uavCols({title:"无人机高空基站负责人", field: "uavStationLeader"})
                    } else {
                        cols = carCols2
                        // cols = uavCols({title:"卫星便携站负责人", field: "chiefWritingStation"})
                    }
                    loadGrid({
                        "listtable":{
                            "listname":"#table",
                            "data":{data:{rows:data.data,total:data.data.length}},
                            "nowrap": true,
                            "styleClass":"noScroll",
                            "pagination": false,
                            "frozenColumns":[[{field: "ck", checkbox: true}]],
                            "columns": [cols],
                            "onLoadSuccess": function(param) {
                                var ids = gps.ids.split("/")
                                for(var i in ids) {
                                    for(var j in data.data) {
                                        if(ids[i] == data.data[j].id) {
                                            $("#table").datagrid("checkRow", j)
                                        }
                                    }
                                }
                                var isChooseAll = false
                                for(var i in param.rows) {
                                    if(!param.rows[i].isShow) {
                                        isChooseAll = true
                                        $(".datagrid-body td[field='ck']").eq(i).find("input")[0].disabled = true
                                    }
                                }
                                if(isChooseAll) {
                                    $(".datagrid-header td[field='ck']").find("input")[0].disabled = true
                                }
                            }
                        }
                    })
                }
            })
        }
        $(function () {
            // 检查是否是从应急设备管理传递过来的数据
            if (gps.dataSource === 'emergency' && parent.window.emergencyEquipmentData) {
                // 使用传递过来的数据直接加载表格
                loadTableWithData(parent.window.emergencyEquipmentData);
            } else {
                // 正常的接口调用
                initTable();
            }

            $('#table').datagrid({
                rowStyler: function(index,row){
                    if (!row.isShow){
                        return 'background-color: rgb(242,242,242);';
                    }
                }
            });

            // 查询按钮事件
            $('#searchBtn').click(function() {
                var carConfiguRation = $('#carConfiguRationSearch').textbox('getValue');
                var carConfiguRationParent = $('#carConfiguRationParentSearch').textbox('getValue');
                initTable(carConfiguRation, carConfiguRationParent);
            });

            // 重置按钮事件
            $('#resetBtn').click(function() {
                $('#carConfiguRationSearch').textbox('setValue', '');
                $('#carConfiguRationParentSearch').textbox('setValue', '');
                initTable();
            });

            // 应急设备分类输入框回车键查询
            $('#carConfiguRationParentSearch').textbox({
                inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents, {
                    keyup: function(e) {
                        if (e.keyCode == 13) { // 回车键
                            var carConfiguRation = $('#carConfiguRationSearch').textbox('getValue');
                            var carConfiguRationParent = $('#carConfiguRationParentSearch').textbox('getValue');
                            initTable(carConfiguRation, carConfiguRationParent);
                        }
                    }
                })
            });

            // 应急设备类型输入框回车键查询
            $('#carConfiguRationSearch').textbox({
                inputEvents: $.extend({}, $.fn.textbox.defaults.inputEvents, {
                    keyup: function(e) {
                        if (e.keyCode == 13) { // 回车键
                            var carConfiguRation = $('#carConfiguRationSearch').textbox('getValue');
                            var carConfiguRationParent = $('#carConfiguRationParentSearch').textbox('getValue');
                            initTable(carConfiguRation, carConfiguRationParent);
                        }
                    }
                })
            });
        });

        // 使用传递的数据加载表格
        function loadTableWithData(data) {
            console.log('使用传递的数据加载表格，数据数量：', data.length);

            // 过滤可调度的设备
            var availableData = data.filter(function(item) {
                return item.isShow !== false && item.schedulingCondition !== '不可调度';
            });

            console.log('可调度设备数量：', availableData.length);

            // 加载表格
            $("#table").datagrid({
                data: { rows: availableData, total: availableData.length },
                columns: [carCols],
                fitColumns: true,
                singleSelect: false,
                checkOnSelect: true,
                selectOnCheck: true,
                pagination: false,
                rownumbers: true,
                nowrap: false,
                rowStyler: function(index,row){
                    if (!row.isShow){
                        return 'background-color: rgb(242,242,242);';
                    }
                }
            });
        }

        window.getchoosedata=function(){
            var dataList = $("#table").datagrid("getChecked")
            for(var i=0;i<dataList.length;i++){
                dataList[i].carId=dataList[i].id
            }
            return {"data": dataList,"type":gps.type, "state":1};
        };
    </script>
</head>
<body>
<div>
    <!-- 搜索条件区域 -->
    <div style="margin: 20px; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9;">
        <table style="width: 100%;">
            <tr>
                <td width="120" align="right">应急设备分类：</td>
                <td width="200">
                    <input id="carConfiguRationParentSearch" class="easyui-textbox" style="width: 180px; height: 32px;"
                           data-options="prompt:'请输入应急设备分类'" />
                </td>
                <td width="120" align="right">应急设备子类：</td>
                <td width="200">
                    <input id="carConfiguRationSearch" class="easyui-textbox" style="width: 180px; height: 32px;"
                           data-options="prompt:'请输入应急设备子类'" />
                </td>
                <td width="100">
                    <a class="btn small" id="searchBtn" style="margin-left: 10px;">
                        <font>查询</font>
                    </a>
                </td>
                <td width="100">
                    <a class="btn small" id="resetBtn" style="margin-left: 10px;">
                        <font>重置</font>
                    </a>
                </td>
                <td></td>
            </tr>
        </table>
    </div>

    <div class="table" style="margin-top: 20px;">
        <table id="table"></table>
    </div>
</div>
</body>
</html>
