package com.simbest.boot.yjtxc.util;

import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class SMSTool {

    private final String[] msgSendUsers = new String[]{"oalijianwu","oazhangqianfeng","oasun<PERSON><PERSON>","oawuxingyu","sjbg"};

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    /**
     * 发送短信
     *
     * @param map 准备发送参数
     * @return
     */
    public Boolean sendSMS(Map<String, Object> map) {
        Boolean isPostMsgOK = false; //是否发送成功标志
        /**发送短信操作**/
        try {
            /**参数不为空情况下**/
            if (map != null && map.size() > 0) {
                String sendUser = map.get("sendUser") != null ? map.get("sendUser").toString() : "";
                /**发送人不为空**/
                if (StringUtils.isNotEmpty(sendUser)) {
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", map.get("fromUser") != null ? map.get("fromUser").toString() : "");
                    paramMap.put("itemSubject", map.get("itemSubject") != null ? map.get("itemSubject").toString() : "");
                    String msg = MessageEnum.MT000001.getMessage(paramMap);
                    /**准备参数**/
                    Map<String, Object> mapParam = Maps.newHashMap();
                    mapParam.put("sendUser", sendUser);
                    mapParam.put("msg", msg);
                    isPostMsgOK = msgPostOperatorService.postMsg(readyParams(mapParam));
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return isPostMsgOK;
    }

    /**
     * 准备短信对象
     *
     * @param map 参数 （endUser待发人，短信模板）
     * @return
     */
    public ShrotMsg readyParams(Map<String, Object> map) {
        ShrotMsg shrotMsg = new ShrotMsg();//短信对象
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode(Constants.APP_CODE);
        content.setUsername(map.get("sendUser") != null ? map.get("sendUser").toString() : "");
        content.setMsgContent(map.get("msg") != null ? map.get("msg").toString() : "");
        content.setImmediately(true);
        content.setSmsPriority(1);
        contentSet.add(content);
        shrotMsg.setContents(contentSet);
        return shrotMsg;
    }

    public void sendMsgUtil(String msgContent) {
        try {
            ShrotMsg shrotMsg = new ShrotMsg();
            Set<Content> contentSet = new HashSet<Content>();
            shrotMsg.setAppCode( Constants.APP_CODE );

            for (int i = 0,count = msgSendUsers.length; i < count; i++) {
                Content content = new Content();
                content.setUsername( msgSendUsers[i] );
                content.setMsgContent( msgContent );
                content.setImmediately( true );
                content.setSmsPriority( 1 );
                contentSet.add( content );
            }
            shrotMsg.setContents( contentSet );
            Boolean isPostMsgOK = msgPostOperatorService.postMsg( shrotMsg );
            log.error( "调用统一待办接口服务发短信状态:【{}】", JacksonUtils.obj2json(isPostMsgOK));
        } catch (Exception e) {
            Exceptions.printException( e );
        }
    }
}

