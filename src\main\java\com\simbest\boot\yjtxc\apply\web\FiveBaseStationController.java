package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FiveBaseStation;
import com.simbest.boot.yjtxc.apply.service.IFiveBaseStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:04
 * @describe
 */
@Api(description = "5G基站站号")
@Slf4j
@RestController
@RequestMapping(value = "/action/fiveBaseStation")
public class FiveBaseStationController extends LogicController<FiveBaseStation, String> {

    private IFiveBaseStationService service;

    public FiveBaseStationController(IFiveBaseStationService service) {
        super(service);
        this.service=service;
    }



    /**
     * 维护5G基站号
     * @return
     */
    @ApiOperation(value = "维护5G基站号", notes = "维护5G基站号")
    @PostMapping({"/updateFiveBaseStation", "/updateFiveBaseStation/sso"})
    public JsonResponse updateFiveBaseStation(@RequestBody FiveBaseStation fiveBaseStation){
        return service.updateFiveBaseStation(fiveBaseStation);
    }

    /**
     * 查询5G基站号
     * @param pmInsId
     * @return
     */
    @ApiOperation(value = "查询5G基站号", notes = "维护5G基站号")
    @PostMapping({"/findByPmInsIdFiveBaseStation", "/findByPmInsIdFiveBaseStation/sso"})
    public JsonResponse findByPmInsIdFiveBaseStation(@RequestParam String pmInsId){
        return service.findByPmInsIdFiveBaseStation(pmInsId);
    }

    /**
     * 批量添加5G基站号
     * @return
     */
    @ApiOperation(value = "批量添加5G基站号", notes = "批量添加5G基站号")
    @PostMapping({"/saveFiveBaseStation", "/saveFiveBaseStation/sso"})
    public JsonResponse saveFiveBaseStation(@RequestBody List<FiveBaseStation> fiveBaseStationList,@RequestParam String pmInsId){
        return service.saveFiveBaseStation(fiveBaseStationList,pmInsId);
    }


}
