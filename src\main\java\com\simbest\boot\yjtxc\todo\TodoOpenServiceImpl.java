package com.simbest.boot.yjtxc.todo;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.task.model.TaskCallbackLog;
import com.simbest.boot.bps.task.service.ITaskCallBackLogService;
import com.simbest.boot.bps.task.todo.TodoOpenInterface;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.simbest.boot.config.MultiThreadConfiguration.MULTI_THREAD_BEAN;

/**
 * <strong>Title : TodoOpenServiceImpl</strong><br>
 * <strong>Description : 推送统一代办</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class TodoOpenServiceImpl implements TodoOpenInterface {

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private ITaskCallBackLogService taskCallBackLogService;


    /**
     * 推送统一代办
     * @param businessStatus     业务流程操作对象
     * @param sendUser           审批人
     */
    @Async (MULTI_THREAD_BEAN)
    @Override
    public void execution ( ActBusinessStatus businessStatus, String sendUser ) {
        try {
            LocalDateTime callbackStartDate = LocalDateTime.now();
            Boolean isTodoFlag = false;  //待办开关 false代表不推送  true推送
            Boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,sendUser );
            if ( simpleApp != null ){
                isTodoFlag = simpleApp.getTodoOpen();
                isPostMsg = simpleApp.getIsSendMsg();
            }
            if ( isTodoFlag ) {
                todoBusOperatorService.openTodo( businessStatus, sendUser );
            }
            Boolean isPostMsgOK = false;
            /**短信相关操作**/
            if ( isPostMsg && StrUtil.isNotEmpty(businessStatus.getPreviousAssistant())){
                /**准备发送短息**/
                if ( StringUtils.isNotEmpty( sendUser) ){
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    // paramMap.put("fromUser", businessStatus.getCreateUserName());
                    paramMap.put("fromUser", businessStatus.getPreviousAssistantName());
                    paramMap.put("itemSubject", businessStatus.getReceiptTitle());
                    String msg = MessageEnum.MT000001.getMessage(paramMap);
                    isPostMsgOK = msgPostOperatorService.postMsg( readyParams( sendUser,msg) );
                }
            }
            /**记录发送短信日志操作**/
            if ( !isPostMsgOK ){
                TaskCallbackLog taskCallbackLog = new TaskCallbackLog();
                taskCallbackLog.setActBusinessStatusId(businessStatus.getId());
                taskCallbackLog.setCallbackType("MSG");
                taskCallbackLog.setCallbackStartDate(callbackStartDate);
                taskCallbackLog.setCallbackEndDate(LocalDateTime.now());
                Duration duration = Duration.between( taskCallbackLog.getCallbackEndDate(),callbackStartDate );
                taskCallbackLog.setCallbackDuration(duration.toMillis());
                taskCallbackLog.setCallbackResult(isPostMsgOK);
                taskCallbackLog.setCallbackError(String.valueOf( isPostMsgOK ));
                log.debug( "TodoOpenServiceImpl execution MSG Fialure>>>>>taskCallBackLogService>>>>" + taskCallBackLogService );
                taskCallbackLog = taskCallBackLogService.insert(taskCallbackLog);
                log.debug( "TodoOpenServiceImpl execution MSG Fialure>>>>" + taskCallbackLog.toString() );
            }
        }catch ( Exception e ){
            log.error("TodoOpenServiceImpl execution Error>>>>>>>" + e.getMessage());
            Exceptions.printException( e );
            throw e;
        }
    }

    /**
     * 准备短信对象
     * @param sendUser 发送人
     * @param msg 短信内容
     * @return
     */
    static ShrotMsg readyParams(String sendUser, String msg){
        ShrotMsg shrotMsg = new ShrotMsg();
        Content content = new Content();
        Set<Content> contentSet = new HashSet<Content>();
        shrotMsg.setAppCode( Constants.APP_CODE );
        content.setUsername( sendUser );
        content.setMsgContent( msg );
        content.setImmediately( true );
        content.setSmsPriority( 1 );
        contentSet.add( content );
        shrotMsg.setContents( contentSet );
        return shrotMsg;
    }
}
