package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.GarageConfiguration;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:29
 * @describe 应急车库配置业务层
 */
public interface IGarageConfigurationService extends ILogicService<GarageConfiguration, String> {


    /**
     * 导入应急车库配置信息
     * @param request
     * @param response
     * @return
     */
    JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response);

    /**
     * 保存应急车库配置信息
     * @param garageConfigurationList
     * @return
     */
    JsonResponse saveGarageConfiguration(List<GarageConfiguration> garageConfigurationList);

    /**
     * 删除应急车库配置信息
      * @param id
     * @return
     */
    JsonResponse deleteGarageConfiguration(String id);

    /**
     * 停用||启用、车库配置信息
     * @param id
     * @param flag
     * @return
     */
    JsonResponse updateGarageConfiguration(String id, String flag);

    /**
     *条件查询车库配置信息
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    JsonResponse findByAllGarageConfiguration(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap);

    /**
     * 查询大型应急车
     * @return
     */
    List<Map<String, Object>> findByBigCar();

    /**
     * 查询GSM卫星车
     * @return
     */
    List<Map<String, Object>> findByGSMCar();

    /**
     * 大型应急车按照地市条件查询
     * @param cities
     * @return
     */
    List<Map<String, Object>> findByBigCarAndCities(String cities);

    /**
     * GSM卫星车按照地市条件查询
     * @param cities
     * @return
     */
    List<Map<String, Object>> findByGSMCarAndCities(String cities);

    List<GarageConfiguration> findByIdddd(String phoneNmu,String vehicle);

    /**
     * 查询车辆
     * @param phoneNmu
     * @return
     */
    GarageConfiguration findByPonte(String phoneNmu);


    /**
     * 查询车辆
     * @param phoneNmu
     * @return
     */
    List<GarageConfiguration> findByPonteList(String phoneNmu);

    /**
     * 根据手机号和车牌号查询
     */
    GarageConfiguration findByPonteLiattion(String phoneNmu ,String protaion);

    /**
     * 获取所有不重复的应急设备大类列表
     * @return JsonResponse包含应急设备大类列表
     */
    JsonResponse getCarConfiguRationParentList();

    JsonResponse getCarConfiguRationList();
}
