<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .splite {
            border-left: 3px solid rgb(57, 174, 245);
            padding-left: 15px;
            font-size: 16px;
            font-weight: 600;
        }
    </style>
    <script type="text/javascript">
        $(function(){
            if(gps.id) {
                ajaxgeneral({
                    url: "action/involvesPersonnel/findById?id="+gps.id,
                    success:function(data) {
                        formval(data.data, "form")
                    }
                })
            }
        });
        window.getchoosedata = function() {
            if(formValidate("form")) {
                return { data: getFormValue("form"), state: 1 }
            }
        }
    </script>
</head>
<body class="body_page">
<form id="form">
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td colspan="6" class="splite">基础信息</td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>地市公司：</td>
            <td>
                <input id="appCities" name="appCities" type="text" value="" class="easyui-validatebox appCities" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>应急配置类型：</td>
            <td>
                <input id="emerConfiType" name="emerConfiType" type="text" value="" class="easyui-validatebox emerConfiType" required="required" />
                <input id="id" name="id" type="hidden" value="" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>对应车牌号：</td>
            <td>
                <input id="correspondsNumber" name="correspondsNumber" type="text" value="" class="easyui-validatebox correspondsNumber" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>设备厂家：</td>
            <td>
                <input id="equipmentManu" name="equipmentManu" type="text" value="" class="easyui-validatebox equipmentManu" required="required" />
            </td>
            <td></td>
            <td> </td>
            <td></td>
            <td> </td>
        </tr>
        <tr>
            <td colspan="6" class="splite">应急车申请人</td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>姓名（A角）：</td>
            <td>
                <input id="appUsernameA" name="appUsernameA" type="text" value="" class="easyui-validatebox appUsernameA" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>电话：</td>
            <td>
                <input id="appphoneNumA" name="appphoneNumA" type="text" value="" class="easyui-validatebox appphoneNumA" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>邮件：</td>
            <td>
                <input id="appLinkEmailA" name="appLinkEmailA" type="text" value="" class="easyui-validatebox appLinkEmailA" required="required" />

            </td>
        </tr>

        <tr>
            <td width="110" align="right">姓名（B角）：</td>
            <td>
                <input id="appTruenameB" name="appTruenameB" type="text" value="" class="easyui-validatebox appTruenameB"  />
            </td>
            <td width="110" align="right">电话：</td>
            <td>
                <input id="appPhoneNumB" name="appPhoneNumB" type="text" value="" class="easyui-validatebox appPhoneNumB"  />

            </td>
            <td width="110" align="right">邮件：</td>
            <td>
                <input id="appLinkEmailB" name="appLinkEmailB" type="text" value="" class="easyui-validatebox appLinkEmailB"  />

            </td>
        </tr>

        <tr>
            <td colspan="6" class="splite">网络部经理</td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>姓名：</td>
            <td>
                <input id="appTruenameVehicle" name="appTruenameVehicle" type="text" value="" class="easyui-validatebox appTruenameVehicle" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>电话：</td>
            <td>
                <input id="appPhoneVehicle" name="appPhoneVehicle" type="text" value="" class="easyui-validatebox appPhoneVehicle" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>邮件：</td>
            <td>
                <input id="appLinkEmailVehicle" name="appLinkEmailVehicle" type="text" value="" class="easyui-validatebox appLinkEmailVehicle" required="required" />

            </td>
        </tr>

        <tr>
            <td colspan="6" class="splite">应急车负责人（开站人）</td>
        </tr>

        <tr>
            <td width="110" align="right"><font class="col_r">*</font>姓名（A角）：</td>
            <td>
                <input id="headTruenameA" name="headTruenameA" type="text" value="" class="easyui-validatebox headTruenameA" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>电话：</td>
            <td>
                <input id="headPhoneNumA" name="headPhoneNumA" type="text" value="" class="easyui-validatebox headPhoneNumA" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>邮件：</td>
            <td>
                <input id="headLinkEmailA" name="headLinkEmailA" type="text" value="" class="easyui-validatebox headLinkEmailA" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right">姓名（B角）：</td>
            <td>
                <input id="headTruenameB" name="headTruenameB" type="text" value="" class="easyui-validatebox headTruenameB"  />
            </td>
            <td width="110" align="right">电话：</td>
            <td>
                <input id="headPhoneNumB" name="headPhoneNumB" type="text" value="" class="easyui-validatebox headPhoneNumB"  />

            </td>
            <td width="110" align="right">邮件：</td>
            <td>
                <input id="headLinkEmailB" name="headLinkEmailB" type="text" value="" class="easyui-validatebox headLinkEmailB"  />

            </td>
        </tr>

    </table>
</form>
</body>
</html>
