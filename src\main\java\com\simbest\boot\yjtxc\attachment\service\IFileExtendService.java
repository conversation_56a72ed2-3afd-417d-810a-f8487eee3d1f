package com.simbest.boot.yjtxc.attachment.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.sys.model.SysFile;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
public interface IFileExtendService extends ILogicService<SysFile,String> {

    /**
     * 更新附件
     * @param pmInsId 主单据id
     * @param id 附近id
     * @return
     */
    int updatePmInsId ( String pmInsId, String id );

    /**
     * @Description 逻辑删除附件
     * @Date 2020/8/26 14:53
     * @Param [pmInsId]
     * @return int
     **/
    int deleteByPmInsId(String pmInsId);

    /**
     * 查询区域附件
     * @param pmInsId
     * @param filePart
     * @return
     */
    List<SysFile> getPartFile ( String pmInsId, String filePart );

    /**
     * 查询区域附件
     * @param id
     * @return
     */
    List<SysFile> getFileById(String[] id);

    /**
     * 上传视频文件
     * @param multipartFiles
     * @param pmInsType
     * @param pmInsId
     * @param pmInsTypePart
     * @return
     */
    List<SysFile> uploadProcessFiles(Collection<MultipartFile> multipartFiles, String pmInsType, String pmInsId, String pmInsTypePart);

    void updatePmInsIdTwo(String id, String id1);

    List<SysFile> finListSysFile(String formId);
}
