package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.FiveBaseStation;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:06
 * @describe 5G基站站号
 */
public interface FiveBaseStationRespository extends LogicRepository<FiveBaseStation, String> {


    @Modifying
    @Query(value = " update US_FIVE_BASE_STATION fbs set fbs.enabled='0',fbs.removed_time=sysdate where fbs.five_base_id=:pmInsId ", nativeQuery = true)
    void deleteByPmInsId(@Param("pmInsId") String pmInsId);

    @Query(value = " select fbs.id," +
            "       fbs.created_time," +
            "       fbs.modified_time," +
            "       fbs.creator," +
            "       fbs.enabled," +
            "       fbs.modifier," +
            "       fbs.removed_time," +
            "       fbs.aau_model," +
            "       fbs.aau_number," +
            "       fbs.baseband_board_model_one," +
            "       fbs.baseband_board_model_two," +
            "       fbs.baseband_board_number_one," +
            "       fbs.baseband_board_number_two," +
            "       fbs.baseband_supports," +
            "       fbs.bbu_model," +
            "       fbs.equipment_manufacturer," +
            "       fbs.five_base_id," +
            "       fbs.spare01," +
            "       fbs.spare02," +
            "       fbs.spare03 from US_FIVE_BASE_STATION fbs where fbs.enabled='1' and fbs.five_base_id=:pmInsId ", nativeQuery = true)
    List<FiveBaseStation> findByPmInsIdFiveBaseStation(@Param("pmInsId") String pmInsId);


}
