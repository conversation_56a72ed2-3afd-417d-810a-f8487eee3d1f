package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.service.IApplicationFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:32
 * @describe 日常应急-地市内调动
 */
@Api(description = "日常应急-地市内调动")
@Slf4j
@RestController
@RequestMapping(value = "/action/applicationForm")
public class ApplicationFormController extends LogicController<ApplicationForm, String> {




    private IApplicationFormService iApplicationFormService;

    public ApplicationFormController(IApplicationFormService iApplicationFormService) {
        super(iApplicationFormService);
        this.iApplicationFormService = iApplicationFormService;
    }


    @Autowired
    IProcessInstanceService iProcessInstanceService;

    /**
     * 打开详情办理
     *
     * @param processInstId 流程实例id
     * @return
     */
    @ApiOperation(value = "打开详情", notes = "办理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
            @ApiImplicitParam(name = "workFlag", value = "打开状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前操作用户", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getFormDetail", "/getFormDetail/api", "/getFormDetail/sso"})
    public JsonResponse getFormDetail(@RequestParam(required = false) Long processInstId,
                                      @RequestParam(required = false) String workFlag,
                                      @RequestParam String source,
                                      @RequestParam(required = false) String pmInsId,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String currentUserCode) {
        return iApplicationFormService.getFormDetail(processInstId, workFlag, source, pmInsId, location, currentUserCode);
    }


    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @return
     */
    @ApiOperation(value = "查询决策", notes = "根据当前环节提供相应的决策")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "processDefName", value = "流程定义名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "location", value = "当前环节", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })

    @PostMapping(value = {"/getDecisions", "/api/getDecisions", "/getDecisions/sso"})
    public JsonResponse getDecisions(
            @RequestParam(required = false) String processInstId,
            @RequestParam(required = false) String processDefName,
            @RequestParam String location,
            @RequestParam(required = false, defaultValue = "PC") String source,
            @RequestParam(required = false) String currentUserCode,
            @RequestParam(required = false) String applyType) {
        return iApplicationFormService.getDecisions(processInstId, processDefName, location, source, currentUserCode, applyType);
    }

    /**
     * 获取到决策下组织人员
     *
     * @param processInstId 流程实例id
     * @param appDecision   决策对象
     * @return
     */
    @ApiOperation(value = "根据决策显示组织人员", notes = "根据决策查询人员组织")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "当前OA账号", dataType = "String", paramType = "query"),
    })
    @PostMapping(value = {"/getOrgAndUser", "/api/getOrgAndUser", "/getOrgAndUser/sso"})
    public JsonResponse getOrgAndUser(@RequestParam String source,
                                      @RequestParam(required = false) String processInstId,
                                      @RequestParam(required = false) String currentUserCode,
                                      @RequestBody SimpleAppDecision appDecision,
                                      @RequestParam(required = false) String location,
                                      @RequestParam(required = false) String phone,
                                      @RequestParam(required = false) String licensePlate,
                                      @RequestParam(required = false) String belongCompanyCode
    ) {
        return iApplicationFormService.getOrgAndUser(processInstId, source, currentUserCode, appDecision, location, phone,licensePlate, belongCompanyCode);
    }

    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param bodyParam       流程西下一步参数
     * @param formId          表单id
     * @param notificationId  待阅表单id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交起草流程", notes = "通过此接口启动流转审批表单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentUserCode", value = "当前登录人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "workItemId", value = "工作项ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "outcome", value = "连线规则", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "message", value = "审批意见", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "nextUserName", value = "审批人", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "location", value = "当前状态", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "copyLocation", value = "抄送下一环节", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "formId", value = "表单id", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "notificationId", value = "待阅表单id", dataType = "String", paramType = "query", required = false),
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = false),
    })
    @PostMapping(value = {"/startSubmitProcess", "/api/startSubmitProcess", "/startSubmitProcess/sso"})
    public JsonResponse startSubmitProcess(@RequestParam(required = false) String currentUserCode,
                                           @RequestParam(required = false) String source,
                                           @RequestParam(required = false) String workItemId,
                                           @RequestParam(required = false) String outcome,
                                           @RequestParam(required = false) String location,
                                           @RequestParam(required = false) String copyLocation,
                                           @RequestParam(required = false) String formId,
                                           @RequestParam(required = false) String notificationId,
                                           @RequestParam(required = false) String processInstId,
                                           @RequestBody(required = false) Map<String, Object> bodyParam) throws Exception {
        return iApplicationFormService.startSubmitProcess(source, currentUserCode, workItemId, outcome, location, copyLocation, bodyParam, formId, notificationId, processInstId);
    }


    /**
     * 查询组织树
     *
     * @return
     */
    @ApiOperation(value = "组织树", notes = "组织树")
    @PostMapping(value = {"/queryOrgTree", "/api/queryOrgTree", "/queryOrgTree/sso"})
    public JsonResponse queryOrgTree() {
        return iApplicationFormService.queryOrgTree();
    }

    /**
     * 工单编号产生
     */
    @ApiOperation(value = "工单编号产生", notes = "工单编号产生")
    @PostMapping(value = {"/creatWorkNumber", "/api/creatWorkNumber", "/creatWorkNumber/sso"})
    public String creatWorkNumber(@RequestParam String value, @RequestParam(required = false) String processType) {
        return iApplicationFormService.creatWorkNumber(value, processType);
    }


    /**
     * 根据起草类型查询车辆
     *
     * @param cities
     * @param carType
     * @param processType
     * @return
     */
    @ApiOperation(value = "根据起草类型查询车辆", notes = "根据起草类型查询车辆")
    @PostMapping(value = {"/findByCar", "/api/findByCar", "/findByCar/sso"})
    public JsonResponse findByCar(@RequestParam String cities,
                                  @RequestParam String carType,
                                  @RequestParam String processType) {
        return iApplicationFormService.findByCar(cities, carType, processType);
    }

    /**
     * 根据起草类型查询车辆
     * @param params
     * @return
     */
    @ApiOperation(value = "根据起草类型查询车辆", notes = "根据起草类型查询车辆")
    @PostMapping(value = {"/findByCarTime", "/api/findByCarTime", "/findByCarTime/sso"})
    public JsonResponse findByCarTime(@RequestBody Map<String, Object> params) {
        return iApplicationFormService.findByCarTime(params);
    }

    /**
     * 查询所有车辆类型（不过滤carType）
     * @param params
     * @return
     */
    @ApiOperation(value = "查询所有车辆类型", notes = "查询所有车辆类型，不对carType字段进行过滤筛选")
    @PostMapping(value = {"/findAllCarTypes", "/api/findAllCarTypes", "/findAllCarTypes/sso"})
    public JsonResponse findAllCarTypes(@RequestBody Map<String, Object> params) {
        return iApplicationFormService.findAllCarTypes(params);
    }

    /**
     * 工单查询
     *
     * @param page
     * @param size
     * @param params
     * @return
     */
    @ApiOperation(value = "工单查询", notes = "工单查询")
    @PostMapping(value = {"/findByCount", "/api/findByCount", "/findByCount/sso"})
    public JsonResponse findByCount(@RequestParam(required = false, defaultValue = "1") int page,
                                    @RequestParam(required = false, defaultValue = "10") int size,
                                    @RequestBody Map<String, Object> params) {
        return iApplicationFormService.findByCount(page, size, params);

    }

    /**
     * 工单信息导出工单查询
     *
     * @param response
     * @param request
     * @param applicationForm
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "工单信息导出工单查询", notes = "工单信息导出工单查询")
    @PostMapping(value = {"/exportOrder", "/api/exportOrder", "/exportOrder/sso"})
    @ResponseBody
    public JsonResponse exportOrder(HttpServletResponse response, HttpServletRequest request, ApplicationForm applicationForm) throws ParseException {
        iApplicationFormService.exportOrder(request, response, applicationForm);
        return JsonResponse.defaultSuccessResponse();
    }



    @ApiOperation(value = "注销该流程", notes = "注销起草的流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
    })
    @PostMapping(value = {"/deleteProcess", "/api/deleteProcess", "/sso/deleteProcess"})
    public JsonResponse deleteProcess(@RequestParam(required = false) Long processInstId,
                                      @RequestBody(required = false) ApplicationForm applicationForm,
                                      @RequestParam(required = false) String PmInsId) {
        return iApplicationFormService.deleteProcess(processInstId, applicationForm, PmInsId);

    }


    @ApiOperation(value = "废除归档但是可以在已办中查到", notes = "废除归档但是可以在已办中查到")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processInstId", value = "流程实例id", dataType = "Long", paramType = "query", required = true),
    })
    @PostMapping(value = {"/terminateProcessInst", "/api/terminateProcessInst", "/sso/terminateProcessInst"})
    public JsonResponse terminateProcessInst(@RequestParam(required = false) Long processInstId,
                                             @RequestBody(required = false) ApplicationForm applicationForm,
                                             @RequestParam(required = false) String currentUserCode) {
        //手动核销删除统一代办
        iApplicationFormService.deleteNtodoDate(processInstId, applicationForm);
        iProcessInstanceService.terminateProcessInst(processInstId);
        return JsonResponse.defaultSuccessResponse();

    }
}
