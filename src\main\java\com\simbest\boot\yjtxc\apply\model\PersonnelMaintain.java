package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/8 11:20
 * @describe 抄送人员维护
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_personnel_maintain")
@ApiModel(value = "抄送人员维护")
public class PersonnelMaintain extends SystemModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "PM") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "公司名称")
    protected String companyName;

    @Column(length = 40)
    @ApiModelProperty(value = "公司名称")
    protected String companyNameCode;

    @Column(length = 40)
    @ApiModelProperty(value = "职位")
    protected String position;

    @Column(length = 40)
    @ApiModelProperty(value = "职位Code")
    protected String positionCode;

    @Column(length = 60)
    @ApiModelProperty(value = "OA账号")
    protected String excludeUserName; // 该部门下不接收待阅的用户

    @Column(length = 60)
    @ApiModelProperty(value = "部门code")
    protected String departmentCode;

    @Column(length = 60)
    @ApiModelProperty(value = "部门名称")
    protected String departmentName;
}
