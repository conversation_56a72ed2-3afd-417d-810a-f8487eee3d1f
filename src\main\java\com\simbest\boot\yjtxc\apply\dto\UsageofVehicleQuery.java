package com.simbest.boot.yjtxc.apply.dto;


import com.simbest.boot.base.annotations.ExcelVOAttribute;
import lombok.Data;

import java.io.Serializable;

/**
 * 车辆使用情况
 */
@Data
public class UsageofVehicleQuery implements Serializable {

    @ExcelVOAttribute(name = "应急车配置类型", column = "A")
    private String carConfiguRation;

    @ExcelVOAttribute(name = "车牌号", column = "B")
    private String licensePlate;

    @ExcelVOAttribute(name = "车辆归属地", column = "C")
    private String placeOwnership;

    @ExcelVOAttribute(name = "设备厂家", column = "D")
    private String equipmentManufacturer;

    @ExcelVOAttribute(name = "活动名称", column = "E")
    protected String nameEvent;

    @ExcelVOAttribute(name = "保障开始时间", column = "F")
    protected String guaranteeStartTime;

    @ExcelVOAttribute(name = "保障结束时间", column = "G")
    protected String guaranteeEndTime;

    @ExcelVOAttribute(name = "调度状态", column = "H")
    protected String schedulingCondition;

    @ExcelVOAttribute(name = "出车地市", column = "I")
    protected String carCities;



}
