package com.simbest.boot.yjtxc.todo.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.yjtxc.todo.model.UsTodoModel;

/**
 * <strong>Title : IUsTodoModelService</strong><br>
 * <strong>Description : 用户业务待办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> lijian<PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IUsTodoModelService extends ILogicService<UsTodoModel,Long> {

    /**
     * 保存推送待办数据到本地
     * @param usTodoModel      待办对象
     * @return
     */
    UsTodoModel savaLocalTodoData ( UsTodoModel usTodoModel );
}
