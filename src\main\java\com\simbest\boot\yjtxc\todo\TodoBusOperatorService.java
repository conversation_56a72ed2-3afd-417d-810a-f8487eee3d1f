package com.simbest.boot.yjtxc.todo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cmcc.mss.importsrvresponse.ImportSrvResponse;
import com.cmcc.mss.sb_oa_oa_importtodocloselistinfosrv.SBOAOAImportToDoCloseListInfoSrvInputItem;
import com.cmcc.mss.sb_oa_oa_importtodoopenlistinfosrv.SBOAOAImportToDoOpenListInfoSrvInputItem;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.bps.enums.ToDoEnum;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.listener.model.WFNotificationInstModel;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.cmcc.nmsg.model.ShrotMsg;
import com.simbest.boot.cmcc.ntodo.CloseTodoSrvClient;
import com.simbest.boot.cmcc.ntodo.OpenTodoSrvClient;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import com.simbest.boot.yjtxc.apply.service.IApplicationFormService;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import com.simbest.boot.yjtxc.todo.model.UsTodoModel;
import com.simbest.boot.yjtxc.todo.repository.UsTodoModelRepository;
import com.simbest.boot.yjtxc.todo.service.IUsTodoModelService;
import com.simbest.boot.yjtxc.util.BpsConfig;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.yjtxc.util.SMSTool;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <strong>Title : TodoBusOperatorService</strong><br>
 * <strong>Description : 统一代办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class TodoBusOperatorService extends LogicService<UsTodoModel, Long> {

    private final String[] msgSendUsers = new String[]{"oalijianwu", "oazhangqianfeng", "oasunhaoran", "oawuxingyu", "oayangkai", "sjbg"};

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;

    @Autowired
    private IApplicationFormService iApplicationFormService;

    private UsTodoModelRepository usTodoModelRepository;

    @Autowired
    private IUsTodoModelService usTodoModelService;

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    public TodoBusOperatorService(UsTodoModelRepository repository) {
        super(repository);
        this.usTodoModelRepository = repository;
    }

    @Autowired
    private SMSTool smsTool;

    public static final Map<String, String> todoHtml;


    @Autowired
    private IProcessTodoDataService processTodoDataService;


    static {
        Map<String, String> todoHtmlTmp = Maps.newConcurrentMap();
        todoHtmlTmp.put("A", "/html/apply/dailyLocal.html");
        todoHtmlTmp.put("B", "/html/apply/dailyAcross.html");
        todoHtmlTmp.put("C", "/html/apply/wartime.html");
        todoHtml = todoHtmlTmp;
    }


    /**
     * 推送统一代办
     *
     * @param businessStatus 业务状态操作对象
     * @param userName       审批人
     */
    public void openTodo(ActBusinessStatus businessStatus, String userName) {
        log.debug("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办开始");
        String oaHtmlUrl = null;
        String urlParams = null;
        Boolean sendFalg = false;
        log.debug("businessStatus>>>>>>>>" + businessStatus.toString());

        try {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(businessStatus)) {
                //发送推送统一待办异常短信
                String msgContent = Constants.APP_NAME.concat(":【调用接口平台推送统一代办异常：流程回调时流程业务关联实体【ActBusinessStatus】为空！");
                smsTool.sendMsgUtil(msgContent);
            }
            String pmInsType = iApplicationFormService.findByApplicationForm(businessStatus.getReceiptCode());
            //截取到数字，代表流程类型
            //String pmInsType = businessStatus.getReceiptCode().replaceAll("[^(A-Za-z)]", "");
            //代办回调的路径
            oaHtmlUrl = appConfig.getAppHostPort() + "/" + Constants.APP_CODE + todoHtml.get(pmInsType);
            //代办回调路径后面带的参数，即url ?后面的数据
            urlParams = "?type=task&location=" + businessStatus.getActivityDefId() + "&processInstId=" + businessStatus.getProcessInstId() + "&processDefName=" + businessStatus.getProcessDefName() + "&workItemId=" + businessStatus.getWorkItemId() + "&currentState=" + businessStatus.getCurrentState()+ "&pmInsId=" + businessStatus.getReceiptCode()+ "&usPmInstenceId=" + businessStatus.getBusinessKey()+ "&pmInsType=" + pmInsType+ "&name=auditVal&appcode=yjtxc&from=oa";
            SBOAOAImportToDoOpenListInfoSrvInputItem inputItem = new SBOAOAImportToDoOpenListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID(String.valueOf(businessStatus.getProcessInstId()));
            inputItem.setWORKID(String.valueOf(businessStatus.getWorkItemId()));
            inputItem.setUSERID(userName);
            inputItem.setTITLE(businessStatus.getReceiptTitle());
            inputItem.setSTARTTIME(DateUtil.localDateTimeToXmlDate(LocalDateTime.now()));
            inputItem.setURL(oaHtmlUrl + urlParams);
            inputItem.setSENDER(userName);
            inputItem.setSYSID(BigDecimal.valueOf(Integer.parseInt(Constants.APP_SYS_ID)));
            inputItem.setDOCTYPE(Constants.APP_NAME);
            inputItem.setTYPE(Constants.APP_WORK_TYPE);
            inputItem.setSOURCEID("PR");
            ImportSrvResponse importSrvResponse = OpenTodoSrvClient.openTodo(Constants.APP_CODE, inputItem);
            String serviceflag = importSrvResponse.getSERVICEFLAG();
            if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                sendFalg = true;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用推送统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台推送统一代办异常");
            smsTool.sendMsgUtil(msgContent);
        }

        saveTodoModel(businessStatus, userName, oaHtmlUrl, urlParams, sendFalg, Constants.APP_SYS_ID, Constants.APP_NAME);

        log.warn("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送统一代办结束");
    }

    /**
     * 核销统一代办
     *
     * @param businessStatus 业务状态操作对象
     * @param userName       审批人
     */
    public void closeTodo(ActBusinessStatus businessStatus, String userName) {
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办开始");
        Boolean sendFalg = false;
        log.warn("businessStatus>>>>>>>>【{}】", JacksonUtils.obj2json(businessStatus));

        try {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(businessStatus)) {
                //发送推送统一待办异常短信
                String msgContent = Constants.APP_NAME.concat(":【调用接口平台核销统一代办异常：流程回调时流程业务关联实体【ActBusinessStatus】为空！");
                smsTool.sendMsgUtil(msgContent);
            }
            SBOAOAImportToDoCloseListInfoSrvInputItem inputItem = new SBOAOAImportToDoCloseListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID(String.valueOf(businessStatus.getProcessInstId()));
            inputItem.setWORKID(String.valueOf(businessStatus.getWorkItemId()));
            inputItem.setUSERID(userName);
            inputItem.setSOURCEID("PR");
            inputItem.setSYSID(BigDecimal.valueOf(Integer.parseInt(Constants.APP_SYS_ID)));
            inputItem.setCLOSETIME(DateUtil.localDateTimeToXmlDate(LocalDateTime.now()));
            ImportSrvResponse importSrvResponse = CloseTodoSrvClient.closeTodo(Constants.APP_CODE, inputItem);
            String serviceflag = importSrvResponse.getSERVICEFLAG();
            if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                sendFalg = true;
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            log.error("TodoBusOperatorService>>>>>>>openTodo>>>>>调用核销统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(userName).concat("】").concat("，调用接口平台核销统一代办异常");
            ;
            smsTool.sendMsgUtil(msgContent);
        }

        closeTodoModel(businessStatus, userName, sendFalg);
        log.warn("TodoBusOperatorService>>>>>>>closeTodo>>>>>调用接口平台核销统一代办结束");
    }

    /**
     * 删除统一代办
     *
     * @param actBusinessStatus 业务状态操作对象
     * @param userName          审批人
     */
    public void cancelTodo(ActBusinessStatus actBusinessStatus, String userName) {
    }

    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModel(ActBusinessStatus businessStatus, String todoUser, String oaHtmlUrl, String urlParams, Boolean sendFalg, String sysId, String sysName) {
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(businessStatus.getWorkItemId().toString());
                usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(businessStatus.getProcessInstId()));
                usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
                usTodoModel.setBusinessStatusId(businessStatus.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(businessStatus.getProcessDefId()));
                usTodoModel.setCreator(businessStatus.getCreateUserCode());
                usTodoModel.setCreatedTime(businessStatus.getCreateTime());
                usTodoModel.setModifiedTime(businessStatus.getEndTime());
                usTodoModel.setModifier(todoUser);
                usTodoModel.setUserName(todoUser);
                usTodoModel.setSender(todoUser);
                usTodoModel.setTitle(businessStatus.getReceiptTitle());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(businessStatus.getEndTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>insert>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    @SuppressWarnings("unused")
    private UsTodoModel closeTodoModel(ActBusinessStatus businessStatus, String todoUser, Boolean sendFalg) {
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(StrUtil.toString(businessStatus.getWorkItemId()));
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)) {
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>close>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    /*@Retryable(value = Exception.class,maxAttempts = 3,backoff = @Backoff(delay = 1000,multiplier = 1.5))
    public String test() throws Exception{
        log.debug("开始执行代码："+ LocalTime.now());
        throw new Exception("【cmss】OpenTodoSrvClient-->openTodo推送待办异常!");
        //ImportSrvResponse importSrvResponse = OpenTodoSrvClient.openTodo(Constants.APP_CODE, null);
        //String serviceflag = importSrvResponse.getSERVICEFLAG();
        //return "22";
    }

    *//**
     * 最终重试失败处理
     * @param
     * @return
     *//*
    @Recover
    public String recover(Exception e){
        log.debug("代码执行重试后依旧失败");
        return null;
    }*/


    /*
     *推送待阅通知到统一待办库
     */
    public void openTodoToRead(UsPmInstence usPmInstence, String copyNextUser, String copyNextUserNames , WFNotificationInstModel wfNotificationInstModel){
        log.warn( "TodoBusOperatorService>>>>>>>openTodoToRead>>>>>调用接口平台推送待阅待办开始");
        String oaHtmlUrl = null;
        String urlParams = null;
        Long workId = null;
        Boolean sendFalg = false;
        ActBusinessStatus businessStatus = null;
        String wfId = null;
        boolean isPostMsg=false;
        boolean isTodoFlag=false;
        try {
            businessStatus = (ActBusinessStatus) processTodoDataService.queryActBusinessStatusByPmInstId(usPmInstence.getPmInsId());

            wfId = wfNotificationInstModel.getId();
            businessStatus.setActivityDefId(wfNotificationInstModel.getNextActivityDefId());

            log.warn( "businessStatus>>>>>>>>"+businessStatus.toString());

           // String pmInsType = iApplicationFormService.findByApplicationForm(businessStatus.getReceiptCode());

            //代办回调的路径
            oaHtmlUrl = appConfig.getAppHostPort() + "/" + Constants.APP_CODE + todoHtml.get(usPmInstence.getPmInsType());

            //代办回调路径后面带的参数，即url ?后面的数据
            urlParams = "?type=toRead&location=" + wfNotificationInstModel.getNextActivityDefId()+ "&processInstId="+ businessStatus.getProcessInstId()  + "&processDefName="+ businessStatus.getProcessDefName()
                    +"&notificationId="+wfNotificationInstModel.getId() + "&pmInsId="+usPmInstence.getPmInsId() +  "&name=auditVal&appcode=" + Constants.APP_CODE + "&from=oa" + "&pmInsType=" + usPmInstence.getPmInsType();
            SBOAOAImportToDoOpenListInfoSrvInputItem inputItem = new SBOAOAImportToDoOpenListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID(String.valueOf(businessStatus.getProcessInstId()));
            inputItem.setWORKID(wfId);
            inputItem.setUSERID(copyNextUser);
            inputItem.setTITLE(businessStatus.getReceiptTitle());
            inputItem.setSTARTTIME(DateUtil.localDateTimeToXmlDate(LocalDateTime.now()));
            inputItem.setURL(oaHtmlUrl + urlParams);
            inputItem.setSENDER(copyNextUser);
            inputItem.setSYSID(BigDecimal.valueOf(Integer.parseInt(Constants.APP_SYS_ID)));
            inputItem.setDOCTYPE(Constants.APP_NAME);
            inputItem.setTYPE("9");    //统一待办中 1为待办，9为待阅
            inputItem.setSOURCEID("PR");
            /**查询发送短信开关是否开始**/
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode( Constants.APP_CODE,wfNotificationInstModel.getSendUser() );
            if ( simpleApp != null ){
                isTodoFlag = simpleApp.getTodoOpen();
                isPostMsg = simpleApp.getIsSendMsg();
            }
            if (isTodoFlag){
                ImportSrvResponse importSrvResponse = OpenTodoSrvClient.openTodo(Constants.APP_CODE, inputItem);
                String serviceflag = importSrvResponse.getSERVICEFLAG();
                if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                    sendFalg = true;
                }
            }
            /**如果开关开启且待发列表不为空则发短信**/
            if (isPostMsg){
                Boolean isPostMsgOK = false;
                if ( StringUtils.isNotEmpty( copyNextUser) ){
                    /**准备审批短信模板数据**/
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("appName", Constants.APP_NAME);
                    paramMap.put("fromUser", businessStatus.getPreviousAssistantName());
                    paramMap.put("itemSubject", businessStatus.getReceiptTitle());
                    String msg = "${appName}:您收到${fromUser}向您发送的[${itemSubject}]的待阅工单，请及时处理。"
                            .replace("${appName}",Constants.APP_NAME).replace("${fromUser}",wfNotificationInstModel.getSendUserName())
                            .replace("${itemSubject}",wfNotificationInstModel.getReceiptTitle());;
                    isPostMsgOK = msgPostOperatorService.postMsg(TodoOpenServiceImpl.readyParams(copyNextUser,msg));
                    log.warn("待阅短信发送状态【{}】",isPostMsgOK);
                }
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>openTodoToRead>>>>>调用推送统一待阅待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待阅人：【").concat(copyNextUser).concat("】").concat("，调用接口平台推送统一待办异常");
            smsTool.sendMsgUtil(msgContent);
        }
        saveTodoModelToRead(businessStatus,copyNextUser,oaHtmlUrl,urlParams,sendFalg,Constants.APP_SYS_ID,Constants.APP_NAME,"toRead",wfId);
        log.warn("TodoBusOperatorService>>>>>>>openTodo>>>>>调用接口平台推送待阅待办结束");
    }

    /**
     * 核销待阅
     */
    public void closeTodoDoRead(UsPmInstence usPmInstence, WFNotificationInstModel model) {
        log.warn("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用接口平台核销待阅待办开始");
        Boolean sendFalg = false;
        ActBusinessStatus businessStatus = null;
        String wfId=null;
        try {
            businessStatus = (ActBusinessStatus) processTodoDataService.queryActBusinessStatusByPmInstId(usPmInstence.getPmInsId());
            wfId=model.getId();
            businessStatus.setActivityDefId(model.getNextActivityDefId());
            log.debug( "businessStatus>>>>>>>>"+businessStatus.toString());
            SBOAOAImportToDoCloseListInfoSrvInputItem inputItem = new SBOAOAImportToDoCloseListInfoSrvInputItem();
            inputItem.setPRIKEY(businessStatus.getBusinessKey());
            inputItem.setDOCID(String.valueOf(businessStatus.getProcessInstId()));
            inputItem.setWORKID(wfId);
            inputItem.setUSERID(model.getRecipient());
            inputItem.setSOURCEID("PR");
            inputItem.setSYSID(BigDecimal.valueOf(Integer.parseInt(Constants.APP_SYS_ID)));
            inputItem.setCLOSETIME(DateUtil.localDateTimeToXmlDate(LocalDateTime.now()));
            ImportSrvResponse importSrvResponse = CloseTodoSrvClient.closeTodo(Constants.APP_CODE, inputItem);
            String serviceflag = importSrvResponse.getSERVICEFLAG();
            if (StrUtil.equals(serviceflag, Constants.SUCCESS_FLAG)) {
                sendFalg = true;
            }
        } catch (Exception e) {
            Exceptions.printException( e );
            log.error("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用核销统一待办接口异常");
            //发送推送统一待办异常短信
            String msgContent = Constants.APP_NAME.concat(":【").concat(businessStatus.getReceiptTitle()).concat("】").concat("，待办人：【").concat(model.getRecipient()).concat("】").concat("，调用接口平台核销统一代办异常");;
            smsTool.sendMsgUtil(msgContent);
        }

        closeTodoModelDoRead(businessStatus,model.getRecipient(),sendFalg,"join",wfId);
        log.warn("TodoBusOperatorService>>>>>>>closeTodoDoRead>>>>>调用接口平台核销待阅待办结束");
    }

    @SuppressWarnings("unused")
    private UsTodoModel saveTodoModelToRead(ActBusinessStatus businessStatus,String todoUser,String oaHtmlUrl,String urlParams,Boolean sendFalg,String sysId,String sysName,String staus,String wfId){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setWorkItemId(wfId);
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (cn.hutool.core.util.ObjectUtil.isEmpty(usTodoModel)) {
                usTodoModel = new UsTodoModel();
                usTodoModel.setWorkItemId(wfId);
                usTodoModel.setActivityDefId(businessStatus.getActivityDefId());
                usTodoModel.setProcessInstanceId(StrUtil.toString(businessStatus.getProcessInstId()));
                usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
                usTodoModel.setBusinessStatusId(businessStatus.getId());
                usTodoModel.setProcessDefId(StrUtil.toString(businessStatus.getProcessDefId()));
                usTodoModel.setCreator(businessStatus.getCreateUserCode());
                usTodoModel.setCreatedTime(businessStatus.getCreateTime());
                usTodoModel.setModifiedTime(businessStatus.getEndTime());
                usTodoModel.setModifier(todoUser);
                usTodoModel.setUserName(todoUser);
                usTodoModel.setSender(todoUser);
                usTodoModel.setTitle(businessStatus.getReceiptTitle());
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue());
                usTodoModel.setOaHtmlUrl(oaHtmlUrl);
                usTodoModel.setUrlParams(urlParams);
                usTodoModel.setWorkFlag(true);
                usTodoModel.setEnabled(true);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setSendDate(businessStatus.getEndTime());
                usTodoModel.setSysId(sysId);
                usTodoModel.setSysName(sysName);
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>saveTodoModelToRead>>>>>【{}】", JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

    private UsTodoModel closeTodoModelDoRead(ActBusinessStatus businessStatus,String todoUser,Boolean sendFalg,String status,String wfId){
        UsTodoModel usTodoModel = new UsTodoModel();
        try {
            usTodoModel.setBusinessKey(businessStatus.getBusinessKey());
            usTodoModel.setSender(todoUser);
            usTodoModel.setWorkFlag(true);
            usTodoModel.setWorkItemId(wfId);
            Specification<UsTodoModel> usTodoModelSpecification = usTodoModelService.getSpecification(usTodoModel);
            usTodoModel = usTodoModelService.findOne(usTodoModelSpecification);
            log.warn("TodoBusOperatorService>>>>>>>usTodoModel>>>>>获取参数"+JacksonUtils.obj2json(usTodoModel));
        }catch (Exception e){
            Exceptions.printException( e );
        }finally {
            if (ObjectUtil.isNotEmpty(usTodoModel)){
                usTodoModel.setWorkFlag(false);
                usTodoModel.setSendFlag(sendFalg);
                usTodoModel.setTypeStatus(ToDoEnum.open.getValue() + "-" + ToDoEnum.close.getValue());
                usTodoModel = usTodoModelService.savaLocalTodoData(usTodoModel);
            }
        }
        log.warn("usTodoModelService>>>closeTodoModelDoRead>>>>>【{}】",JacksonUtils.obj2json(usTodoModel));
        return usTodoModel;
    }

}
