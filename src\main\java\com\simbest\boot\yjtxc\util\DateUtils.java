package com.simbest.boot.yjtxc.util;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.codehaus.plexus.util.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class DateUtils {

    private DateUtils() {

    }

    /**
     * 本周周第一天
     */
    public static String getCurrWeekFirstDay() {
        DateTime dt = new DateTime();
        DateTime firstday = dt.dayOfWeek().withMinimumValue();
        return firstday.toString("yyyy-MM-dd");
    }

    /**
     * 本周最后一天
     */
    public static String getCurrWeekLastDay() {
        DateTime dt = new DateTime();
        DateTime lastday = dt.dayOfWeek().withMaximumValue();
        return lastday.toString("yyyy-MM-dd");
    }

    /**
     * 指定日期的周第一天
     */
    public static String getAppointedWeekFirstDay(String dateStr) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dateTime = DateTime.parse(dateStr, df);
        DateTime firstday = dateTime.dayOfWeek().withMinimumValue();
        return firstday.toString("yyyy-MM-dd");
    }

    /**
     * 指定日期的周最后一天
     */
    public static String getAppointedWeekLastDay(String dateStr) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dateTime = DateTime.parse(dateStr, df);
        DateTime lastday = dateTime.dayOfWeek().withMaximumValue();
        return lastday.toString("yyyy-MM-dd");
    }

    /**
     * 指定日期向前增加XX天的日期
     */
    public static String getAppointedDateAfterDay(String dateStr, int days) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dtFirst = DateTime.parse(dateStr, df);
        dtFirst = dtFirst.plusDays(days);
        return dtFirst.toString("yyyy-MM-dd");
    }

    /**
     * 指定日期向后增加XX天的日期
     */
    public static String getAppointedDateBeforDay(String dateStr, int days) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dtFirst = DateTime.parse(dateStr, df);
        dtFirst = dtFirst.minusDays(days);
        return dtFirst.toString("yyyy-MM-dd");
    }

    /**
     * 获取一段时间内的每天的日期
     */
    public static List<String> findDays(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dBegin = null;
        Date dEnd = null;
        try {
            dBegin = sdf.parse(startDate);
            dEnd = sdf.parse(endDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<String> daysStrList = new ArrayList<>();
        daysStrList.add(sdf.format(dBegin));
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(dEnd);
        while (dEnd.after(calBegin.getTime())) {
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            String dayStr = sdf.format(calBegin.getTime());
            daysStrList.add(dayStr);
        }
        return daysStrList;
    }

    /**
     * 根据日期计算当天是周几
     */
    public static String dateToWeek(String datetime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = sdf.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        return weekDays[w];
    }

    /**
     * 获取当前周的上一周的周一
     */
    public static String lastWeekMonday(String datetime) {
        cn.hutool.core.date.DateTime parse = DateUtil.parse(datetime);
        cn.hutool.core.date.DateTime dateTime = DateUtil.offsetDay(parse, -7);
        String date = com.simbest.boot.util.DateUtil.getDate(dateTime);
        String appointedWeekFirstDay = DateUtils.getAppointedWeekFirstDay(date);
        return appointedWeekFirstDay;
    }

    /**
     * 获取当前周的上一周的周日
     */
    public static String lastWeekSunday(String datetime) {
        cn.hutool.core.date.DateTime parse = DateUtil.parse(datetime);
        cn.hutool.core.date.DateTime dateTime = DateUtil.offsetDay(parse, -7);
        String date = com.simbest.boot.util.DateUtil.getDate(dateTime);
        String appointedWeekLastDay = DateUtils.getAppointedWeekLastDay(date);
        return appointedWeekLastDay;
    }

    /**
     * 获取当前周的下一周的周一
     */
    public static String nextWeekMonday(String datetime) {
        cn.hutool.core.date.DateTime parse = DateUtil.parse(datetime);
        cn.hutool.core.date.DateTime dateTime = DateUtil.offsetDay(parse, 7);
        String date = com.simbest.boot.util.DateUtil.getDate(dateTime);
        String appointedWeekFirstDay = DateUtils.getAppointedWeekFirstDay(date);
        return appointedWeekFirstDay;
    }

    /**
     * 获取当前周的下一周的周一
     */
    public static String nextWeekSunday(String datetime) {
        cn.hutool.core.date.DateTime parse = DateUtil.parse(datetime);
        cn.hutool.core.date.DateTime dateTime = DateUtil.offsetDay(parse, 7);
        String date = com.simbest.boot.util.DateUtil.getDate(dateTime);
        String appointedWeekLastDay = DateUtils.getAppointedWeekLastDay(date);
        return appointedWeekLastDay;
    }

    /**
     * 两个时间点的差
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static long DateGap(String startDate, String endDate) {
        Date star = DateUtil.parse(startDate);
        Date end = DateUtil.parse(endDate);
        long gapDate = (int) DateUtil.between(star, end, DateUnit.DAY);
        return gapDate;
    }

    public static Boolean judgeDateIsAm(String Date) {
        java.util.Date date = com.simbest.boot.util.DateUtil.parseCustomDate(Date, "yyyy-MM-dd HH:mm");
        return DateUtil.isAM(date);
    }

    /**
     * 判断日期格式是否正确
     *
     * @param length
     * @param sDate
     * @param format
     * @return
     */
    public static boolean isLegalDate(int length, String sDate, String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据指定日期获得当前月份的第一天
     */
    public static String getMouthFirstDay(String datetime) {
        cn.hutool.core.date.DateTime dt = DateUtil.parse(datetime);
        //获取当前月第一天：
        Calendar ca = Calendar.getInstance();
        ca.setTime(dt);
        // ca.add(Calendar.MONTH, 0); 此方法可以获取前n月或后n月
        ca.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        ca.set(Calendar.HOUR, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        Date time = ca.getTime();
        String date = DateUtil.formatDate(time);
        return date;
    }

    /**
     * 根据指定日期获得当前月份的第一天
     */
    public static String getMouthLastDay(String datetime) {
        cn.hutool.core.date.DateTime dt = DateUtil.parse(datetime);
        //获取当前月最后一天：
        Calendar ca = Calendar.getInstance();
        ca.setTime(dt);
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        ca.set(Calendar.HOUR, 23);
        ca.set(Calendar.MINUTE, 59);
        ca.set(Calendar.SECOND, 59);
        Date time = ca.getTime();
        String date = DateUtil.formatDate(time);
        return date;
    }

    public static String getTime() {
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return sdf.format(d);
    }

    /**
     * 判断是否是过去的日期
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static boolean beforeDates(String date) throws ParseException {
        Date nowDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
        Date date1 = sdf.parse(date);
        //判断指定日期是否在当前日期之前
        return date1.before(nowDate);
    }

    /**
     * 判断当前是否是未来的日期
     *
     * @param date
     * @return
     * @throws ParseException
     */
    public static boolean afterDate(String date) throws ParseException {
        Date nowDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA);
        Date date1 = sdf.parse(date);
        //判断当前日期是否小于指定日期
        return date1.after(nowDate);
    }

    /**
     * 判断选择时间点是否大于当前时间
     *
     * @param remindTime
     * @return
     */
    public static boolean getTimeStatus(String remindTime) {
        Date now = new Date(System.currentTimeMillis());
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTime(now);
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String nowTime = format.format(gc.getTime());
        String[] timeGsonArray = remindTime.split(":");
        String[] nowArray = nowTime.split(":");
        String time = Arrays.stream(timeGsonArray).collect(Collectors.joining());
        String nowTimeString = Arrays.stream(nowArray).collect(Collectors.joining());
        int timeInt = Integer.parseInt(time);
        int nowTimeInt = Integer.parseInt(nowTimeString);
        return timeInt > nowTimeInt;
    }

    /**
     * 将字符串日期转为Date类型
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static Date convertStringToDate(String dateStr, String format) {
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            if (!StringUtils.isBlank(dateStr)) {
                date = sdf.parse(dateStr);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 判断指定日期是否在当前时间之前（今天属于之后）
     *
     * @param startTime
     * @return
     * @throws ParseException
     */
    public static Boolean beforeDate(String startTime) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(System.currentTimeMillis());
        String nowDate = formatter.format(date);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Long diff = format.parse(nowDate).getTime() - format.parse(startTime).getTime();
        System.out.println(diff);
        return diff > 0;
    }

    /**
     * 判断日期时间是否与当前日期时间相等
     *
     * @param remindDate
     * @param remindTime
     * @return
     * @throws ParseException
     */
    public static Boolean judgeEquals(String remindDate, String remindTime) throws ParseException {
        String nowTime = com.simbest.boot.util.DateUtil.getDate(com.simbest.boot.util.DateUtil.getCurrent(), "HH:mm");
        String nowDate = com.simbest.boot.util.DateUtil.getCurrentStr();
        if (StrUtil.equals(remindDate, nowDate) && StrUtil.equals(remindTime, nowTime)) {
            return true;
        } else {
            return false;
        }
    }

    public static Boolean judgeRemindDateAndTime(String DateAndTime) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);

        Date d = sdf.parse(DateAndTime);
        //注意如果日期相同也判断为早于今天
        boolean flag = d.before(com.simbest.boot.util.DateUtil.getCurrent());
       return flag;
    }
}
