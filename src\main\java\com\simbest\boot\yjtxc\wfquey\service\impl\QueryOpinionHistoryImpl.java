package com.simbest.boot.yjtxc.wfquey.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.service.IApplicationFormService;
import com.simbest.boot.yjtxc.wfquey.service.IQueryOpinionHistoryService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Data 2018/06/12
 * @Description 流程意见
 */
@Slf4j
@Service(value = "queryOpinionHistory")
public class QueryOpinionHistoryImpl implements IQueryOpinionHistoryService {

    @Autowired
    private IWfOptMsgService wfOptMsgService;

    @Autowired
    private IApplicationFormService iApplicationFormService;


    /**
     * 查询工单审批意见
     *
     * @param processInstId   流程实例id
     * @param currentUserCode 当前人
     * @return
     */
    @Override
    public List<Map<String, Object>> getWfOptMags(Long processInstId, String currentUserCode) {
        /**查询流程审批意见**/
        Map<String, Object> mapParam = Maps.newHashMap();
        mapParam.put("processInsId", processInstId);
        if (StringUtils.isEmpty(currentUserCode)) {
            mapParam.put("currentUser", SecurityUtils.getCurrentUserName());
        } else {
            mapParam.put("currentUser", currentUserCode);
        }
        List<Map<String, Object>> list = wfOptMsgService.queryProcessOptMsgDataMap(mapParam);
        Iterator<Map<String, Object>> iterator = list.iterator();
        List<Map<String, Object>> linkedList = CollectionUtil.newLinkedList();
        while (iterator.hasNext()){
            Map<String, Object> next = iterator.next();
            Map<String, Object> newMap = new HashMap<>(next);
            String workitemId = MapUtil.getStr(next, "WORKITEMID");
            Map<String, Object> param = iApplicationFormService.findByWorkItemNameWorkitemId(workitemId);
            newMap.put("activityDefId", MapUtil.getStr(param, "workItemName"));
            newMap.put("participant", MapUtil.getStr(param, "participant"));
            newMap.put("partiName", MapUtil.getStr(param, "partiName"));
            newMap.put("createdTime", MapUtil.getStr(param, "createdTime"));
            newMap.put("activityInstName", MapUtil.getStr(param, "activityInstName"));
            linkedList.add(newMap);
        }

        return linkedList;
    }


}
