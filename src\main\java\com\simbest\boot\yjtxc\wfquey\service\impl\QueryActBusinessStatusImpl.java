package com.simbest.boot.yjtxc.wfquey.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.GenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.mapper.ActBusinessStatusMapper;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.bps.process.listener.service.IWfWorkItemModelService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.ObjectUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.wf.unitfytodo.IProcessTodoDataService;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.service.IApplicationFormService;
import com.simbest.boot.yjtxc.apply.service.IEmergencyEquipmentService;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.yjtxc.util.OperateLogTool;
import com.simbest.boot.yjtxc.util.PageTool;
import com.simbest.boot.yjtxc.wfquey.service.IQueryActBusinessStatusService;
import com.simbest.boot.yjtxc.util.OperateLogTool;
import com.simbest.boot.yjtxc.wfquey.service.IQueryActBusinessStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 用途：查询待办已办
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryActBusinessStatus")
public class QueryActBusinessStatusImpl extends GenericService<ActBusinessStatus, Long> implements IQueryActBusinessStatusService {

    @Autowired
    private IProcessTodoDataService processTodoDataService;

    @Autowired
    private ActBusinessStatusMapper actBusinessStatusMapper;


    @Autowired
    private IWfWorkItemModelService wfWorkItemModelService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IApplicationFormService iApplicationFormService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private OperateLogTool operateLogTool;
    @Autowired
    private IEmergencyEquipmentService iEmergencyEquipmentService;

    String param1 = "/action/queryActBusinessStatus";


    /**
     * 我的待办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myTaskToDo(Integer pageindex, Integer pagesize,  String title, String pmInsType,String source, String userCode, String pmInsId,String nameEvent,String licensePlate) {
        Map<String, String> paramMap = Maps.newHashMap();
        Pageable pageable = getPageable(pageindex, pagesize, null, null);
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myTaskToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }

            /**查询待办**/
            paramMap.put("pmInsType", "A,B,C,D,E,F");
            String participant = SecurityUtils.getCurrentUserName();
            paramMap.put("participant", participant);
            StringBuilder baseSql = new StringBuilder("SELECT act.id, " +
                    "       act.business_key, " +
                    "       act.create_org_code, " +
                    "       act.create_org_name, " +
                    "       act.create_time, " +
                    "       act.create_user_code, " +
                    "       act.create_user_id, " +
                    "       act.create_user_name, " +
                    "       act.current_state, " +
                    "       act.duration, " +
                    "       act.enabled, " +
                    "       act.end_time, " +
                    "       act.parent_proc_id, " +
                    "       act.previous_assistant, " +
                    "       act.previous_assistant_date, " +
                    "       act.previous_assistant_name, " +
                    "       act.previous_assistant_org_code, " +
                    "       act.previous_assistant_org_name, " +
                    "       act.process_ch_name, " +
                    "       act.process_def_id, " +
                    "       act.process_def_name, " +
                    "       act.process_inst_id, " +
                    "       act.receipt_code, " +
                    "       act.receipt_title, " +
                    "       act.removed, " +
                    "       act.start_time, " +
                    "       act.update_time, " +
                    "       wk.WORK_ITEM_ID                 as work_Item_Id, " +
                    "       wk.ACTIVITY_DEF_ID              as activity_Def_Id, " +
                    "       wk.ACTIVITY_INST_NAME           as activity_Inst_Name, " +
                    "       wk.participant                  as participant, " +
                    "       wk.assistant                    as assistant, " +
                    "       wk.current_State                as wkCurState, " +
                    "       wk.start_Time                   as workItem_Start_Time, " +
                    "       wk.end_time as work_Item_End_Time, " +
                    "       af.device_configuration, " +
                    "       af.guarantee_start_time, " +
                    "       af.guarantee_end_time, " +
                    "       af.name_event, " +
                    "       af.license_plate, " +
                    "       us.pm_ins_type " +
                    "  FROM act_business_status act, us_pm_instence us, wf_workitem_model wk,us_application_form af " +
                    " WHERE act.PROCESS_INST_ID = wk.PROCESS_INST_ID " +
                    "   and act.BUSINESS_KEY = us.id " +
                    "    and af.pm_ins_id = act.receipt_code and wk.participant = '" + participant + "'");

            if (StrUtil.isNotEmpty(title)) {
                baseSql.append("  and act.RECEIPT_TITLE like concat(concat('%', :title), '%') ");
                paramMap.put("title", title);
            }

            if (StrUtil.isNotEmpty(nameEvent)) {
                baseSql.append("  and af.name_event like concat(concat('%', :nameEvent), '%') ");
                paramMap.put("nameEvent", nameEvent);
            }

            if (StrUtil.isNotEmpty(licensePlate)) {
                baseSql.append("  and af.license_plate like concat(concat('%', :licensePlate), '%') ");
                paramMap.put("licensePlate", licensePlate);
            }

            baseSql.append("   and wk.current_State = 10 " +
                    "   and act.enabled = 1 " +
                    "   and wk.enabled = 1 " +
                    "   and us.enabled = 1 " +
                    "   and af.enabled = 1 " +
                    " order by wk.START_TIME desc ");

            Pageable page = getPageable(pageindex, pagesize, null, null);
            //actBusinessStatuses = (Page<ActBusinessStatus>) processTodoDataService.getTodoByUserNamePage(paramMap, page);
            log.warn(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>输出查询代办sql: 【{}】>>>>>>>>>>参数:【{}】", baseSql.toString(), paramMap);
            List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);
            List<Map<String, Object>> list = com.simbest.boot.util.MapUtil.formatHumpNameForList(resultList);
            if (list.size() > 0) {
                int size = list.size();
                List<Map<String, Object>> listPart = PageTool.pagination(list, pageindex, pagesize);
                return JsonResponse.success(new PageImpl(listPart, pageable, size));
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(new PageImpl(new ArrayList(), pageable, 0));
    }

    /**
     * 我的已办
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyJoin(Integer pageindex, Integer pagesize, String source,String title,  String userCode,String pmInsId,String nameEvent,String licensePlate) {
        Page<ActBusinessStatus> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyJoin";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        Pageable pageable = getPageable(pageindex, pagesize, null, null);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的已办**/

            paramMap.put("pmInsType", "A,B,C,D,E,F");
            String assistant = SecurityUtils.getCurrentUserName();
            paramMap.put("assistant", assistant);
            StringBuilder baseSql = new StringBuilder("select act.*,  " +
                    "       workItem.WORK_ITEM_ID       as work_Item_Id,  " +
                    "       workItem.ACTIVITY_DEF_ID    as activity_Def_Id,  " +
                    "       workItem.ACTIVITY_INST_NAME as activity_Inst_Name,  " +
                    "       workItem.participant        as participant,  " +
                    "       workItem.assistant          as assistant,  " +
                    "       workItem.current_State      as wkCurState,  " +
                    "       workItem.start_Time         as workItem_Start_Time,  " +
                    "       workItem.end_time           as work_Item_End_Time,  " +
                    "       af.device_configuration,  " +
                    "       af.guarantee_start_time,  " +
                    "       af.guarantee_end_time,  " +
                    "       af.name_event,  " +
                    "       af.license_plate,          us.pm_ins_type  " +
                    "  from ACT_BUSINESS_STATUS act,  " +
                    "       US_PM_INSTENCE us,  " +
                    "       (select *  " +
                    "          from wf_workitem_model  " +
                    "         where WORK_ITEM_ID in  " +
                    "               (select max(WORK_ITEM_ID)  " +
                    "                  from wf_workitem_model B  " +
                    "                 where B.enabled = 1  " +
                    "                   and B.assistant = :assistant  " +
                    "                   and b.current_state = 12  " +
                    "                 group by B.Root_Proc_Inst_Id)) workItem,  " +
                    "       us_application_form af  " +
                    " where act.process_inst_id = workItem.process_inst_id  " +
                    "   and act.BUSINESS_KEY = us.id  " +
                    "   and af.pm_ins_id = act.receipt_code  " +
                    "   and act.enabled = 1  " +
                    "   and workItem.enabled = 1  " +
                    "   and us.enabled = 1  " +
                    "   and af.enabled = 1 ");

            if (StrUtil.isNotEmpty(title)) {
                baseSql.append("  and act.RECEIPT_TITLE like concat(concat('%', :title), '%') ");
                paramMap.put("title", title);
            }
            if (StrUtil.isNotEmpty(nameEvent)) {
                baseSql.append("  and af.name_event like concat(concat('%', :nameEvent), '%') ");
                paramMap.put("nameEvent", nameEvent);
            }

            if (StrUtil.isNotEmpty(licensePlate)) {
                baseSql.append("  and af.license_plate like concat(concat('%', :licensePlate), '%') ");
                paramMap.put("licensePlate", licensePlate);
            }

            baseSql.append(" order by workItem.END_TIME desc ");
            List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), paramMap);

            List<Map<String, Object>> list = com.simbest.boot.util.MapUtil.formatHumpNameForList(resultList);
            if (list.size() > 0) {
                int size = list.size();

                List<Map<String, Object>> listPart = PageTool.pagination(list, pageindex, pagesize);
                return JsonResponse.success(new PageImpl(listPart, pageable, size));
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(new PageImpl(new ArrayList(), pageable, 0));
    }

    /**
     * 我的申请
     *
     * @param pageindex
     * @param pagesize
     * @param title
     * @return
     */
    @Override
    public Page<ActBusinessStatus> queryMyApply(Integer pageindex, Integer pagesize, String title) {
        Map<String, String> paramMap = Maps.newHashMap();
        IUser iUser = SecurityUtils.getCurrentUser();
        paramMap.put("participant", iUser.getUsername());
        paramMap.put("title", title);
        return (Page<ActBusinessStatus>) processTodoDataService.getMyCreateDataPage(paramMap, PageRequest.of(pageindex, pagesize, Sort.by(Sort.Direction.DESC, "created_time")));
    }

    /**
     * 我的待阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyPending(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Page<ActBusinessStatus> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyPending";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的待阅**/
            paramMap.put("title", title);

            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            Pageable page = getPageable(pageindex, pagesize, null, null);
            actBusinessStatuses = (Page<ActBusinessStatus>) processTodoDataService.getMyTodoReadByUserNamePage(paramMap, page);
            List<ActBusinessStatus> content = actBusinessStatuses.getContent();
            Iterator<ActBusinessStatus> iterator = content.iterator();
            while (iterator.hasNext()) {
                ActBusinessStatus actBusinessStatus = iterator.next();
                String processType = actBusinessStatus.getReceiptCode().replaceAll("[^a-z^A-Z]", "");
                String pmInsType = "A";
                switch (processType) {
                    case Constants.RCDDSN:
                        pmInsType = "A";
                        break;
                    case Constants.RCDDSJ:
                        pmInsType = "B";
                        break;
                    default:
                        pmInsType = "C";
                        break;
                }
                actBusinessStatus.setPmInsType(pmInsType);
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(actBusinessStatuses);//,( actBusinessStatuses !=null && actBusinessStatuses.getContent().size() > 0 ) ? "操作成功！":"暂无待阅"
    }

    /**
     * 我的已阅
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    是否是从手机端调用
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse queryMyRead(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Page<ActBusinessStatus> actBusinessStatuses = null;
        Map<String, String> paramMap = Maps.newHashMap();
        List<Map<String, Object>> resultsList = CollectionUtil.newArrayList();

        /**准备操作数据**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/queryMyRead";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        Map<String, Object> toMap = CollectionUtil.newHashMap();
        try {
            /**判断是否是从手机端还是PC端**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询我的已阅**/
            paramMap.put("title", title);
            paramMap.put("recipient", SecurityUtils.getCurrentUserName());
            Pageable page = getPageable(pageindex, pagesize, null, null);
            actBusinessStatuses = (Page<ActBusinessStatus>) processTodoDataService.getMyAreadyReadByUserNamePage(paramMap, page);
            List<ActBusinessStatus> content = actBusinessStatuses.getContent();
            Iterator<ActBusinessStatus> iterator = content.iterator();
            while (iterator.hasNext()) {
                ActBusinessStatus actBusinessStatus = iterator.next();
                String processType = actBusinessStatus.getReceiptCode().replaceAll("[^a-z^A-Z]", "");
                String pmInsType = "A";
                switch (processType) {
                    case Constants.RCDDSN:
                        pmInsType = "A";
                        break;
                    case Constants.RCDDSJ:
                        pmInsType = "B";
                        break;
                    default:
                        pmInsType = "C";
                        break;
                }
                actBusinessStatus.setPmInsType(pmInsType);

                String receiptCode = actBusinessStatus.getReceiptCode();
                toMap = (Map<String, Object>) com.simbest.boot.util.MapUtil.objectToMap(actBusinessStatus);
                //根据主单据去查询环节信息
                Map<String, Object> param = iApplicationFormService.findByWorkItemName(receiptCode);
                actBusinessStatus.setActivityInstName(MapUtil.getStr(param, "activityInstName"));
                /*String itemName = MapUtil.getStr(param, "workItemName");
                String participant = MapUtil.getStr(param, "participant");
                String partiName = MapUtil.getStr(param, "partiName");
                String createdTime = MapUtil.getStr(param, "createdTime");
                String activityInstName = MapUtil.getStr(param, "activityInstName");
                toMap.put("activityDefId", itemName);
                toMap.put("participant", participant);
                toMap.put("partiName", partiName);
                toMap.put("createdTime", createdTime);
                toMap.put("activityInstName", activityInstName);
                resultsList.add(toMap);*/
            }

        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
        }
//        return JsonResponse.success(resultsList);
        return JsonResponse.success(actBusinessStatuses);
    }

    /**
     * 查询草稿
     *
     * @param pageindex 页码
     * @param pagesize  数量
     * @param title     标题
     * @param source    来源
     * @param userCode  OA账号
     * @return
     */
    @Override
    public JsonResponse myDraftToDo(Integer pageindex, Integer pagesize, String title, String source, String userCode) {
        Map<String, String> paramMap = Maps.newHashMap();
        Page<Map<String, Object>> draftTodo = null;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/myDraftToDo";
        String params = "pageindex=" + pageindex.toString() + ",pagesize=" + pagesize.toString() + ",title=" + title + ",source=" + source + ",userCode" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**查询草稿**/
            if (!StringUtils.isEmpty(title)) {
                paramMap.put("title", title);
            }
            paramMap.put("createUser", SecurityUtils.getCurrentUserName());
            paramMap.put("pmInsType", "A,B,C,D,E,F,G,H,I,J,K");
            Pageable page = getPageable(pageindex, pagesize, null, null);
            draftTodo = processTodoDataService.getMyApplyPage(paramMap, page);
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**操作日志记录**/
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(draftTodo);
    }

}
