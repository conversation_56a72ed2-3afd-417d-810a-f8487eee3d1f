package com.simbest.boot.yjtxc.todo.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.constants.ApplicationConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * <strong>Title : UsTodoModel</strong><br>
 * <strong>Description : 提交统一待办记录表</strong><br>
 * <strong>Create on : 2018/7/17</strong><br>
 * <strong>Modify on : 2018/7/17</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Data
@EqualsAndHashCode (callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity (name = "us_todo_model")
@ApiModel (value = "提交统一待办记录表")
public class UsTodoModel extends LogicModel {

    @Id
    @Column (name = "id", nullable = false)
    @SequenceGenerator (name="us_todo_model_seq", sequenceName="us_todo_model_seq")
    @GeneratedValue(strategy=GenerationType.AUTO, generator="us_todo_model_seq")
    private Long id;

    @Column(name = "workItemId",nullable = false)
    private String workItemId;                                                           //工作项ID

    @Column(name = "activityDefId",nullable = false)
    private String activityDefId;                                                        //环节定义ID

    @Column(name = "processInstanceId",nullable = false)
    private String processInstanceId;                                                   //流程实例ID

    @Column(name = "businessKey",nullable = false)
    private String businessKey;                                                           //业务流程主键

    @Column(nullable = false)
    private String businessStatusId;                                                      //业务流程状态主键

    @Column(name = "processDefId",nullable = false)
    private String processDefId;                                                        //流程定义名称

    @Column(name = "username",nullable = false)
    private String userName;                                                            //提交人

    @Column(name = "sender",nullable = false)
    private String sender;                                                              //审批人

    @Column(name = "title",nullable = false)
    private String title;                                                               //单据标题

    @Column(name = "oaHtmlUrl",nullable = false)
    private String oaHtmlUrl;                                                           //代办回调的路径

    @Column(name = "urlParams",nullable = false)
    private String urlParams;                                                           //代办回调路径后面带的参数，即url ?后面的数据

    @Column(name = "typeStatus",nullable = false)
    private String typeStatus;                                                         //待办、已办状态

    @Column(name = "workFlag", nullable = false, columnDefinition = "int default 0")
    protected Boolean workFlag;                                                             //是否已办

    @Column(name = "sendFlag", nullable = false, columnDefinition = "int default 0")
    protected Boolean sendFlag;                                                             //是否推送统一代办

    @Column (length = 20)
    @ApiModelProperty(value = "统一待办应用注册ID")
    protected String sysId;

    @Column (length = 200)
    @ApiModelProperty(value = "统一待办应用注册名称")
    protected String sysName;

    @JsonFormat (pattern = ApplicationConstants.FORMAT_DATE_TIME, timezone = ApplicationConstants.FORMAT_TIME_ZONE)
    @JsonDeserialize (using = LocalDateTimeDeserializer.class)
    @JsonSerialize (using = LocalDateTimeSerializer.class)
    @Column(name = "sendDate", nullable = false)
    private LocalDateTime sendDate;                                                              // 推送判定时间,如果延时推送，则是延时推送的判定时间，如果是即时推送，则是推送的时间
}
