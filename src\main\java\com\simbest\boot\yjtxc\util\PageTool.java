package com.simbest.boot.yjtxc.util;

import java.util.ArrayList;
import java.util.List;

/**
 * 用途：分页工具
 * 作者：zsf
 * 时间：2018.09.14
 */
public class PageTool {

    /**
     * 对返回后的列表进行处理后进行分页
     * @param objList 处理后的数据
     * @param pageindex 页码
     * @param pagesize  每页数量
     * @return
     */
    public  static List pagination(List objList, int pageindex, int pagesize) {
        List list = new ArrayList<>();
        int count = 0;
        for (int i = 0;i<objList.size();i++){
            if (i>=(pageindex - 1) * pagesize){
                list.add(objList.get(i));
                count++;
            }
            if (count == pagesize ){
                break;
            }
        }
        return list;
    }
}
