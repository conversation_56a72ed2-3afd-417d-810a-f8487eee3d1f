package com.simbest.boot.yjtxc.msg.service;

import com.simbest.boot.base.service.IGenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;

import java.util.List;
import java.util.Map;

/**
 * @description: 界面短信催办相关业务层接口
 * @author: jing<PERSON><PERSON>
 * @date: 2018/11/29
 */
public interface IShortMessageService extends IGenericService<ActBusinessStatus, Long> {


    /**
     * @description: 短信催办
     * @author: jingwen<PERSON>
     * @date: 2018/11/29
     * @params:
     * @return:
     */
    JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list);


}
