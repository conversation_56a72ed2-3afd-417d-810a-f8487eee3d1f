package com.simbest.boot.yjtxc.mainbills.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;

/**
 * <strong>Title : IPmInstenceService</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface IUsPmInstenceService extends ILogicService<UsPmInstence,String> {

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    int deleteByPmId(Long id);


    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    int deleteByPmIdHexiao(String id);


    /**
     * 根据pmInsId查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    UsPmInstence findByPmInsId(String pmInsId);
}
