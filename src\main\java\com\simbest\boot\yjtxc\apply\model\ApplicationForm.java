package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.sys.model.SysFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/31 15:36
 * @describe 日常应急-战时调度
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_application_form")
@ApiModel(value = "日常应急-战时调度")
public class ApplicationForm extends WfFormModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "af") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID")
    protected String pmInsId;

    @Column(length = 60)
    @ApiModelProperty(value = "工单编号")
    protected String orderNumber;

    @Column(length = 60)
    @ApiModelProperty(value = "申请人姓名")
    protected String userName;

    @Column(length = 60)
    @ApiModelProperty(value = "申请人OA账号")
    protected String trueName;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人所在单位")
    protected String applicantUnit;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人联系方式")
    protected String applicantPhone;

    @Column(length = 100)
    @ApiModelProperty(value = "保障开始时间")
    protected String guaranteeStartTime;

    @Column(length = 100)
    @ApiModelProperty(value = "保障结束时间")
    protected String guaranteeEndTime;

    @Column(length = 100)
    @ApiModelProperty(value = "工单标题")
    protected String orderTitle;

    @Column(length = 100)
    @ApiModelProperty(value = "申请应急设备台数")
    protected String equipmenApplicationsNum;

    @Column(length = 100)
    @ApiModelProperty(value = "保障地市")
    protected String specificCity;

    @Column(length = 100)
    @ApiModelProperty(value = "保障性质")
    protected String securityProperties;

    @Column(length = 100)
    @ApiModelProperty(value = "保障具体地址")
    protected String specificAddress;

    @Column(length = 100)
    @ApiModelProperty(value = "预计人数")
    protected String numberExpected;

    @Column(length = 100)
    @ApiModelProperty(value = "活动名称")
    protected String nameEvent;

    @Column(length = 100)
    @ApiModelProperty(value = "申请应急设备配置")
    protected String deviceConfiguration;

    @Column(length = 100)
    @ApiModelProperty(value = "申请应急设备大类")
    protected String deviceConfigurationParent;

    @Column(length = 100)
    @ApiModelProperty(value = "跨地市-申请应急设备配置")
    protected String kuadeviceConfiguration;


    @Column(length = 1000)
    @ApiModelProperty(value = "事项概要")
    protected String itemOverview;

    @Column(length = 100)
    @ApiModelProperty(value = "保障现场联系人")
    protected String siteContact;

    @Column(length = 100)
    @ApiModelProperty(value = "保障现场联系人电话")
    protected String siteContactPhone;

    @Column(length = 100)
    @ApiModelProperty(value = "设备使用开始时间")
    protected String startTime;

    @Column(length = 100)
    @ApiModelProperty(value = "设备使用结束时间")
    protected String endTime;

    @Column(length = 100)
    @ApiModelProperty(value = "申请应急设备厂家")
    protected String equipmentManufacturer;

    @Column(length = 20)
    @ApiModelProperty(value = "申请类型") //0-地市内调动、1-地市间调动、2战时调度
    protected String applicationType;

    @Column(length = 20)
    @ApiModelProperty(value = "流程类型") //A-地市内调动、B-地市间调动、C-战时调度
    protected String processType;

    @Transient
    @ApiModelProperty(value = "附件")
    protected List<SysFile> attachmentList;

    @Transient
    @ApiModelProperty(value = "大型应急车")
    protected List<EmergencyEquipment> equipmentList;

    @Transient
    @ApiModelProperty(value = "卫星车")
    protected List<EmergencyEquipment> equipmentMoonList;

    @Transient
    @ApiModelProperty(value = "无人机")
    protected List<EmergencyEquipment> equipmentUavList;

    @Transient
    @ApiModelProperty(value = "卫星便携战")
    protected List<EmergencyEquipment> equipmentBleList;



    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

/***-----------------做信息导出，不持久化---------------------------------------*/
@Transient
@ApiModelProperty(value = "保障时间")
protected String baozhangTime;



    @Column(length = 100)
    @ApiModelProperty(value = "车牌号")
    protected String licensePlate;

}
