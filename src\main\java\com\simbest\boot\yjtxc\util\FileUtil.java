package com.simbest.boot.yjtxc.util;

import com.simbest.boot.sys.model.SysFile;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <strong>Title : FileUtil</strong><br>
 * <strong>Description :  </strong><br>
 * <strong>Create on : 2020/5/20</strong><br>
 * <strong>Modify on : 2020/5/20</strong><br>
 * <strong>Copyright SimBest(C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Component
public class FileUtil {

    public static String parsingFileList(List<SysFile> files){

        StringBuilder stringBuilder=new StringBuilder();
        int size = files.size();
        int i=0;
        for (SysFile file : files) {
            i++;
            String id = file.getId();
            stringBuilder.append(id);
            if(i<size){
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();

    }



}