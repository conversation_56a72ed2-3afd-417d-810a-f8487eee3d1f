package com.simbest.boot.yjtxc.util;

import com.simbest.boot.base.service.impl.SystemService;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.service.ISysDictValueService;
import com.simbest.boot.util.security.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @用途:流程表单起草基础信息设置
 * @作者：zsf
 * @时间: 2019/4/16
 */
@Component
public class BaseFieldTool<T extends WfFormModel, PK extends Serializable> extends SystemService<T, PK> {

    @Autowired
    private ISysDictValueService sysDictValueService;

    public T setBaseField(T o) {
        BelongInfoTool.setBelongCompanyAndDepartment(o);
        IUser iuser = SecurityUtils.getCurrentUser();
        o.setBelongOrgCode(iuser.getBelongOrgCode());
        o.setBelongOrgName(iuser.getBelongOrgName());
        o.setBelongCompanyCode(iuser.getBelongCompanyCode());
        o.setBelongCompanyName(iuser.getBelongCompanyName());
        o.setBelongDepartmentCode(iuser.getBelongDepartmentCode());
        o.setBelongDepartmentName(iuser.getBelongDepartmentName());
        o.setBelongCompanyTypeDictValue(iuser.getBelongCompanyTypeDictValue());


        /*  String userName = riskAdmin.getUserCode();
        SimpleUser user = uumsSysUserinfoApi.findByUsername(userName, Constants.APP_CODE);
        o.setProDepartCode(user.getBelongCompanyCodeParent());
        o.setProDepartName(user.getBelongCompanyNameParent());*/
        return o;
    }

}
