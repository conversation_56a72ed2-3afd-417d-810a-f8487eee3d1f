package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.yjtxc.apply.model.FiveBaseStation;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import com.simbest.boot.yjtxc.apply.repository.FourFDDBaseStationRepository;
import com.simbest.boot.yjtxc.apply.service.IFourFDDBaseStationService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:57
 * @describe 4GFDD基站站号
 */
@Slf4j
@Service
public class FourFDDBaseStationServiceImpl extends LogicService<FourFDDBaseStation, String> implements IFourFDDBaseStationService {


    private FourFDDBaseStationRepository repository;

    public FourFDDBaseStationServiceImpl(FourFDDBaseStationRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private ISysOperateLogService sysOperateLogService;


    final String param1 = "action/fourFDDBaseStation/";

    /**
     * 根据PmIdsId删除4GFDD基站站号信息
     *
     * @param pmInsId
     */
    @Override
    public void deleteByFourFDDBaseStationPmInsId(String pmInsId) {
        repository.deleteByPmInsId(pmInsId);
    }

    /**
     * 维护4GFDD基站站号
     *
     * @param fourFDDBaseStation
     * @return
     */
    @Override
    public JsonResponse updateFourFDDBaseStation(FourFDDBaseStation fourFDDBaseStation) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("FourFDDBaseStation", fourFDDBaseStation);
        optLogParam.put("operateInterface", param1 + "updateFiveBaseStation");
        try {
            deleteById(fourFDDBaseStation.getId());
            insert(fourFDDBaseStation);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询4GTDD基站站号
     *
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse findByPmInsIdFourFDDBaseStation(String pmInsId) {

        return JsonResponse.success(repository.findByPmInsIdFourFDDBaseStation(pmInsId));
    }

    /**
     * 维护4GFDD基站站号
     *
     * @param fourFDDBaseStationlist
     * @return
     */
    @Override
    public JsonResponse saveFourFDDBaseStation(List<FourFDDBaseStation> fourFDDBaseStationlist,String pmInsId) {
        List<FourFDDBaseStation> list = CollectionUtil.newArrayList();
        Iterator<FourFDDBaseStation> iterator = fourFDDBaseStationlist.iterator();
        while (iterator.hasNext()){
            FourFDDBaseStation fourFDDBaseStation = iterator.next();
            String fourFddId = fourFDDBaseStation.getFourFddId();
            if (StrUtil.isNotEmpty(fourFddId)) {
                repository.deleteByPmInsId(fourFddId);
            }
            fourFDDBaseStation.setId("");
            list.add(fourFDDBaseStation);
        }

        if (ObjectUtil.isNotEmpty(list)) {
            saveAll(list);
        }else {
            repository.deleteByPmInsId(pmInsId);
        }
        return JsonResponse.success("数据保存成功");
    }
}
