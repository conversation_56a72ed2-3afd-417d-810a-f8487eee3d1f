package com.simbest.boot.yjtxc.util;


/**
 * 用途：生成随机值
 * 作者：tp
 * 时间：2021.09.29
 */
public class RandomTool {


    /**
     * java生成随机数字和字母组合10位数  生成随机数的长度
     * @param length
     * @return
     */
    public static String getRandomNickname(int length) {
        String val = "";
        java.util.Random random = new java.util.Random();
        for (int i = 0; i < length; i++) {
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }
}
