package com.simbest.boot.yjtxc.wfquey.service.impl;

import com.google.common.collect.Maps;
import com.simbest.boot.bps.process.listener.model.WFNotificationInstModel;
import com.simbest.boot.yjtxc.wfquey.service.IQueryProcessHistoryService;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IWFNotificationService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用途：查询审批流程
 * 作者：zhangshaofeng
 * 时间：2018/07/05
 */
@Slf4j
@Service(value = "queryProcessHistory")
public class QueryProcessHistoryImpl  implements IQueryProcessHistoryService {

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private IWFNotificationService wfNotificationService;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;
    /**
     * 查询流转过的工作项
     * @param processInstId 流程实例id
     * @param currentUserCode 当前登录人
     * @return
     */
    @Override
    public Map<String,Object> getWorkItems(Long processInstId, String currentUserCode) {
        Map<String,Object> map = Maps.newHashMap();
        try {
            /**查询审批流程**/
            List<WFNotificationInstModel> wfNotificationInstModels = (List<WFNotificationInstModel>) wfNotificationService.getWFNotificationInstListByProcessInstId(processInstId);
            /**查询抄送流程**/
            Map<String,Object> mapParam = Maps.newHashMap();
            mapParam.put("processInsId", processInstId);
            if (StringUtils.isEmpty( currentUserCode )){
                mapParam.put("currentUser", SecurityUtils.getCurrentUserName());
            }else {
                mapParam.put("currentUser", currentUserCode );
            }
            List<Map<String, Object>> wfWorkItemModels =workItemManager.queryWorkITtemDataMap(mapParam);
            //List<WfWorkItemModel> wfWorkItemModels =  (List<WfWorkItemModel>) workItemManager.queryWorkTtemDataByProInsId( processInstId );
            map.put("approval",wfWorkItemModels);
            map.put("copy",wfNotificationInstModels);
        }catch ( Exception e ){
            log.debug( e.toString() );
        }
        return map;
    }
}
