package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.FourTDDBaseStation;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:53
 * @describe 4GTDD基站站号
 */
public interface FourTDDBaseStationRepository extends LogicRepository<FourTDDBaseStation, String> {


    @Modifying
    @Query(value = " update US_FOUR_T_STATION fbs set fbs.enabled='0',fbs.removed_time=sysdate where fbs.four_tdd_id=:pmInsId ", nativeQuery = true)
    void deleteByPmInsId(@Param("pmInsId") String pmInsId);

    @Query(value = " select * from US_FOUR_T_STATION t where t.enabled='1' and t.four_tdd_id=:pmInsId ", nativeQuery = true)
    List<FourTDDBaseStation> findByPmInsIdFourTDDBaseStation(@Param("pmInsId") String pmInsId);
}
