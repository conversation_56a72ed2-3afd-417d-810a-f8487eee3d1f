package com.simbest.boot.yjtxc.util.filter;

import com.google.common.collect.Maps;
import com.simbest.boot.yjtxc.util.BpsConfig;
import com.simbest.boot.util.encrypt.RsaEncryptor;
import com.simbest.boot.wf.login.WorkFlowBpsLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;


@Slf4j
@Component
@ServletComponentScan
@WebFilter(urlPatterns = {"/action/applicationForm/api/startSubmitProcess",
        "/action/currencyFrom/startSubmitProcess",
        "/action/anonymous/startSubmitProcess",
        "/action/applicationForm/api/startSubmitProcess",
        "/action/applicationForm/deleteArchive/api",
        "/action/applicationForm/deleteArchive",
        "/action/applicationForm/findByRoleAndUser",
        "/action/applicationForm/startSubmitProcess",
        "/action/anonymous/bps/workItemlistener/*",
        "/action/anonymous/bps/processlistener/*"}, filterName = "bpsRequestFilter")
public class BpsRequestFilter implements Filter {

    @Autowired
    private BpsConfig bpsConfig;

    @Autowired
    private WorkFlowBpsLoginService workFlowBpsLoginService;

    @Autowired
    private RsaEncryptor rsaEncryptor;

    @Override
    public void init ( FilterConfig filterConfig ) throws ServletException {
        log.debug("BpsRequestFilter>>>>>>>>init>>>>>BPS过滤器初始化>>>>>>" );
    }

    @Override
    public void doFilter ( ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain ) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        log.debug("BpsRequestFilter>>>>>>>>doFilter>>>>>"+ httpServletRequest.getRequestURI());
        String reqStr = httpServletRequest.getQueryString();
        String currentUser = "";
        String[] reqList = reqStr.split( "&" );
        for (String str : reqList){
            if (str.contains("currentUserCode")){
                currentUser = str.substring(str.indexOf("=")+1);
            }
        }
        log.debug("BpsRequestFilter>>>>>>>>doFilter>>>>>reqStr>>>"+reqStr);
        log.debug("BpsRequestFilter>>>>>>>>doFilter>>>>>currentUser>>>"+currentUser);
        String userName = "";
        if( currentUser.length() > 50 ){
            userName = rsaEncryptor.decrypt( URLDecoder.decode(currentUser,"utf-8")  );
        }else {
            userName = currentUser;
        }
        log.debug("BpsRequestFilter>>>>>>>>doFilter解密后>>>>>userName>>>"+userName);
        boolean bpsTenant = Boolean.valueOf(bpsConfig.bpsTenant);
        String bpsTenantId = bpsConfig.bpsTenantId;
        Map<String,Object> map = Maps.newConcurrentMap();
        map.put("tenant", bpsTenant );
        map.put("userName",  userName );
        map.put("tenantId", bpsTenantId );
        workFlowBpsLoginService.bpsLogin( map );
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy ( ) {
        log.debug("BpsRequestFilter>>>>>>>>init>>>>>BPS过滤器销毁>>>>>>" );
    }
}
