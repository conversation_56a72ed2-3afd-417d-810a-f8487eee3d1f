package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.GenericRepository;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.InvolvesPersonnelConfiguration;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 16:29
 * @describe 流程固化涉及人员配置持久层
 */
public interface InvolvesPersonnelConfigurationRepositort extends LogicRepository<InvolvesPersonnelConfiguration, String> {

    /**
     * 根据公司code查询对应的责任经理
     * @param cities 公司code
     * @return
     */
    @Query(
            value = " select distinct t.appt_username_vehicle from us_personnel_config t where t.enabled = 1 and t.app_cities_code in :cities ", nativeQuery = true
    )
    List<String> getCompanyVehicle(@Param("cities") List<String> cities);
}
