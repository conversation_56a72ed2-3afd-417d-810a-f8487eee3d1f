package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 14:34
 * @describe 4GFDD基站站号
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_four_f_station")
@ApiModel(value = "4GFDD基站站号")
public class FourFDDBaseStation extends LogicModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ffs") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID")  //应急车库配置关联ID
    protected String fourFddId;

    @Column(length = 100)
    @ApiModelProperty(value = "LTEFDD设备厂家")
    protected String lteFddManufacturer;

    @Column(length = 100)
    @ApiModelProperty(value = "BBU型号")
    protected String bbuModel;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板型号1")
    protected String basebandBoardModelOne;

    @Column(length = 40)
    @ApiModelProperty(value = "基带板型号2")
    protected String basebandBoardModelTwo;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板数量1")
    protected String basebandBoardNumberOne;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板数量2")
    protected String basebandBoardNumberTwo;

    @Column(length = 100)
    @ApiModelProperty(value = "RRU型号1")
    protected String rruModelOne;

    @Column(length = 100)
    @ApiModelProperty(value = "rru数量1")
    protected String rruNumberOne;

    @Column(length = 100)
    @ApiModelProperty(value = "RRU型号2")
    protected String rruModelTwo;

    @Column(length = 100)
    @ApiModelProperty(value = "rru数量2")
    protected String rruNumberTwo;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;


}
