package com.simbest.boot.yjtxc.task;

import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.component.distributed.lock.AppRuntimeMaster;
import com.simbest.boot.component.task.AbstractTaskSchedule;

import com.simbest.boot.sys.repository.SysTaskExecutedLogRepository;
import cn.hutool.core.date.DateUtil;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.repository.ApplicationFormRepository;
import com.simbest.boot.yjtxc.apply.service.IApplicationFormService;
import com.simbest.boot.yjtxc.apply.service.IEmergencyEquipmentService;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import com.simbest.boot.yjtxc.mainbills.repository.UsPmInstenceRepository;
import com.simbest.boot.yjtxc.todo.TodoBusOperatorService;
import com.simbest.boot.yjtxc.util.BpsLoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 2.2.3.吉祥号审批规则接口
 * 接口内容：用于省侧上报本省吉祥号销售审批规则信息。
 * 接口名称：吉祥号销售审批规则文件接口
 * 接口版本号：1.0
 * 文件传输协议：sFTP
 * 上传频次：每月8日凌晨1点前全量更新
 **/

@Component
@Slf4j
public class MainTask extends AbstractTaskSchedule {

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private BpsLoginUtil bpsLoginUtil;

    @Autowired
    private IApplicationFormService applicationFormService;

    @Autowired
    private IEmergencyEquipmentService emergencyEquipmentService;

    @Autowired
    private ApplicationFormRepository applicationFormRepository;

    @Autowired
    private UsPmInstenceRepository pmInstenceRepository;

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    public MainTask(AppRuntimeMaster appRuntime, SysTaskExecutedLogRepository repository) {
        super(appRuntime, repository);
    }

    @Override
    @Scheduled(cron = "${main.task}")
    public void checkAndExecute() {
        super.checkAndExecute(true);
    }

    @Override
    public String execute() {
        log.warn("MainTask: 【{}】", "定时器开始执行");
        loginUtils.adminLogin();

        try{
            Date currentDate = new Date();
            String currentTimestamp = DateUtil.format(currentDate,"yyyy-MM-dd HH:mm:ss");
            List<ApplicationForm> applicationForms = applicationFormRepository.getspecial01(currentTimestamp); //符合时间且符合环节
            for (ApplicationForm item : applicationForms) {
                UsPmInstence pmInstence = pmInstenceRepository.findByPmInsId(item.getPmInsId());
                //查询wf表涉及的workItemId
                List<Map<String, Object>> wfList = applicationFormRepository.getspecial02(item.getPmInsId());
                for (Map<String, Object> wfItem : wfList) {
                    bpsLoginUtil.bpsLogin(String.valueOf(wfItem.get("PARTICIPANT"))); //模拟登录
                    //手动流转
                    long ret = applicationFormService.processApproval(Long.valueOf(String.valueOf(wfItem.get("WORK_ITEM_ID"))), String.valueOf(wfItem.get("PARTICIPANT")),
                            null, null, "end", String.valueOf(wfItem.get("ACTIVITY_DEF_ID")), "归档", pmInstence);
                    if (ret > 0) {
                        //核销统一待办
                        ActBusinessStatus actBusinessStatus = new ActBusinessStatus();
                        actBusinessStatus.setBusinessKey(pmInstence.getId());
                        actBusinessStatus.setProcessInstId(Long.parseLong(String.valueOf(wfItem.get("PROCESS_INST_ID"))));
                        actBusinessStatus.setWorkItemId(Long.parseLong(String.valueOf(wfItem.get("WORK_ITEM_ID"))));
                        actBusinessStatus.setReceiptTitle(String.valueOf(wfItem.get("RECEIPT_TITLE")));
                        todoBusOperatorService.closeTodo(actBusinessStatus, String.valueOf(wfItem.get("PARTICIPANT")));
                    }
                }
                //查询所有设备,并设置当前时间为最终结束时间
                List<EmergencyEquipment> emmList = emergencyEquipmentService.findByPmInsId(item.getPmInsId());
                for (EmergencyEquipment emm : emmList) {
                    try {
                        if (StringUtils.isNotEmpty(emm.getArchiveTime())) {
                            String endTime = emm.getArchiveTime();
                            String nowTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                            LocalDateTime endTimeLocal = LocalDateTime.parse(endTime, formatter);
                            LocalDateTime nowTimeLocal = LocalDateTime.parse(nowTime, formatter);
                            if (endTimeLocal.isAfter(nowTimeLocal)) {
                                emm.setArchiveTime(nowTime);
                                emergencyEquipmentService.update(emm);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.warn("修改归档时间异常: 【{}】", e.getMessage());
                    }
                }
            }
        }catch (Exception e){
            log.warn("MainTask定时器执行异常: 【{}】", e.getMessage());
        }

        return null;
    }
}
