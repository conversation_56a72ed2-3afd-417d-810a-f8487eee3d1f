package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.DateUtil;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.yjtxc.apply.dto.ParameterQuery;
import com.simbest.boot.yjtxc.apply.dto.UsageofVehicleQuery;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.model.UsageofVehicle;
import com.simbest.boot.yjtxc.apply.repository.ApplicationFormRepository;
import com.simbest.boot.yjtxc.apply.repository.EmergencyEquipmentRespository;
import com.simbest.boot.yjtxc.apply.repository.UsageofVehicleRepository;
import com.simbest.boot.yjtxc.apply.service.IUsageofVehicleService;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.yjtxc.util.DateUtils;
import com.simbest.boot.yjtxc.util.FileTool;
import com.simbest.boot.yjtxc.util.PageTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 车辆使用情况
 */
@Slf4j
@Service
public class UsageofVehicleServiceImpl extends LogicService<UsageofVehicle, String> implements IUsageofVehicleService {
    private UsageofVehicleRepository usageofVehicleRepository;

    final String param1 = "/action/emergencyEquipment/";

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    @Autowired
    private ApplicationFormRepository applicationFormRepository;

    @Autowired
    private EmergencyEquipmentRespository emergencyEquipmentRespository;

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    public UsageofVehicleServiceImpl(UsageofVehicleRepository usageofVehicleRepository) {
        super(usageofVehicleRepository);
        this.usageofVehicleRepository = usageofVehicleRepository;
    }

    /**
     * 查询车辆使用情况
     *
     * @param page
     * @param size
     * @param paramMap
     * @return
     */
    @Override
    public JsonResponse findByParameterQuery(int page, int size, Map<String, Object> paramMap) {
        Page<SchedulingLedger> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");

        try {
            if (page == 0) {
                page = StrUtil.isNotEmpty(MapUtil.getStr(paramMap, "page")) ? Integer.parseInt(MapUtil.getStr(paramMap, "page")) : 1;
            }
            if (size == 0) {
                size = StrUtil.isNotEmpty(MapUtil.getStr(paramMap, "size")) ? Integer.parseInt(MapUtil.getStr(paramMap, "size")) : 99;
            }
            String chooseTime = null;
            //获取车辆归属地
            String cities = cn.hutool.core.map.MapUtil.getStr(paramMap, "cities");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //应急车配置类型
            String carConfiguRation = MapUtil.getStr(paramMap, "carConfiguRation");
            //车牌号
            String licensePlate = MapUtil.getStr(paramMap, "licensePlate");
            //查询时间
            chooseTime = MapUtil.getStr(paramMap, "chooseTime");
            //调度状态
            String schedule = MapUtil.getStr(paramMap, "schedule");

            Map<String, Object> params = CollectionUtil.newHashMap();

            //StringBuilder basqSql = new StringBuilder(" select * " +
            //        "  from (select a.id, " +
            //        "               a.license_plate, " +
            //        "               a.cities, " +
            //        "               a.equipment_manufacturer, " +
            //        "               a.car_configu_ration, " +
            //        "               a.enabled, " +
            //        "               af.name_event, " +
            //        "               af.guarantee_start_time, " +
            //        "               af.guarantee_end_time, " +
            //        "               ea.car_cities, " +
            //        "               decode(af.pm_ins_id, null, '可调度', '调度中') as schedule, " +
            //        "               decode(af.pm_ins_id, null, 0, 1) as display_order " +
            //        "          from us_uav_confi a " +
            //        "          left join us_emergency_equipment ea " +
            //        "            on ea.car_id = a.id " +
            //        "           and ea.enabled = 1 " +
            //        "           and :chooseTime between ea.start_time and ea.archive_time " +
            //        "          left join us_application_form af " +
            //        "            on af.pm_ins_id = ea.ee_id " +
            //        "           and af.enabled = 1 " +
            //        "        union all " +
            //        "        select b.id, " +
            //        "               b.license_plate, " +
            //        "               b.cities, " +
            //        "               b.equipment_manufacturer, " +
            //        "               b.car_configu_ration, " +
            //        "               b.enabled, " +
            //        "               af.name_event, " +
            //        "               af.guarantee_start_time, " +
            //        "               af.guarantee_end_time, " +
            //        "               ea.car_cities, " +
            //        "               decode(af.pm_ins_id, null, '可调度', '调度中') as schedule, " +
            //        "               decode(af.pm_ins_id, null, 0, 1) as display_order " +
            //        "          from us_satellite_portable b " +
            //        "          left join us_emergency_equipment ea " +
            //        "            on ea.car_id = b.id " +
            //        "           and ea.enabled = 1 " +
            //        "           and :chooseTime between ea.start_time and ea.archive_time " +
            //        "          left join us_application_form af " +
            //        "            on af.pm_ins_id = ea.ee_id " +
            //        "           and af.enabled = 1 " +
            //        "        union all " +
            //        "        select c.id, " +
            //        "               c.license_plate, " +
            //        "               c.cities, " +
            //        "               c.equipment_manufacturer, " +
            //        "               c.car_configu_ration, " +
            //        "               c.enabled, " +
            //        "               af.name_event, " +
            //        "               af.guarantee_start_time, " +
            //        "               af.guarantee_end_time, " +
            //        "               ea.car_cities, " +
            //        "               decode(af.pm_ins_id, null, '可调度', '调度中') as schedule, " +
            //        "               decode(af.pm_ins_id, null, 0, 1) as display_order " +
            //        "          from us_garage_confi c " +
            //        "          left join us_emergency_equipment ea " +
            //        "            on ea.car_id = c.id " +
            //        "           and ea.enabled = 1 " +
            //        "           and :chooseTime between ea.start_time and ea.archive_time " +
            //        "          left join us_application_form af " +
            //        "            on af.pm_ins_id = ea.ee_id " +
            //        "           and af.enabled = 1) t " +
            //        " where t.enabled = 1 ");

            StringBuilder basqSql = new StringBuilder(" select *\n" +
                    "  from (select a.id,\n" +
                    "               a.license_plate,\n" +
                    "               a.cities,\n" +
                    "               a.equipment_manufacturer,\n" +
                    "               a.car_configu_ration,\n" +
                    "               a.enabled,\n" +
                    "               af.name_event,\n" +
                    "               af.guarantee_start_time,\n" +
                    "               af.guarantee_end_time,\n" +
                    "               ea.car_cities,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  '调度中'\n" +
                    "                 else\n" +
                    "                  '可调度'\n" +
                    "               end) as schedule,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  1\n" +
                    "                 else\n" +
                    "                  0\n" +
                    "               end) as display_order\n" +
                    "          from us_uav_confi a\n" +
                    "          left join us_emergency_equipment ea\n" +
                    "            on ea.car_id = a.id\n" +
                    "           and ea.enabled = 1\n" +
                    "           and :chooseTime between ea.start_time and ea.archive_time\n" +
                    "          left join us_application_form af\n" +
                    "            on af.pm_ins_id = ea.ee_id\n" +
                    "           and af.enabled = 1\n" +
                    "        union all\n" +
                    "        select b.id,\n" +
                    "               b.license_plate,\n" +
                    "               b.cities,\n" +
                    "               b.equipment_manufacturer,\n" +
                    "               b.car_configu_ration,\n" +
                    "               b.enabled,\n" +
                    "               af.name_event,\n" +
                    "               af.guarantee_start_time,\n" +
                    "               af.guarantee_end_time,\n" +
                    "               ea.car_cities,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  '调度中'\n" +
                    "                 else\n" +
                    "                  '可调度'\n" +
                    "               end) as schedule,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  1\n" +
                    "                 else\n" +
                    "                  0\n" +
                    "               end) as display_order\n" +
                    "          from us_satellite_portable b\n" +
                    "          left join us_emergency_equipment ea\n" +
                    "            on ea.car_id = b.id\n" +
                    "           and ea.enabled = 1\n" +
                    "           and :chooseTime between ea.start_time and ea.archive_time\n" +
                    "          left join us_application_form af\n" +
                    "            on af.pm_ins_id = ea.ee_id\n" +
                    "           and af.enabled = 1\n" +
                    "        union all\n" +
                    "        select c.id,\n" +
                    "               c.license_plate,\n" +
                    "               c.cities,\n" +
                    "               c.equipment_manufacturer,\n" +
                    "               c.car_configu_ration,\n" +
                    "               c.enabled,\n" +
                    "               af.name_event,\n" +
                    "               af.guarantee_start_time,\n" +
                    "               af.guarantee_end_time,\n" +
                    "               ea.car_cities,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  '调度中'\n" +
                    "                 else\n" +
                    "                  '可调度'\n" +
                    "               end) as schedule,\n" +
                    "               (case\n" +
                    "                 when af.pm_ins_id is not null and ea.END_STATE is null then\n" +
                    "                  1\n" +
                    "                 else\n" +
                    "                  0\n" +
                    "               end) as display_order\n" +
                    "          from us_garage_confi c\n" +
                    "          left join us_emergency_equipment ea\n" +
                    "            on ea.car_id = c.id\n" +
                    "           and ea.enabled = 1\n" +
                    "           and :chooseTime between ea.start_time and ea.archive_time\n" +
                    "          left join us_application_form af\n" +
                    "            on af.pm_ins_id = ea.ee_id\n" +
                    "           and af.enabled = 1) t\n" +
                    " where t.enabled = 1 ");


            //判断车辆归属地市是否为空
            if (StrUtil.isNotEmpty(cities)) {
                basqSql.append(" and t.cities like concat(concat('%',:cities),'%') ");
                params.put("cities", cities);
            }

            //判断设备厂家是否为空
            if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                basqSql.append(" and t.equipment_manufacturer like concat(concat('%',:equipmentManufacturer),'%') ");
                params.put("equipmentManufacturer", equipmentManufacturer);
            }

            //判断车牌号是否为空
            if (StrUtil.isNotEmpty(licensePlate)) {
                basqSql.append(" and t.license_plate like concat(concat('%',:licensePlate),'%') ");
                params.put("licensePlate", licensePlate);
            }

            //调度状态
            if (StrUtil.isNotEmpty(schedule)) {
                basqSql.append(" and t.schedule = :schedule ");
                params.put("schedule", schedule);
            }

            //应急车配置类型
            if (StrUtil.isNotEmpty(carConfiguRation)) {
                basqSql.append(" and t.car_configu_ration = :carConfiguRation ");
                params.put("carConfiguRation", carConfiguRation);
            }

            if (StrUtil.isEmpty(chooseTime)) {
                chooseTime = DateUtil.getDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            }
            params.put("chooseTime", chooseTime);

            basqSql.append( " order by t.display_order desc, t.guarantee_start_time, t.guarantee_end_time, t.id ")  ;
            List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(basqSql.toString(), params);
            List<Map<String, Object>> list = com.simbest.boot.util.MapUtil.formatHumpNameForList(resultList);

            if (list.size() > 0) {
                int row = list.size();
                Pageable pageable1 = getPageable(page, size, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(list, page, size);
                return JsonResponse.success(new PageImpl(listPart, pageable1, row));
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);

    }

    @Override
    public void exportOrder(HttpServletRequest request, HttpServletResponse response, EmergencyEquipment emergencyEquipment) {


        String vehicleConfiguration = emergencyEquipment.getVehicleConfiguration();
        //获取车牌号
        String licensePlate = emergencyEquipment.getLicensePlate();
        //应急车配置类型
        String carConfiguRation = emergencyEquipment.getCarConfiguRation();
        //获取设备
        String equipmentManufacturer = emergencyEquipment.getEquipmentManufacturer();
        //获取归属地
        String cities = emergencyEquipment.getCities();
        //获取开始时间
        String guaranteeStartTime = emergencyEquipment.getStartTime();
        //获取结束时间
        String guaranteeEndTime = emergencyEquipment.getEndTime();

        Map<String, Object> params = CollectionUtil.newHashMap();
        params.put("vehicleConfiguration", vehicleConfiguration);
        params.put("licensePlate", licensePlate);
        params.put("carConfiguRation", carConfiguRation);
        params.put("equipmentManufacturer", equipmentManufacturer);
        params.put("cities", cities);
        params.put("guaranteeStartTime", guaranteeStartTime);
        params.put("guaranteeEndTime", guaranteeEndTime);

        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "车辆使用情况.xls";
        List<Map<String, Object>> formList = this.findByParameterQuery(params);
        List<UsageofVehicleQuery> list = CollectionUtil.newArrayList();
        for (Map<String, Object> map : formList) {
            UsageofVehicleQuery form = creatModel(map);
            list.add(form);
        }
        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<UsageofVehicleQuery> exportUtil = new ExcelUtil<UsageofVehicleQuery>(UsageofVehicleQuery.class);
            exportUtil.exportExcel(list, "车辆使用情况", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    public List<Map<String, Object>> findByParameterQuery(Map<String, Object> paramMap) {

        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");

        try {
            //获取车辆归属地
            String cities = cn.hutool.core.map.MapUtil.getStr(paramMap, "cities");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //应急车配置类型
            String carConfiguRation = MapUtil.getStr(paramMap, "carConfiguRation");
            //车牌号
            String licensePlate = MapUtil.getStr(paramMap, "licensePlate");
            //开始使用时间
            String guaranteeStartTime = MapUtil.getStr(paramMap, "guaranteeStartTime");
            //结束使用时间
            String guaranteeEndTime = MapUtil.getStr(paramMap, "guaranteeEndTime");
            Map<String, Object> params = CollectionUtil.newHashMap();
            StringBuilder basqSql = new StringBuilder("select t.*, " +
                    " aa.guarantee_start_time ," +
                    " aa.guarantee_end_time ," +
                    "aa.name_event" +
                    " from US_EMERGENCY_EQUIPMENT t , US_APPLICATION_FORM aa " +
                    " WHERE" +
                    " t.enabled='1'" +
                    " and aa.enabled='1'" +
                    " and t.ee_id = aa.order_number");
            //判断车辆归属地市是否为空

            if (StrUtil.isNotEmpty(cities)) {
                basqSql.append(" and t.cities like:cities");
                params.put("cities", "%" + cities + "%");
            }
            //判断设备厂家是否为空
            if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                basqSql.append(" and t.equipment_manufacturer like:equipmentManufacturer");
                params.put("equipmentManufacturer", "%" + equipmentManufacturer + "%");
            }
            //判断应急车配置类型是否为空
            if (StrUtil.isNotEmpty(carConfiguRation)) {
                basqSql.append(" and t.car_configu_ration=:carConfiguRation");
                params.put("carConfiguRation", carConfiguRation);
            }

            //判断车牌号是否为空
            if (StrUtil.isNotEmpty(licensePlate)) {
                basqSql.append(" and t.license_plate like" + "'%" + licensePlate + "%'");
                // params.put("licenseNumber", "%" + licenseNumber + "%");
            }

            //判断开始时间是否为空
            if (StrUtil.isNotEmpty(guaranteeStartTime)) {
                basqSql.append("  and aa.guarantee_start_time>=:guaranteeStartTime");
                params.put("guaranteeStartTime", guaranteeStartTime);
            }
            //判断结束时间是否为空
            if (StrUtil.isNotEmpty(guaranteeEndTime)) {
                basqSql.append("  and aa.guarantee_end_time<=:guaranteeEndTime");
                params.put("guaranteeEndTime", guaranteeEndTime);
            }

            mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList(customDynamicWhere.queryNamedParameterForList(basqSql.toString(), paramMap));
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return mapList;

    }

    private UsageofVehicleQuery creatModel(Map<String, Object> map) {
        UsageofVehicleQuery usageofVehicleQuery = new UsageofVehicleQuery();
        //应急车配置类型
        String carConfiguRation = MapUtil.getStr(map, "carConfiguRation");
        //车牌号
        String licensePlate = MapUtil.getStr(map, "licensePlate");
        //车辆归属地
        String placeOwnership = MapUtil.getStr(map, "cities");
        //设备厂家
        String equipmentManufacturer = MapUtil.getStr(map, "equipmentManufacturer");
        //活动名称
        String nameEvent = MapUtil.getStr(map, "nameEvent");
        //保障开始时间
        String guaranteeStartTime = MapUtil.getStr(map, "guaranteeStartTime");
        //保障结束时间
        String guaranteeEndTime = MapUtil.getStr(map, "guaranteeEndTime");
        //调度状态
        String schedulingCondition = MapUtil.getStr(map, "schedulingCondition");
        //出车地市
        String carCities = MapUtil.getStr(map, "carCities");

        usageofVehicleQuery.setCarConfiguRation(carConfiguRation);
        usageofVehicleQuery.setLicensePlate(licensePlate);
        usageofVehicleQuery.setPlaceOwnership(placeOwnership);
        usageofVehicleQuery.setEquipmentManufacturer(equipmentManufacturer);
        usageofVehicleQuery.setNameEvent(nameEvent);
        usageofVehicleQuery.setGuaranteeStartTime(guaranteeStartTime);
        usageofVehicleQuery.setGuaranteeEndTime(guaranteeEndTime);
        usageofVehicleQuery.setSchedulingCondition(schedulingCondition);
        usageofVehicleQuery.setCarCities(carCities);


        return usageofVehicleQuery;


    }
}
