package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;

import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:56
 * @describe 4GFDD基站站号
 */
public interface IFourFDDBaseStationService  extends ILogicService<FourFDDBaseStation, String> {
    /**
     * 根据PmIdsId删除4GFDD基站站号信息
     * @param pmInsId
     */
    void deleteByFourFDDBaseStationPmInsId(String pmInsId);

    /**
     * 维护4GFDD基站站号
     * @param fourFDDBaseStation
     * @return
     */
    JsonResponse updateFourFDDBaseStation(FourFDDBaseStation fourFDDBaseStation);

    /**
     * 查询4GTDD基站站号
     * @param pmInsId
     * @return
     */
    JsonResponse findByPmInsIdFourFDDBaseStation(String pmInsId);

    /**
     * 维护4GFDD基站站号
     * @param fourFDDBaseStationlist
     * @param pmInsId
     * @return
     */
    JsonResponse saveFourFDDBaseStation(List<FourFDDBaseStation> fourFDDBaseStationlist,String pmInsId);
}
