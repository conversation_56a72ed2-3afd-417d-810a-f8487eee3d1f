package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.SimpleAppDecision;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:33
 * @describe 日常应急-地市内调动
 */
public interface IApplicationFormService  extends ILogicService<ApplicationForm, String> {



    /**
     * 打开详情办理
     *
     * @param processInstId
     * @param workFlag
     * @param source
     * @param pmInsId
     * @param location
     * @param currentUserCode
     * @return
     */
    JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String pmInsId, String location, String currentUserCode);

    /**
     * 查询决策
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @return
     */
    JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String applyType);


    /**
     * 根据决策查找人员
     *
     * @param processInstId  流程实例id
     * @param sysAppDecision 决策对象
     * @param source         来源
     * @param userCode       用户OA账户
     * @return
     */
    JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision, String localhost,String phone,String licensePlate, String belongCompanyCode);


    /**
     * 提交下一步
     *
     * @param currentUserCode 当前登录人
     * @param workItemId      活动项id
     * @param outcome         连线规则
     * @param location        当前环节
     * @param copyLocation    抄送下一环节
     * @param bodyParam       流程西下一步参数
     * @param formId          表单id
     * @param notificationId  待阅表单id
     * @return
     * @throws Exception
     */
    JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId, String processInstId);


    /**
     * 根据id查询
     * @param pmInsId
     * @return
     */
    ApplicationForm getFormDetailByPmInsId(String pmInsId);

    /**
     * 查询组织树
     *
     * @return
     */
    JsonResponse queryOrgTree();

    /**
     * 工单编号产生
     * @return
     */
    String creatWorkNumber(String value,String processType);

    /**
     * 根据起草类型查询车辆
     * @param cities
     * @param carType
     * @param processType
     * @return
     */
    JsonResponse findByCar(String cities, String carType,String processType);

    /**
     * 工单查询
     * @param page
     * @param size
     * @param params
     * @return
     */
    JsonResponse findByCount(int page,int size, Map<String, Object> params);

    /**
     * 根据主单据去查询环节信息
     * @param receiptCode
     * @return
     */
    Map<String, Object> findByWorkItemName(String receiptCode);

    /**
     * 根据流程实例ID查询环节信息
     * @param processInsId
     * @return
     */
    Map<String, Object> findByWorkItemNameProcessInsId(String processInsId);

    /**
     * 根据workitemId查询环节信息
     * @param workitemId
     * @return
     */
    Map<String, Object> findByWorkItemNameWorkitemId(String workitemId);


    /**
     * 工单信息导出工单查询
     * @param request
     * @param response
     * @param applicationForm
     */
    void exportOrder(HttpServletRequest request, HttpServletResponse response, ApplicationForm applicationForm);

    /**
     * 根据起草类型查询车辆
     * @param params
     * @return
     */
    JsonResponse findByCarTime(Map<String, Object> params);

    /**
     * 查询所有车辆类型（不过滤carType）
     * @param params
     * @return
     */
    JsonResponse findAllCarTypes(Map<String, Object> params);

    /**
     * 核销流程
     * @param processInstId
     * @param applicationForm
     * @param pmInsId
     * @return
     */
    JsonResponse deleteProcess(Long processInstId, ApplicationForm applicationForm, String pmInsId);


    /**
     * 手动核销统一代办
     * @param processInstId
     * @param applicationForm
     */
    void deleteNtodoDate(Long processInstId, ApplicationForm applicationForm);

    /**
     * 根据主单据查询流程类型
     * @param receiptCode
     * @return
     */
    String findByApplicationForm(String receiptCode);

    /**
     * 模糊查询
     * @param receiptCode
     * @param nameEvent
     * @param licensePlate
     * @return
     */
    ApplicationForm findAallFormDetailByPmInsId(String receiptCode, String nameEvent, String licensePlate);

     long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence);

}
