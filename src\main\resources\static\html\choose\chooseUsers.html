﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>组织人员树</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
</head>
<body class="page_body">
<ul id="orgTree"></ul> 
<div class="role orgC"></div>
<script type="text/javascript">
	var gps=getQueryString();
	$(function(){
		treeLoadSuccess();
		$("#orgTree").tree({
			url:web.rootdir+"uums/sys/userinfo/findOneStep?appcode="+web.appCode,
			//checkbox:true,//是否在每一个借点之前都显示复选框
			lines:true,//是否显示树控件上的虚线	
			treePid:'parentId',
			queryParams:{"appCode":web.appCode},
			contentType: "application/json; charset=utf-8",
			//cascadeCheck:false,
			//onlyLeafCheck:true,
			onlyone:gps.multi==0?true:false,//不要乱配
            fileds:'id,parentId,name|text,treeType',
			animate:true,//节点在展开或折叠的时候是否显示动画效果
			onClick:function(node){
				if(node.treeType=="org"){
					if(node.children){
						if(node.children.length==0) top.mesAlert("提示信息","该组织无下级组织！", 'info');
					}else{
						ajaxgeneral({
							url:"uums/sys/userinfo/findOneStep?appcode="+web.appCode+"&orgCode="+node.id,
							data:{"appCode":web.appCode,"orgCode":node.id},
							contentType: "application/json; charset=utf-8",
							success:function(data){
								if(data.data.length==0){
									top.mesAlert("提示信息","该组织无下级数据！", 'info');
								}else{
									for(var i in data.data){
										data.data[i].text=data.data[i].name;
									}
									$("#orgTree").tree("append", { 
										parent : node.target, 
										data : data.data
									});
								}
							}
						});
					}
				}
			},
			onBeforeSelect:function(node){
				if(node.treeType=="org") return false;
				if(gps.multi==0){
					var nodes=$("#orgTree").tree("getChecked");
					for(var i in nodes){
						//var nodei=$("#orgTree").tree("find",nodes[i].id);
						$("#orgTree").tree("uncheck",nodes[i].target);									
					}
					$(".role").html("");
				}
				if(!(getObjects("id,name",node.id+","+node.name))) $(".role").append("<a name='"+node.name+"' id='"+node.id+"'><font>"+node.name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");	
			}
		});
		$(document).on("click",".role a i",function(){
			$(this).parent("a").remove();
		});
	});
	//是否有该条数据
	function getObjects(idas,keyas){
		var a=false;
		var ids=idas.split(",");
		var idkeys=keyas.split(",");
		$(".role a").each(function(i,v){	
			var b=0;
			for(var i in ids){			
				if ($(v).attr(idkeys[i]) == ids[i]) {
					b++;
				}
			}
			if(b==ids.length) {
				a=true;
				return false;
			}
		});
		return a;
	}; 
	//数据加载成功
	function treeLoadSuccess(){
	    if(gps.isReview == 1) {
            var chooseRow=top.chooseWeb[gps.name]?top.chooseWeb[gps.name].data:[];
            for(var i in chooseRow){
                if(!(getObjects("id,name",chooseRow[i].id+","+chooseRow[i].name))) $(".role").append("<a name='"+chooseRow[i].name+"' id='"+chooseRow[i].id+"'><font>"+chooseRow[i].name+"</font><i class='iconfont fr'>&#xe6ef;</i></a>");
            }
        }
	};
	window.getchoosedata=function(){
		var datas=[];
		$(".role a").each(function(i,v){
			var data={};
			data.id=$(v).attr("id");
			data.name=$(v).children("font").html();
			datas.push(data);
		});
		return {"data":datas,"state":1};//state一般用于是否必选(必选时state为１表示选择有内容，为０表示没有选择，可以return {"data":[],"state":0};之前加弹出提示)因为当前不是必选所以state为１
	};
</script>
</body>
</html>
