package com.simbest.boot.yjtxc.msg.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.msg.service.IShortMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**短消息接口
 * @Auther: ztz
 * @Date: 2020/5/22 10:12
 * @Description:
 */
@Api(description = "短消息接口 ")
@Slf4j
@RestController
@RequestMapping(value = "/action/sendMsg")
public class ShortMessageController {

    @Autowired
    private IShortMessageService shortMessageService;

    /**
     * 发送催办短信
     *
     * @param source          来源
     * @param currentUserCode 用户账号
     * @param list
     * @return
     */
    @ApiOperation(value = "发送催办短信", notes = "发送催办短信")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "source", value = "来源", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentUserCode", value = "用户账号", dataType = "String", paramType = "query")})
    @PostMapping(value = {"/sendShortMessage", "/api/sendShortMessage", "/sendShortMessage/sso"})
    public JsonResponse sendShortMessage(@RequestParam String source,
                                         @RequestParam(required = false) String currentUserCode,
                                         @RequestBody List<Map<String, Object>> list) {
        return shortMessageService.sendShortMessage(source, currentUserCode, list);
    }
}
