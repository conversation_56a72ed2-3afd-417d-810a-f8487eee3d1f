package com.simbest.boot.yjtxc.apply.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.InvolvesPersonnelConfiguration;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 16:26
 * @describe 流程固化涉及人员配置
 */
public interface IInvolvesPersonnelConfigurationService extends ILogicService<InvolvesPersonnelConfiguration, String> {


    /**
     * 流程固化涉及人员配置
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    JsonResponse findByAllInvolvesPersonnelConfiguration(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap);

    /**
     * 导入流程固化涉及人员配置信息
     * @param request
     * @param response
     * @return
     */
    JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response);

    /**
     * 批量保存流程固化涉及人员配置信息
     * @param involvesPersonnelConfigurationsList
     * @return
     */
    JsonResponse saveInvolvesPersonnelConfiguration(List<InvolvesPersonnelConfiguration> involvesPersonnelConfigurationsList);

    /**
     * 根据公司code查询对应的责任经理
     * @param belongCompanyCodes 分公司code
     * @return
     */
    List<String> getCompanyVehicle(List<String> belongCompanyCodes);

    InvolvesPersonnelConfiguration edit(InvolvesPersonnelConfiguration map);
}
