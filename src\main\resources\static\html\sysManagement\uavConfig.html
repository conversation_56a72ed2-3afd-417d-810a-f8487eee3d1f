<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#uavTable",
                "querycmd":"action/uavAerialConfigurationStation/findByAlluavAerialConfigurationStation?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "frozenColumns":[],
                "columns":[
                    [
                        { title: "地市", field: "cities", width: 150, rowspan: 2, align: "center"},
                        { title: "重保分队装备", width: 150,tooltip:true,colspan: 5, align: "center" },
                        { title: "调度状态", field: "schedulingCondition", width: 150,tooltip:true, rowspan: 2, align: "center" },
                        { title: "操作", field: "opt", width: 150, rowspan: 2, align: "center",
                            formatter: function(value, row, index) {
                                return "<a href='#' id='"+row.id+"' onclick='editList(this)'>修改</a>" + " " +
                                       "<a href='#' onclick='deleteList(this)' id='"+row.id+"'>删除</a>"
                            }
                        }
                    ],
                    [

                        { title: "设备厂家", field: "equipmentManufacturer", width: 80,tooltip:true, align: "center" },
                        { title: "无人机高空站配置库编号", field: "licensePlate", width: 100,tooltip:true, align: "center" },
                        { title: "无人机高空站配置类型", field: "carConfiguRation", width: 100,tooltip:true, align: "center" },
                        { title: "无人机高空基站负责人", field: "uavStationLeader", width: 100,tooltip:true, align: "center" },
                        { title: "负责人手机号", field: "uavStationLeaderPhone", width: 80,tooltip:true, align: "center" }

                    ]
                ]
            }
        };

        $(function(){
            loadGrid(pageparam);
            $(".addUav").click(function() {
                top.dialogP("html/sysManagement/uavAddForm.html", window.name, "卫星便携站配置", "addCB", false, 600, 400)
            })
            $(".import").click(function() {
                top.dialogP("html/sysManagement/import.html?type=uav", window.name, "模板导入", "importCB", false, "maximized", "maximized")
            })
            $(".downLoad").click(function () {
                $("#downloadForm").attr("action", web.rootdir + "action/uavAerialConfigurationStation/downloadTemplate");
                $("#downloadForm .downloadSubmit").trigger("click");
            })
            $(".resetForm").click(function() {
                formreset("uavTableQueryForm")
                $(".searchtable").trigger("click")
            })
        });
        function editList(param) {
            top.dialogP("html/sysManagement/uavAddForm.html?id="+$(param).attr("id"), window.name, "卫星便携站配置", "addCB", false, 600, 400)
        }
        function addCB(data) {
            var param = [data.data]
            if(data.data.id) {
                param = data.data
            }
            ajaxgeneral({
                url: "action/uavAerialConfigurationStation/"+(data.data.id ? "updateUavAerialConfigurationStation" : "saveUavAerialConfigurationStation"),
                data: param,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#uavTable").datagrid("reload")
                }
            })
        }
        function importCB(data) {
            ajaxgeneral({
                url: "action/uavAerialConfigurationStation/saveUavAerialConfigurationStation",
                data: data.data,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#uavTable").datagrid("reload")
                }
            })
        }
        function deleteList(param) {
            $.messager.confirm("温馨提示！", "确认删除吗？", function(r) {
                if(r) {
                    ajaxgeneral({
                        url: "action/uavAerialConfigurationStation/deleteBySatellitePortableLibrary?id="+$(param).attr("id"),
                        success: function(data) {
                            $("#uavTable").datagrid("reload")
                        }
                    })
                }
            })
        }

    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="uavTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">地市：</td>
            <td width="150">
                <input name="cities" type="text" value="" />
            </td>
            <td width="90" align="right">设备厂家：</td>
            <td width="150">
                <input name="equipmentManufacturer" type="text" value="" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                    <a class="btn fl ml15 a_success resetForm"><font>重置</font></a>
                </div>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fr ml15 a_success addUav"><font>添加</font></a>
                    <a class="btn fr ml15 a_warning import"><font>模板导入</font></a>
                    <a class="btn fr a_success downLoad"><font>模板下载</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="uavTable"><table id="uavTable"></table></div>
<form id="downloadForm" class="hide" method="post">
    <input type="submit" class="downloadSubmit"/>
</form>
</body>
</html>
