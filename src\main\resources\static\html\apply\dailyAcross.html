<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
	<title>日常应急-跨地市调动</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<style>
		.splite {
			border-left: 3px solid rgb(57, 174, 245);
			padding-left: 15px;
			font-size: 16px;
			font-weight: 600;
		}

		.myTitle {
			text-align: center;
			font-size: 20px;
			position: relative;
		}

		.chooseCarHide {
			display: none;
		}
		/* /// */
		.demandValue {
			width: 100%;
			margin-top: 20px;
			padding: 0 8px;
			box-sizing: border-box;
		}

		.involve {
			margin: 20px 0;
		}

		.involve span {
			display: inline-block;
			width: 100px;
		}

		.demandValue span {
			margin-right: 20px;
		}

		.demandValue label {
			cursor: pointer;
			margin-right: 30px;
			position: relative;
		}

		.demandValue label i {
			display: block;
			position: absolute;
			right: -15px;
			top: 5px;
			background: #999;
			border-radius: 50%;
			width: 14px;
			height: 14px;
			font-size: 12px;
			color: #fff;
			text-indent: 3px;
			line-height: 15px;
		}
	</style>
	<script type="text/javascript">
		getCurrent()
		var carTableA = [];
		var carTableB = [];
		var carTableC = [];
		var carTableD = [];
		// 新增应急设备管理表格数据
		var emergencyEquipmentTable = [];
		var startTime=''
		var endTime=''
		// var myEditorDate = {
		// 	type: 'datebox',
		// 	options: {
		// 		onShowPanel : function(){
		// 			$(this).datebox('calendar').calendar({
		// 				validator :function(date){
		// 					var date1 = new Date(new Date(startTime)-(1000*60*60*24));
		// 					var date2 = new Date(endTime);
		// 					return (date1<=date && date<=date2);
		// 				}
		// 			});
		// 		}
		// 	}
		// };
		var myEditorDate = {
			type: 'datetimebox',
			options: {
				showSeconds: false,
				onShowPanel: function(){
					var startDate = new Date(new Date(startTime)-(1000*60*60*24)); // 开始日期时间
					var endDate = new Date(endTime); // 结束日期时间
					$(this).datetimebox('calendar').calendar({
						validator: function(date){
						return (startDate <= date && date <= endDate);
						}
					});
				}
			}
		};
		var currentPhone=web.currentUser.preferredMobile
		// $.extend($.fn.validatebox.defaults.rules, {
		// 	beforeDateCheck: {//validType="beforeDateCheck"
		// 		validator: function (value, param) {
		// 			var nowDate = param;
		// 			var chooseData = new Date(value.replace(new RegExp("-", 'g'), "/"));
		// 			return nowDate <= chooseData;
		// 		},
		// 		message: "不能选择当前之前的时间"
		// 	},
		// 	afterDateCheck: {//validType="afterDateCheck"
		// 		validator: function (value, param) {
		// 			var nowDate = param;
		// 			var chooseData = new Date(value.replace(new RegExp("-", 'g'), "/"));
		// 			return nowDate >= chooseData;
		// 		},
		// 		message: "不能选择当前之后的时间"
		// 	},
		// })
		function carCols() {
			return [
				{ title: "调用应急设备地市", field: "cities", width: 150, align: "center" },
				{ title: "应急设备分类", field: "carConfiguRation", width: 120, align: "center" },
				{ title: "应急设备子类", field: "carConfiguRationParent", width: 120, align: "center" },
				{ title: "调用应急设备车牌号", field: "licensePlate", width: 150, align: "center" },
				{ title: "调用应急设备联系人", field: "openingStation", width: 150, align: "center" },
				{ title: "调用应急设备联系电话", field: "openingStationPhone", width: 170, align: "center" },

				// <input id="guaranteeEndTime" name="guaranteeEndTime" type="text" class="easyui-datetimebox guaranteeEndTime"
				// 	   required="required" validType="startDateCheck['guaranteeEndTime','guaranteeStartTime']"
				// 	   data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;" />
			]
		}
		function uavCols(param) {
			return [
				{ title: "地市", field: "cities", width: 150, align: "center" },
				{ title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center" },
				{ title: param.title, field: param.field, width: 150, align: "center" },
				{ title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center" }
			]
		}
		function loadTable(cols) {
			$(".carTable").empty()
			$(".carTable").append($("<table>").attr("id", "carTable"))
			if (gps.location == "yjtxc.coordinatingDispatching") {
				cols.push({
					title: "使用开始时间111", field: "startTime", width: 150, align: "center",
					formatter: function (value, row, index) {
						if (gps.type == "task" && gps.location == "yjtxc.coordinatingDispatching") {
							var phoneType = row.theDriverPhone ? row.theDriverPhone : row.uavStationLeaderPhone
							if (phoneType.split(",")[0] == web.currentUser.preferredMobile) {
								return '<input type="text" index="' + index + '" field="startTime" class="easyui-datebox start' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.startTime + '" style="width:100%;height:32px;"/>'
							}
						}
						return value
					}
				})
				cols.push({
					title: "使用结束时间", field: "endTime", width: 150, align: "center",
					formatter: function (value, row, index) {
						if (gps.type == "task" && gps.location == "yjtxc.coordinatingDispatching") {
							var phoneType = row.theDriverPhone ? row.theDriverPhone : row.uavStationLeaderPhone
							if (phoneType.split(",")[0] == web.currentUser.preferredMobile) {
								return '<input type="text" index="' + index + '" field="endTime" class="easyui-datebox end' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.endTime + '" style="width:100%;height:32px;"/>'
							}
						}
						return value
					}
				})
			}
			if (gps.location == "yjtxc.schedulingChiefAudit") {
				cols.push({
					title: "操作", field: "opt", width: 50, align: "center",
					formatter: function (value, row, index) {
						return "<a href='#' index='" + index + "' onclick='deleteInfo(this)'>删除</a>"
					}
				})
			}
			loadGrid({
				"listtable": {
					"listname": "#carTable",
					"data": { data: { rows: rows, total: rows.length } },
					"nowrap": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [cols],
					"onLoadSuccess": function () {
						var startHorizon = $("#guaranteeStartTime").datebox("getValue")
						var endHorizon = $("#guaranteeEndTime").datebox("getValue")
						setTimeout(function () {
							dateHorizon("easyuidate", startHorizon, endHorizon)
						}, 1500)
						$(".easyuidate").datebox({
							onSelect: function (data) {
								rows[$(this).attr("index")][$(this).attr("field")] = getTimeDate(data, "yyyy-MM-dd")
								if ($(this).attr("field") == "startTime") {
									dateHorizon("end" + $(this).attr("index"), getTimeDate(data, "yyyy-MM-dd"), endHorizon)
								} else {
									dateHorizon("start" + $(this).attr("index"), startHorizon, getTimeDate(data, "yyyy-MM-dd"))
								}
							}
						})
						$("#carTable").datagrid("resize")
					}
				}
			})
		}
		var rows = []
		function initProcess() {
			var param = {
				"htmlName": window.name,
				"formId": "dailyLocalForm",
				"processNextCmd": "action/applicationForm/startSubmitProcess",
				"processDeleteCmd": "action/applicationForm/deleteProcess",
				"processNextLoading":true,
				"processDraftDeleteCmd": "action/applicationForm/deleteDraft",
				"processNextBeforeSubmit": function (data) {
					if (data.decisionId == "yjtxc.coordinatingDispatching_To_schedulingChiefAudit") {
						if (data.formData.equipmentList.length == 0) {
							return top.mesShow("温馨提示！", "请至少添加一个应急设备！", 2000, "yellow")
						}
					}
					return data
				}
			};
			loadProcess(param);
			if (gps.type == "workOrder") {
				$(".pageInfo").hide()
				$(".body_page").css("padding-top", 0)
			}
			if (!gps.location) {
				$(".userName").val(web.currentUser.truename)
				$(".trueName").val(web.currentUser.username)
				$(".applicantUnit").val(web.currentUser.belongCompanyName)
				$(".applicantPhone").val(web.currentUser.preferredMobile)
			}
			if (!gps.location || gps.location == "yjtxc.start") {
				$(".guaranteeStartTime").datebox({
					onSelect: function (data) {
						dateHorizon("guaranteeEndTime", getTimeDate(data, "yyyy-MM-dd"))
						rows = []
						$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
					}
				})
				$(".guaranteeEndTime").datebox({
					onSelect: function (data) {
						dateHorizon("guaranteeStartTime", "0001-01-01", getTimeDate(data, "yyyy-MM-dd"))
						rows = []
						$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
					}
				})
			}
			// if (gps.location == "yjtxc.schedulingChiefAudit" || gps.location == 'yjtxc.networkDepartment' || gps.location == 'yjtxc.coordinatingDispatching') {

			// }

			// 控制应急设备管理表格的显示 - 只在特定流程节点显示
			if (gps.location == "yjtxc.coordinatingDispatching" ||
				gps.location == "yjtxc.schedulingChiefAudit" ||
				gps.location == "yjtxc.networkDepartment") {
				$(".chooseCarHide").show()
				console.log('当前流程节点：' + gps.location + '，显示应急设备管理表格');
			} else {
				$(".chooseCarHide").hide()
				console.log('当前流程节点：' + gps.location + '，隐藏应急设备管理表格');
			}
		}
		$(function () {
			initProcess()
			// 控制保障时间只显示时分
			$('#guaranteeEndTime').datetimebox({
			  showSeconds: false,
			  showMillis: false
			});
			$('#guaranteeStartTime').datetimebox({
			  showSeconds: false,
			  showMillis: false
			});

			// 动态加载申请应急设备配置选项
			if(!gps.location){
				loadDeviceConfigurationOptions();
			}

			// 初始化应急设备管理表格
			loadEmergencyEquipmentTable();

			// 调试：检查按钮是否存在
			setTimeout(function() {
				var btnCount = $('.selectEmergencyEquipmentBtn').length;
				console.log('找到选择应急设备按钮数量：', btnCount);
				if (btnCount > 0) {
					console.log('按钮元素：', $('.selectEmergencyEquipmentBtn')[0]);
					console.log('按钮是否可见：', $('.selectEmergencyEquipmentBtn').is(':visible'));
				}
			}, 1000);

			// 绑定选择应急设备按钮点击事件
			$(document).on('click', '.selectEmergencyEquipmentBtn', function() {
				console.log('选择应急设备按钮被点击');

				// 验证必填时间字段
				var startTime = $("#guaranteeStartTime").datebox("getValue");
				if (!startTime) {
					top.mesShow("温馨提示！", "请先选择保障开始时间！", 2000, "red");
					return;
				}

				var endTime = $("#guaranteeEndTime").datebox("getValue");
				if (!endTime) {
					top.mesShow("温馨提示！", "请先选择保障结束时间！", 2000, "red");
					return;
				}

				// 获取选中的申请应急设备配置
				var selectedDeviceConfigurations = [];
				$("input[name='checkbox1']:checked").each(function() {
					selectedDeviceConfigurations.push($(this).val());
				});

				// 验证是否选择了应急设备配置
				if (selectedDeviceConfigurations.length === 0) {
					top.mesShow("温馨提示！", "请先选择申请应急设备配置！", 2000, "red");
					return;
				}

				console.log('选中的应急设备配置：', selectedDeviceConfigurations);

				// 调用接口获取应急设备数据
				var requestParams = {
					cities: web.currentUser.belongCompanyName,
					processType: "B", // 跨地市调度
					startTime: startTime,
					endTime: endTime,
					carConfiguRation: "",
					carConfiguRationParent: selectedDeviceConfigurations.join(','), // 将选中的设备配置用逗号连接
					pmInsId: $("#pmInsId").val()
				};


				$.ajax({
					url: web.rootdir + "action/applicationForm/findAllCarTypes",
					type: "POST",
					contentType: "application/json; charset=utf-8",
					data: JSON.stringify(requestParams),
					success: function(response) {
						console.log('接口调用成功：', response);
						if (response && response.errcode === 0) {
							var equipmentData = response.data || [];
							console.log('设备数据数量：', equipmentData.length);
							if (equipmentData.length > 0) {
								console.log('第一个设备数据结构：', equipmentData[0]);
								console.log('设备数据字段：', Object.keys(equipmentData[0]));
							}
							var startTimes = $("#guaranteeStartTime").datebox("getValue")
							var endTimes = $("#guaranteeEndTime").datebox("getValue")
							equipmentData =	equipmentData.map(ele => {
								return {
									...ele,
									startDate: startTimes,
									endDate: endTimes
								}
							})
							// 成功获取数据，使用现有的弹窗函数
							showEmergencyEquipmentDialog(equipmentData);
						} else {
							var errorMsg = response.errmsg || "获取应急设备数据失败";
							top.mesShow("错误", errorMsg, 3000, "red");
						}
					},
					error: function(xhr, status, error) {
						console.error('调用findAllCarTypes接口失败：', error);
						top.mesShow("错误", "网络请求失败，请检查网络连接！", 3000, "red");
					}
				});
			});

			if(gps.location){
				// $('.tooltip').css('display','none')
				$('.tooltip').removeAttr('style')
			}

			// 页面加载完成后，再次确保按钮可见
			setTimeout(function() {
				var btn = $('.selectEmergencyEquipmentBtn');
				if (btn.length > 0) {
					btn.css({
						'display': 'inline-block !important',
						'visibility': 'visible !important',
						'opacity': '1 !important'
					});
					console.log('页面加载完成，按钮已强制显示');
				} else {
					console.log('页面加载完成，但未找到按钮');
				}
			}, 2000);
		});

		// 动态加载申请应急设备配置选项
		function loadDeviceConfigurationOptions(callback) {
			ajaxgeneral({
				url: "action/garageConfiguration/getCarConfiguRationParentList",
				type: "POST",
				contentType: "application/json; charset=utf-8",
				success: function(response) {
					try {
						if (response && response.errcode === 0 && response.data) {
							var container = $('#deviceConfigurationContainer');
							container.empty(); // 清空现有内容
							var readOnlyis = $('#equipmenApplicationsNum')[0].readOnly
							// 遍历返回的数据数组，动态生成复选框
							for (var i = 0; i < response.data.length; i++) {
								var item = response.data[i];
								if (item && item.trim() !== '') {
									var dataType = 'val'; // 默认类型
									if (item === '大型应急车') {
										dataType = 'other';
									} else if (item === '卫星车') {
										dataType = 'none';
									}
									if (readOnlyis) {
										var checkboxHtml = '<label>' +
												'<input type="checkbox" disabled="disabled" style="width: 16px;" name="checkbox1" data-type="' + dataType + '" value="' + item + '" noReset="true" />' + item +
												'</label>';
									}else {
										var checkboxHtml = '<label>' +
												'<input type="checkbox" style="width: 16px;" name="checkbox1" data-type="' + dataType + '" value="' + item + '" noReset="true" />' + item +
												'</label>';
									}


									container.append(checkboxHtml);
									if(callback && typeof callback === 'function') {
										callback();
									}
								}
							}

							console.log('申请应急设备配置选项加载成功');
						} else {
							console.error('获取申请应急设备配置数据失败：', response);
							// 如果接口调用失败，使用默认选项
							loadDefaultDeviceConfigurationOptions();
						}
					} catch (e) {
						console.error('处理申请应急设备配置数据时出错：', e);
						// 如果处理数据时出错，使用默认选项
						loadDefaultDeviceConfigurationOptions();
					}
				},
				error: function(xhr, status, error) {
					console.error('调用申请应急设备配置接口失败：', error);
					// 如果接口调用失败，使用默认选项
					loadDefaultDeviceConfigurationOptions();
				}
			});
		}

		// 加载默认的申请应急设备配置选项（作为备用方案）
		function loadDefaultDeviceConfigurationOptions() {
			var container = $('#deviceConfigurationContainer');
			container.empty();

			var defaultOptions = [
				{value: '大型应急车', dataType: 'other'},
				{value: '卫星车', dataType: 'none'},
				{value: '无人机高空站', dataType: 'val'},
				{value: '卫星便携站', dataType: 'val'}
			];
			for (var i = 0; i < defaultOptions.length; i++) {
				var option = defaultOptions[i];
				var checkboxHtml = '<label>' +
					'<input type="checkbox" style="width: 16px;" name="checkbox1" data-type="' + option.dataType + '" value="' + option.value + '" noReset="true" />' + option.value +
					'</label>';

				container.append(checkboxHtml);
			}

			console.log('使用默认申请应急设备配置选项');
		}

		// 显示应急设备选择弹窗
		function showEmergencyEquipmentDialog2(equipmentData) {
			console.log('显示应急设备选择弹窗，数据数量：', equipmentData.length);

			// 构建弹窗URL参数
			var startTime = $("#guaranteeStartTime").datebox("getValue");
			var endTime = $("#guaranteeEndTime").datebox("getValue");
			var pmInsId = $("#pmInsId").val();

			// 将设备数据存储到全局变量中，供弹窗使用
			window.emergencyEquipmentData = equipmentData;

			// 打开选择应急设备弹窗
			top.dialogP("html/apply/chooseCar.html?processType=B" +
				"&city=" + encodeURI(web.currentUser.belongCompanyName) +
				"&startTime=" + startTime +
				"&endTime=" + endTime +
				"&pmInsId=" + pmInsId +
				"&dataSource=emergency",
				window.name, "选择应急设备", "selectEmergencyEquipmentCallback", false, 1100, 700);
		}

		// 应急设备选择回调函数
		function selectEmergencyEquipmentCallback(data) {
			console.log('应急设备选择回调：', data);
			if (data && data.data && data.data.length > 0) {
				// 将选中的设备添加到应急设备管理表格
				var selectedEquipment = data.data;
				var guaranteeStartTime = $("#guaranteeStartTime").datebox("getValue");
				var guaranteeEndTime = $("#guaranteeEndTime").datebox("getValue");

				for (var i = 0; i < selectedEquipment.length; i++) {
					var equipment = selectedEquipment[i];
					// 检查是否已存在
					var exists = false;
					for (var j = 0; j < emergencyEquipmentTable.length; j++) {
						if (emergencyEquipmentTable[j].id === equipment.id) {
							exists = true;
							break;
						}
					}
					if (!exists) {
						// 为新设备设置默认的开始和结束时间
						equipment.startDate = equipment.startDate || guaranteeStartTime;
						equipment.endDate = equipment.endDate || guaranteeEndTime;
						// 同步给后端识别的字段名
						equipment.startTime = equipment.startDate;
						equipment.endTime = equipment.endDate;
						emergencyEquipmentTable.push(equipment);
					}
				}

				// 刷新应急设备管理表格
				$("#emergencyEquipmentTable").datagrid("loadData", {
					data: { rows: emergencyEquipmentTable, total: emergencyEquipmentTable.length }
				});

				console.log('已添加设备到应急设备管理表格，当前总数：', emergencyEquipmentTable.length);
			}
		}

		// 原有的loadCarTable函数已移除，统一使用应急设备管理表格
		function loadCarTable(param) {
			// 不再使用分类表格，统一使用应急设备管理表格
			console.log('loadCarTable函数已废弃，统一使用应急设备管理表格');
		}



		// 初始化应急设备管理表格
		function loadEmergencyEquipmentTable() {
			var emergencyEquipmentParam = {
				"listtable": {
					"listname": "#emergencyEquipmentTable",
					"data": { data: { rows: emergencyEquipmentTable, total: emergencyEquipmentTable.length } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [[
						{ title: "调用应急设备地市", field: "cities", width: 150, align: "center" },
						{ title: "应急设备分类", field: "carConfiguRationParent", width: 120, align: "center" },
						{ title: "应急设备子类", field: "carConfiguRation", width: 120, align: "center" },
						{ title: "调用应急设备车牌号", field: "licensePlate", width: 150, align: "center" },
						{ title: "调用应急设备联系人", field: "openingStation", width: 150, align: "center" },
						{ title: "调用应急设备联系电话", field: "openingStationPhone", width: 170, align: "center" },
					]]
				}
			};
			if(gps.location == "yjtxc.coordinatingDispatching"){
				emergencyEquipmentParam.listtable.columns[0].push({
					title: "使用开始时间",
					field: "startDate",
					width: 135,
					fixed: true,
					align: "center",
					formatter: function (value, row, index) {
						// 获取当前行的开始时间值，如果没有则使用保障开始时间
						var currentValue = row.startTime || $('#guaranteeStartTime').datebox("getValue");
						var openingStationList = row.openingStation ?row.openingStation.split(','):[];
						var startFlag = false;
						// 匹配当前人员
						openingStationList.forEach(item=>{
							if(item == web.currentUser.truename){
								startFlag = true;
							}
						})
						// 创建可编辑的时间输入框
						if(gps.type == 'task' && startFlag){
							return `<input type="text" index="${index}" field="startDate"
								class="easyui-datetimebox equipment-start-time-${index}"
								data-options="panelHeight:'auto',editable:false,showSeconds:false"
								value="${currentValue}"
								style="width:100%;height:32px;"/>`;

						}else{
							return currentValue
						}
						if(gps.type == 'join'){
							return currentValue
						}
						
					}
				});

				// 添加使用结束时间列
				emergencyEquipmentParam.listtable.columns[0].push({
					title: "使用结束时间",
					field: "endDate",
					width: 135,
					fixed: true,
					align: "center",
					formatter: function (value, row, index) {
						// 获取当前行的结束时间值，如果没有则使用保障结束时间
						var currentValue = row.endTime || $('#guaranteeEndTime').datebox("getValue");
						var openingStationList = row.openingStation ?row.openingStation.split(','):[];
						var startFlag = false;
						// 匹配当前人员
						openingStationList.forEach(item=>{
							if(item == web.currentUser.truename){
								startFlag = true;
							}
						})
						if(gps.type == 'task' && startFlag){
							// 创建可编辑的时间输入框
							return `<input type="text" index="${index}" field="endDate"
									class="easyui-datetimebox equipment-end-time-${index}"
									data-options="panelHeight:'auto',editable:false,showSeconds:false"
									value="${currentValue}"
									style="width:100%;height:32px;"/>`;
						}else{
							return currentValue
						}
						if(gps.type == 'join'){
							return currentValue
						}
					}
				});

			}
			

			// 始终添加删除操作列
			if(gps.type == 'task'){
				emergencyEquipmentParam.listtable.columns[0].push({
				title: "操作", field: "opt", width: 80, align: "center",
				formatter: function (value, row, index) {
					return "<a href='#' index='" + index + "' onclick='deleteEmergencyEquipment(this)' style='color: #d9534f; text-decoration: none;'>删除</a>"
				}
			});

			}
			

			// 添加表格加载成功后的回调处理
			emergencyEquipmentParam.listtable.onLoadSuccess = function() {
				if(gps.location == "yjtxc.coordinatingDispatching"){
					initEquipmentTimeFields();

				}
			};

			loadGrid(emergencyEquipmentParam);
			$("#emergencyEquipmentTable").datagrid("resize");
		}

		// 初始化应急设备表格中的时间字段
		function initEquipmentTimeFields() {
			// 获取保障时间范围
			var guaranteeStartTime = $("#guaranteeStartTime").datebox("getValue");
			var guaranteeEndTime = $("#guaranteeEndTime").datebox("getValue");

			// 为每个时间输入框初始化datetimebox并绑定事件
			$(".emergencyEquipmentTable .easyui-datetimebox").each(function() {
				var $this = $(this);
				var index = $this.attr("index");
				var field = $this.attr("field");

				// 初始化datetimebox，增加范围与先后顺序校验（同时监听 onSelect 与 onChange）
				$this.datetimebox({
					showSeconds: false,
					editable: false,
					panelHeight: 'auto',
					onSelect: function(date){
						applyValidation(getTimeDate(date, 'yyyy-MM-dd hh:mm'), emergencyEquipmentTable[index][field] || '');
					},
					onChange: function(newVal, oldVal) {
						applyValidation(newVal, oldVal || emergencyEquipmentTable[index][field] || '');
					}
				});

				function applyValidation(newValue, oldValue){
					if (!emergencyEquipmentTable[index]) return;
					var formattedNew = typeof newValue === 'string' && newValue ? newValue : '';
					if (!formattedNew) {
						$('.equipment-'+ (field==='startDate'?'start':'end') +'-time-' + index).datetimebox('setValue', oldValue);
						return;
					}
					// 保障时间范围（以起草时填写的保障时间为准）
					var gStartStr = $('#guaranteeStartTime').datebox('getValue');
					var gEndStr = $('#guaranteeEndTime').datebox('getValue');
					// 统一使用 / 以提高解析稳定性
					var toMs = function(s){ return new Date((s||'').replace(/-/g,'/')).getTime(); };
					var gStart = toMs(gStartStr), gEnd = toMs(gEndStr);

					// 计算候选的开始/结束时间（另一端取当前表格值）
					var candidateStart = emergencyEquipmentTable[index].startDate || gStartStr;
					var candidateEnd = emergencyEquipmentTable[index].endDate || gEndStr;
					if (field === 'startDate') candidateStart = formattedNew;
					if (field === 'endDate') candidateEnd = formattedNew;
					var startMs = toMs(candidateStart), endMs = toMs(candidateEnd);

					var inRangeStart = !isNaN(startMs) && startMs >= gStart && startMs <= gEnd;
					var inRangeEnd = !isNaN(endMs) && endMs >= gStart && endMs <= gEnd;
					if (!inRangeStart || !inRangeEnd) {
						top.mesShow('温馨提示！', '使用开始/结束时间必须在起草的保障时间范围内！', 2000, 'orange');
						$('.equipment-'+ (field==='startDate'?'start':'end') +'-time-' + index).datetimebox('setValue', oldValue);
						return;
					}
					if (!isNaN(startMs) && !isNaN(endMs) && endMs <= startMs) {
						top.mesShow('温馨提示！', '使用结束时间必须晚于使用开始时间！', 2000, 'orange');
						$('.equipment-'+ (field==='startDate'?'start':'end') +'-time-' + index).datetimebox('setValue', oldValue);
						return;
					}
					emergencyEquipmentTable[index][field] = formattedNew;
					// 同步更新后端识别的字段
					if (field === 'startDate') {
						emergencyEquipmentTable[index].startTime = formattedNew;
					} else if (field === 'endDate') {
						emergencyEquipmentTable[index].endTime = formattedNew;
					}
				}

			});
		}

		// 删除应急设备管理表格中的设备
		function deleteEmergencyEquipment(element) {
			var index = $(element).attr("index");

			// 获取要删除的设备信息
			var equipment = emergencyEquipmentTable[index];
			if (!equipment) {
				top.mesShow("温馨提示！", "未找到要删除的设备信息！", 2000, "red");
				return;
			}

			// 使用项目统一的确认删除弹框样式
			$.messager.confirm("温馨提示！", "确认删除吗？", function(r) {
				if (r) {
					emergencyEquipmentTable.splice(index, 1);
					$("#emergencyEquipmentTable").datagrid("loadData", {
						data: { rows: emergencyEquipmentTable, total: emergencyEquipmentTable.length }
					});

					top.mesShow("温馨提示！", "设备删除成功！", 2000, "green");
				}
			});
		}

		// 显示应急设备选择弹出框
		function showEmergencyEquipmentDialog(equipmentData) {
			console.log('开始显示应急设备选择弹窗，数据数量：', equipmentData ? equipmentData.length : 0);

			// 先检查并清理已存在的弹窗
			if ($('#emergencyEquipmentDialog').length > 0) {
				console.log('发现已存在的弹窗，先清理');
				$('#emergencyEquipmentDialog').dialog('destroy').remove();
			}

			// 清理可能存在的表格实例
			if ($('#equipmentSelectionTable').length > 0) {
				try {
					$('#equipmentSelectionTable').datagrid('destroy');
				} catch(e) {
					console.log('清理表格实例时出现异常：', e);
				}
			}

			// 创建弹出框HTML，优化布局结构
			var dialogHtml = '<div id="emergencyEquipmentDialog" title="选择应急设备" style="position: relative; height: 100%;">' +
				'<div id="dialogContent" style="position: absolute; top: 0; left: 0; right: 0; bottom: 60px; overflow: auto; padding: 10px;">' +
				'<div style="margin-bottom: 10px; white-space: nowrap;">' +
				'<span style="display: inline-block; margin-right: 15px;">' +
				'<label>应急设备分类：</label>' +
				'<input id="equipmentTypeFilter" type="text" style="width: 150px; height: 25px; padding: 3px;" placeholder="请输入应急设备分类">' +
				'</span>' +
				'<span style="display: inline-block; margin-right: 15px;">' +
				'<label>应急设备子类：</label>' +
				'<input id="equipmentSubTypeFilter" type="text" style="width: 150px; height: 25px; padding: 3px;" placeholder="请输入应急设备子类">' +
				'</span>' +
				'<span style="display: inline-block; vertical-align: top;">' +
				'<a href="#" id="filterBtn" class="btn small" style="margin-right: 5px; height: 25px; line-height: 23px; padding: 1px 10px; vertical-align: baseline; display: inline-block; box-sizing: border-box;"><font>查询</font></a>' +
				'<a href="#" id="resetBtn" class="btn small" style="height: 25px; line-height: 23px; padding: 1px 10px; vertical-align: baseline; display: inline-block; box-sizing: border-box;"><font>重置</font></a>' +
				'</span>' +
				'</div>' +
				'<div style="height: calc(100% - 50px); min-height: 300px;">' +
				'<table id="equipmentSelectionTable" style="width: 100%; height: 100%;"></table>' +
				'</div>' +
				'</div>' +
				'<div id="dialogFooter" style="position: absolute; bottom: 0; left: 0; right: 0; height: 60px;  border-top: 1px solid #ddd; display: flex; align-items: center; justify-content: center; gap: 10px; padding: 0 20px; box-sizing: border-box;">' +
				'<a href="#" id="confirmBtn" class="btn small" style="min-width: 80px; height: 32px; line-height: 30px; padding: 0 20px; text-align: center; display: inline-block; box-sizing: border-box;"><font>确认</font></a>' +
				'<a href="#" id="cancelBtn" class="btn small" style="min-width: 80px; height: 32px; line-height: 30px; padding: 0 20px; text-align: center; display: inline-block; box-sizing: border-box;"><font>取消</font></a>' +
				'</div>' +
				'</div>';

			// 添加CSS样式到页面头部（如果还没有添加）
			if (!$('#emergencyDialogStyles').length) {
				var styles = '<style id="emergencyDialogStyles">' +
					'#emergencyEquipmentDialog .panel-body { padding: 0 !important; }' +
					'#emergencyEquipmentDialog #dialogContent { box-sizing: border-box; }' +
					'#emergencyEquipmentDialog .datagrid { border: 1px solid #ddd; }' +
					'#emergencyEquipmentDialog .datagrid-header { background: #f8f9fa; }' +
					'</style>';
				$('head').append(styles);
			}

			// 添加到页面
			$('body').append(dialogHtml);

			// 初始化弹出框
			$('#emergencyEquipmentDialog').dialog({
				width: 1000,
				height: 600,
				modal: true,
				resizable: true,
				maximizable: true,
				onClose: function() {
					console.log('弹窗关闭，清理资源');
					// 清理全局变量
					window.originalEquipmentData = null;
					window.filteredEquipmentData = null;
					// 销毁并移除DOM
					$(this).dialog('destroy').remove();
				},
				onOpen: function() {
					console.log('弹窗打开，开始初始化');
					// 弹出框打开后绑定按钮事件和调整布局
					setTimeout(function() {
						// 绑定按钮事件
						bindDialogEvents();
						// 调整表格高度以适应新布局
						adjustDialogLayout();
					}, 100);
				},
				onResize: function(width, height) {
					// 弹窗大小改变时调整内部布局
					setTimeout(function() {
						adjustDialogLayout();
					}, 50);
				}
			});

			// 存储原始数据
			window.originalEquipmentData = equipmentData || [];
			window.filteredEquipmentData = [...window.originalEquipmentData];

			console.log('原始设备数据：', window.originalEquipmentData);
			console.log('设备数据数量：', window.originalEquipmentData.length);

			// 预处理数据，为carConfiguRationParent为空的数据设置默认值
			for (var i = 0; i < window.originalEquipmentData.length; i++) {
				var item = window.originalEquipmentData[i];
				// if (!item.carConfiguRationParent) {
				// 	if (item.carConfiguRation === '无人机高空站') {
				// 		item.carConfiguRationParent = '无人机高空站';
				// 	} else if (item.carConfiguRation === '卫星便携站') {
				// 		item.carConfiguRationParent = '卫星便携站';
				// 	} else if (item.carConfiguRation && (item.carConfiguRation.indexOf('大型应急车') >= 0 || item.carConfiguRation.indexOf('卫星车') >= 0)) {
				// 		item.carConfiguRationParent = '应急通讯车';
				// 	} else {
				// 		item.carConfiguRationParent = '应急通讯车';
				// 	}
				// }
			}
			window.filteredEquipmentData = [...window.originalEquipmentData];

			// 初始化设备选择表格
			initEquipmentSelectionTable();
		}

		// 调整弹窗布局函数
		function adjustDialogLayout() {
			try {
				// 获取弹窗的实际高度
				var dialogHeight = $('#emergencyEquipmentDialog').height();
				var contentHeight = dialogHeight - 60; // 减去底部按钮区域高度

				// 调整内容区域高度
				$('#dialogContent').css('height', contentHeight + 'px');

				// 调整表格容器高度
				var filterHeight = $('#dialogContent > div:first-child').outerHeight(true) || 50;
				var tableHeight = contentHeight - filterHeight - 20; // 减去过滤区域高度和padding

				$('#dialogContent > div:last-child').css('height', tableHeight + 'px');

				// 重新调整表格大小
				if ($('#equipmentSelectionTable').length > 0) {
					$('#equipmentSelectionTable').datagrid('resize', {
						width: '100%',
						height: tableHeight
					});
				}

				console.log('弹窗布局调整完成，内容高度：', contentHeight, '表格高度：', tableHeight);
			} catch(e) {
				console.error('调整弹窗布局时出现异常：', e);
			}
		}

		// 绑定弹出框事件
		function bindDialogEvents() {
			// 查询按钮事件
			$('#filterBtn').click(function(e) {
				e.preventDefault();
				filterEquipmentData();
			});

			// 重置按钮事件
			$('#resetBtn').click(function(e) {
				e.preventDefault();
				resetEquipmentFilter();
			});

			// 确认按钮事件
			$('#confirmBtn').click(function(e) {
				e.preventDefault();
				confirmEquipmentSelection();
			});

			// 取消按钮事件
			$('#cancelBtn').click(function(e) {
				e.preventDefault();
				closeEquipmentDialog();
			});

			// 应急设备分类输入框回车键查询
			$('#equipmentTypeFilter').keyup(function(e) {
				if (e.keyCode == 13) { // 回车键
					filterEquipmentData();
				}
			});

			// 应急设备子类输入框回车键查询
			$('#equipmentSubTypeFilter').keyup(function(e) {
				if (e.keyCode == 13) { // 回车键
					filterEquipmentData();
				}
			});
		}



		// 初始化设备选择表格
		function initEquipmentSelectionTable() {
			console.log('初始化设备选择表格，数据数量：', window.filteredEquipmentData ? window.filteredEquipmentData.length : 0);

			// 数据验证
			if (!window.filteredEquipmentData || !Array.isArray(window.filteredEquipmentData)) {
				console.error('设备数据无效，使用空数组');
				window.filteredEquipmentData = [];
			}

			if (window.filteredEquipmentData.length > 0) {
				console.log('表格数据示例：', window.filteredEquipmentData[0]);
			}

			// 确保表格容器存在
			if ($('#equipmentSelectionTable').length === 0) {
				console.error('表格容器不存在');
				return;
			}

			// 使用与原本页面相同的loadGrid方式初始化表格
			var equipmentSelectionParam = {
				"listtable": {
					"listname": "#equipmentSelectionTable",
					"data": { data: { rows: window.filteredEquipmentData, total: window.filteredEquipmentData.length } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"rownumbers": true,
					"singleSelect": false,
					"frozenColumns": [],
					"columns": [[
						{field: 'ck', checkbox: true},
						{field: 'cities', title: '调用应急设备地市', width: 120, align: 'center'},
						{field: 'carConfiguRationParent', title: '应急设备分类', width: 120, align: 'center',},
						{field: 'carConfiguRation', title: '应急设备子类', width: 120, align: 'center'},
						{field: 'licensePlate', title: '调用应急设备车牌号', width: 120, align: 'center'},
						{field: 'openingStation', title: '调用应急设备联系人', width: 120, align: 'center',},
						{field: 'openingStationPhone', title: '调用应急设备联系电话', width: 140, align: 'center',},
						{field: 'schedulingCondition', title: '调度状态', width: 80, align: 'center'}
					]],
					"onLoadSuccess": function(data) {
						console.log('表格加载成功，数据：', data);
						console.log('表格行数：', data.data ? data.data.rows.length : 0);
						if (data.data && data.data.rows && data.data.rows.length > 0) {
							console.log('第一行数据：', data.data.rows[0]);
						} else {
							console.log('表格无数据');
						}
					},
					"onLoadError": function(xhr, status, error) {
						console.error('表格加载失败：', error);
					}
				}
			};

			try {
				loadGrid(equipmentSelectionParam);
				// 延迟调整表格大小，确保DOM已完全渲染
				setTimeout(function() {
					adjustDialogLayout();
				}, 300);
			} catch(e) {
				console.error('初始化表格时出现异常：', e);
			}
		}

		// 过滤设备数据
		function filterEquipmentData() {
			var selectedType = $('#equipmentTypeFilter').val().trim();
			var selectedSubType = $('#equipmentSubTypeFilter').val().trim();

			console.log('开始过滤，分类：', selectedType, '子类：', selectedSubType);

			// 数据验证
			if (!window.originalEquipmentData || !Array.isArray(window.originalEquipmentData)) {
				console.error('原始设备数据无效');
				window.filteredEquipmentData = [];
				return;
			}

			console.log('原始数据数量：', window.originalEquipmentData.length);

			window.filteredEquipmentData = window.originalEquipmentData.filter(function(item) {
				var typeMatch = true;
				var subTypeMatch = true;

				// 应急设备分类过滤
				if (selectedType) {
					typeMatch = item.carConfiguRationParent &&
								item.carConfiguRationParent.indexOf(selectedType) >= 0;
				}

				// 应急设备子类过滤
				if (selectedSubType) {
					subTypeMatch = item.carConfiguRation &&
								   item.carConfiguRation.indexOf(selectedSubType) >= 0;
				}

				return typeMatch && subTypeMatch;
			});

			console.log('过滤后的设备数量：', window.filteredEquipmentData.length);
			if (window.filteredEquipmentData.length > 0) {
				console.log('过滤后第一条数据：', window.filteredEquipmentData[0]);
			}

			// 重新加载表格数据，使用与原本页面相同的数据格式
			try {
				$('#equipmentSelectionTable').datagrid('loadData', {
					data: { rows: window.filteredEquipmentData, total: window.filteredEquipmentData.length }
				});
			} catch(e) {
				console.error('重新加载表格数据时出现异常：', e);
			}
		}

		// 重置过滤条件
		function resetEquipmentFilter() {
			$('#equipmentTypeFilter').val('');
			$('#equipmentSubTypeFilter').val('');

			// 数据验证
			if (!window.originalEquipmentData || !Array.isArray(window.originalEquipmentData)) {
				console.error('原始设备数据无效，无法重置');
				window.filteredEquipmentData = [];
				return;
			}

			window.filteredEquipmentData = [...window.originalEquipmentData];

			// 使用与原本页面相同的数据格式重新加载表格
			try {
				$('#equipmentSelectionTable').datagrid('loadData', {
					data: { rows: window.filteredEquipmentData, total: window.filteredEquipmentData.length }
				});
			} catch(e) {
				console.error('重置表格数据时出现异常：', e);
			}

			console.log('重置过滤条件，设备数量：', window.filteredEquipmentData.length);
		}

		// 确认设备选择
		function confirmEquipmentSelection() {
			var selectedRows = $('#equipmentSelectionTable').datagrid('getChecked');
			if (selectedRows.length === 0) {
				top.mesShow("温馨提示！", "请至少选择一个应急设备！", 2000, "red");
				return;
			}

			console.log('选中的设备：', selectedRows);

			// 添加选中的设备到应急设备管理表格
			var addedCount = 0;
			var guaranteeStartTime = $("#guaranteeStartTime").datebox("getValue");
			var guaranteeEndTime = $("#guaranteeEndTime").datebox("getValue");
			for (var i = 0; i < selectedRows.length; i++) {
				var equipment = selectedRows[i];

				// 标准化设备数据，确保包含所需字段
				var standardizedEquipment = {
					id: equipment.id,
					cities: equipment.cities,
					carConfiguRation: equipment.carConfiguRation, // 应急设备子类
					carConfiguRationParent: equipment.carConfiguRationParent, // 应急设备分类
					licensePlate: equipment.licensePlate,
					openingStation: equipment.openingStation,
					openingStationPhone: equipment.openingStationPhone,
					schedulingCondition: equipment.schedulingCondition,
					// 设置默认的开始和结束时间
					startDate: equipment.startDate || guaranteeStartTime,
					endDate: equipment.endDate || guaranteeEndTime
				};

				// 检查是否已存在（根据ID或车牌号判断）
				var exists = false;
				for (var j = 0; j < emergencyEquipmentTable.length; j++) {
					if (emergencyEquipmentTable[j].id === equipment.id ||
						emergencyEquipmentTable[j].licensePlate === equipment.licensePlate) {
						exists = true;
						break;
					}
				}

				if (!exists) {
					emergencyEquipmentTable.push(standardizedEquipment);
					addedCount++;
				}
			}

			// 刷新应急设备管理表格
			$("#emergencyEquipmentTable").datagrid("loadData", {
				data: { rows: emergencyEquipmentTable, total: emergencyEquipmentTable.length }
			});

			// 关闭弹出框
			closeEquipmentDialog();

			var message = addedCount > 0 ?
				"成功添加 " + addedCount + " 个应急设备！" +
				(selectedRows.length > addedCount ? "（已过滤重复设备）" : "") :
				"所选设备已存在，未添加新设备！";

			top.mesShow("温馨提示！", message, 2500, addedCount > 0 ? "green" : "orange");
		}

		// 获取联系人
		function getContactPerson(equipment) {
			if (equipment.carConfiguRation === '无人机高空站' || equipment.carConfiguRation === '卫星便携站') {
				return equipment.uavStationLeader || '';
			} else {
				return equipment.openingStation || equipment.theDriver || '';
			}
		}

		// 获取联系电话
		function getContactPhone(equipment) {
			if (equipment.carConfiguRation === '无人机高空站' || equipment.carConfiguRation === '卫星便携站') {
				return equipment.uavStationLeaderPhone || '';
			} else {
				return equipment.openingStationPhone || equipment.theDriverPhone || '';
			}
		}

		// 关闭设备选择弹出框
		function closeEquipmentDialog() {
			console.log('手动关闭设备选择弹窗');

			// 清理表格实例
			if ($('#equipmentSelectionTable').length > 0) {
				try {
					$('#equipmentSelectionTable').datagrid('destroy');
				} catch(e) {
					console.log('清理表格实例时出现异常：', e);
				}
			}

			// 清理全局变量
			window.originalEquipmentData = null;
			window.filteredEquipmentData = null;

			// 关闭并销毁弹窗
			if ($('#emergencyEquipmentDialog').length > 0) {
				$('#emergencyEquipmentDialog').dialog('close');
			}
		}

		$(document).on('click', '.addBtn', function () {
			var type = $(this).attr("data-type")
			var startTime = $("#guaranteeStartTime").datebox("getValue")
			if (!startTime) {
				return top.mesShow("温馨提示！", "请选择 保障开始时间 !", 2000, "red")
			}
			var endTime = $("#guaranteeEndTime").datebox("getValue")
			if (!endTime) {
				return top.mesShow("温馨提示！", "请选择 保障结束时间 !", 2000, "red")
			}
			var ids = []
			var rows = []
			if (type == "大型应急车") {
				rows = carTableA
			} else if (type == "卫星车") {
				rows = carTableB
			} else if (type == "无人机高空站") {
				rows = carTableC
			} else if (type == "卫星便携站") {
				rows = carTableD
			}
			for (var i in rows) {
				ids.push(rows[i].id || rows[i].carId)
			}
			top.dialogP("html/apply/chooseCar.html?processType=C&ids=" + ids.join("/") +
				"&city=" + encodeURI(web.currentUser.belongCompanyName) +
				"&startTime=" + startTime +
				"&endTime=" + endTime +
				"&pmInsId=" + $("#pmInsId").val() +
				"&type=" + encodeURI(type),
				window.name, "选择应急设备", "chooseCar", false, 1100, 700)
		});

		// 选择应急车辆回显
		function chooseCar(data) {
			var tableID = "";
			var carTable = [];
			if (data.type == "大型应急车") {
				tableID = "#carTableA"
				for (var i in data.data) {
					if (includes(carTableA, data.data[i].id)) {
						carTableA.push(data.data[i])
					}
				}
				carTable = carTableA
			} else if (data.type == "卫星车") {
				tableID = "#carTableB"
				for (var i in data.data) {
					if (includes(carTableB, data.data[i].id)) {
						carTableB.push(data.data[i])
					}
				}
				carTable = carTableB
			} else if (data.type == "无人机高空站") {
				tableID = "#carTableC"
				for (var i in data.data) {
					if (includes(carTableC, data.data[i].id)) {
						carTableC.push(data.data[i])
					}
				}
				carTable = carTableC
			} else if (data.type == "卫星便携站") {
				tableID = "#carTableD"
				for (var i in data.data) {
					if (includes(carTableD, data.data[i].id)) {
						carTableD.push(data.data[i])
					}
				}
				carTable = carTableD
			}

			$(tableID).datagrid("loadData", { data: { rows: carTable, total: carTable.length } })
		}

		// 原有的deleteInfo函数已移除，统一使用应急设备管理表格的删除功能

		function getcallback(data) {
			startTime=data.guaranteeStartTime
			endTime=data.guaranteeEndTime
			$('#guaranteeStartTime').datebox('readonly',true)
			$('#guaranteeEndTime').datebox('readonly',true)
			loadDeviceConfigurationOptions(function() {
				// 在加载完成后执行回调
				console.log('申请应急设备配置选项加载完成');
				var kuadeviceConfiguration = data.kuadeviceConfiguration ? data.kuadeviceConfiguration.split(',') : [];
				for (var i = 0; i < kuadeviceConfiguration.length; i++) {
					$("input[name='checkbox1'][value='" + kuadeviceConfiguration[i] + "']").attr('checked', 'checked')
				}
			});
			

			if (gps.location == "yjtxc.schedulingChiefAudit" || gps.location == 'yjtxc.networkDepartment' || gps.location == 'yjtxc.coordinatingDispatching' || gps.type == 'toRead' || gps.type == 'doRead') {
				// 不再使用分类表格，统一使用应急设备管理表格
				// 将所有设备数据合并到统一的应急设备管理表格
				emergencyEquipmentTable = [];
				if (data.equipmentList) {
					emergencyEquipmentTable = emergencyEquipmentTable.concat(data.equipmentList);
				}
				if (data.equipmentMoonList) {
					emergencyEquipmentTable = emergencyEquipmentTable.concat(data.equipmentMoonList);
				}
				if (data.equipmentUavList) {
					emergencyEquipmentTable = emergencyEquipmentTable.concat(data.equipmentUavList);
				}
				if (data.equipmentBleList) {
					emergencyEquipmentTable = emergencyEquipmentTable.concat(data.equipmentBleList);
				}

				// 确保每个设备都有时间字段，如果没有则使用保障时间
				for (var i = 0; i < emergencyEquipmentTable.length; i++) {
					var equipment = emergencyEquipmentTable[i];
					if (!equipment.startDate) {
						equipment.startDate = data.guaranteeStartTime;
					}
					if (!equipment.endDate) {
						equipment.endDate = data.guaranteeEndTime;
					}
					// 同步给后端识别的字段名
					if (!equipment.startTime) {
						equipment.startTime = equipment.startDate;
					}
					if (!equipment.endTime) {
						equipment.endTime = equipment.endDate;
					}
				}

				// 刷新应急设备管理表格显示
				$("#emergencyEquipmentTable").datagrid("loadData", {
					data: { rows: emergencyEquipmentTable, total: emergencyEquipmentTable.length }
				});
			}
			// 重复的代码已移除，统一在上面处理
			if(gps.type=="join"){
				$('td .addBtn').hide();
			}
			// 控制应急设备管理表格的显示 - 只在特定流程节点显示
			if (gps.location == "yjtxc.coordinatingDispatching" ||
				gps.location == "yjtxc.schedulingChiefAudit" ||
				gps.location == "yjtxc.networkDepartment") {
				$(".chooseCarHide").show();
				console.log('getcallback - 当前流程节点：' + gps.location + '，显示应急设备管理表格');

				// 确保选择应急设备按钮可见
				setTimeout(function() {
					$('.selectEmergencyEquipmentBtn').show();
					console.log('确保选择应急设备按钮可见');
				}, 100);
			} else {
				$(".chooseCarHide").hide();
				console.log('getcallback - 当前流程节点：' + gps.location + '，隐藏应急设备管理表格');
			}

		}

		function nextBtnOther() {
			var phoneTmp = [];
			var belongCompanyCodeTmp = [];

			// 使用统一的应急设备管理表格数据
			for (var i = 0; i < emergencyEquipmentTable.length; i++) {
				var equipment = emergencyEquipmentTable[i];
				if (equipment.openingStationPhone) {
					phoneTmp.push(equipment.openingStationPhone.split(",")[0]);
				}
				if (equipment.citiesCode) {
					belongCompanyCodeTmp.push(equipment.citiesCode);
				}
			}

			var phone=[]
			for(var i=0;i<phoneTmp.length;i++) {
				var items=phoneTmp[i];
				if($.inArray(items ,phone)===-1) {
					phone.push(items);
				}
			}

			var belongCompanyCode=[]
			for(var i=0;i<belongCompanyCodeTmp.length;i++) {
				var items=belongCompanyCodeTmp[i];
				if($.inArray(items ,belongCompanyCode)===-1) {
					belongCompanyCode.push(items);
				}
			}
			return {
				phone: phone.join(","),
				belongCompanyCode : belongCompanyCode.join(","),
				applyType: "B"
			}
		}

		function onSubmit(data) {
			var involveB = [] //涉及B系统
			$("input[name='checkbox1']:checked").each(function (index, item) {
				involveB.push($(item).val())
			});
			// if(involveB.length==0){
			// 	return top.mesShow("温馨提示！", "请至少添加一个申请应急设备配置！", 2000, "red")
			// }
			data.kuadeviceConfiguration = involveB.join(',');
			var start = $(".guaranteeStartTime").datebox("getValue")
			var end = $("#guaranteeEndTime").datebox("getValue")
			var days = (new Date(end).getTime() - new Date(start).getTime()) / 1000 / 60 / 60 / 24
			if (days >= 3 && data.attachmentList.length == 0) {
				return top.mesShow("温馨提示！", "调度天数大于等于3天需要添加附件！", 2000, "red")
			}
			// if (gps.location == "yjtxc.coordinatingDispatching") {
			// 	var dateValidate = true
			// 	$(".easyuidate").each(function (i, v) {
			// 		if (!$(v).datebox("getValue")) {
			// 			dateValidate = false
			// 			return false
			// 		}
			// 	})
			// 	if (!dateValidate) {
			// 		return top.mesShow("温馨提示！", "请填写 使用开始时间 和 使用结束时间 ！", 2000, "red")
			// 	}
			// }
			if (gps.location == "yjtxc.schedulingChiefAudit" || gps.location == 'yjtxc.coordinatingDispatching') {
				// 使用统一的应急设备管理表格数据，不再使用分类表格
				if (emergencyEquipmentTable.length == 0) {
					return top.mesShow("温馨提示！", "请至少添加一个应急设备！", 2000, "red")
				}

				// 将统一表格中的数据按设备类型分类到对应的列表中
				data.equipmentList = [];
				data.equipmentMoonList = [];
				data.equipmentUavList = [];
				data.equipmentBleList = [];

				var timeFlag = 1;
				for (var i = 0; i < emergencyEquipmentTable.length; i++) {
					var equipment = emergencyEquipmentTable[i];
					var item = {
						carConfiguRation: equipment.carConfiguRation,
						carConfiguRationParent:equipment.carConfiguRationParent,
						carId: equipment.id,
						cities: equipment.cities,
						equipmentManufacturer: equipment.equipmentManufacturer || '',
						licensePlate: equipment.licensePlate,
						openingStation: equipment.openingStation,
						openingStationPhone: equipment.openingStationPhone,
						startTime: equipment.startTime,
						endTime: equipment.endTime,
						id: equipment.id
					};

					// 根据设备类型添加特定字段
					if (equipment.carConfiguRation === '大型应急车' || equipment.carConfiguRation === '卫星车') {
						item.theDriver = equipment.openingStation;
						item.theDriverPhone = equipment.openingStationPhone;
					} else if (equipment.carConfiguRation === '无人机高空站') {
						item.uavStationLeader = equipment.openingStation;
						item.uavStationLeaderPhone = equipment.openingStationPhone;
					} else if (equipment.carConfiguRation === '卫星便携站') {
						item.chiefWritingStation = equipment.openingStation;
						item.uavStationLeaderPhone = equipment.openingStationPhone;
					}

					// 时间验证
					if (equipment.startDate) item.startDate = equipment.startDate;
					if (equipment.endDate) item.endDate = equipment.endDate;
					timeFlag = calcTime(item);
					if (timeFlag < 0) return false;

					// 根据设备类型分类存储
					// if (equipment.carConfiguRation === '大型应急车') {
						data.equipmentList.push(item);
					// } else if (equipment.carConfiguRation === '卫星车') {
					// 	data.equipmentMoonList.push(item);
					// } else if (equipment.carConfiguRation === '无人机高空站') {
					// 	data.equipmentUavList.push(item);
					// } else if (equipment.carConfiguRation === '卫星便携站') {
					// 	data.equipmentBleList.push(item);
					// }
				}
			}
			console.log(data)
			return data
		}

		// 校验表格时间
		function calcTime(item) {
			var guaranteeStartTimeDate = new Date($('#guaranteeStartTime').datebox('getValue')).getTime();
			var guaranteeEndTimeDate = new Date($('#guaranteeEndTime').datebox('getValue')).getTime();
			if (item.endTime && item.startTime) {
				var startTimeDate = new Date(item.startTime).getTime()
				var endTimeDate = new Date(item.endTime).getTime()
				if (endTimeDate < startTimeDate) {
					mesShow('温馨提示', '使用结束时间不能早于使用开始时间！', 1000, 'orange');
					return -2
				} else if (startTimeDate < guaranteeStartTimeDate || startTimeDate > guaranteeEndTimeDate || endTimeDate < guaranteeStartTimeDate || endTimeDate > guaranteeEndTimeDate) {
					mesShow('温馨提示', '使用开始时间或使用结束时间要在保障时间范围内！', 1000, 'orange');
					return -3
				} else {
					return 1
				}
			} else {
				if (gps.location == 'yjtxc.coordinatingDispatching') {
					mesShow('温馨提示', '使用开始时间或使用结束时间不能为空！', 1000, 'orange');
					return -1
				} else {
					return 1
				}
			}
		}

		// 原有的chooseCarCB函数已移除，统一使用应急设备管理表格
		// function deleteInfo(param) {
		// 	rows.splice($(param).attr("index"), 1)
		// 	$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
		// }
		function includes(arr, key) {
			for (var i in arr) {
				if (key == arr[i].id) {
					return false
				}
			}
			return true
		}
		function loadTableCB(data) {
			if (data == "卫星车" || data == "大型应急车") {
				loadTable(carCols())
			} else if (data == "无人机高空站") {
				loadTable(uavCols({ title: "无人机高空基站负责人", field: "uavStationLeader" }))
			} else {
				loadTable(uavCols({ title: "卫星便携站负责人", field: "chiefWritingStation" }))
			}
		}
	</script>
</head>

<body class="body_page" style="padding-top:85px;">
	<form id="dailyLocalForm" method="post" formLocation="yjtxc.start" noNextUserDecisionId="end"
		contentType="application/json; charset=utf-8" cmd-select="action/applicationForm/getFormDetail"
		getcallback="getcallback()" nextBtnOther="nextBtnOther()" onSubmit="onSubmit()">
		<div class="pageInfo">
			<div class="pageInfoD">
				<a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i>
					<font>流转下一步</font>
				</a>
				<a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i>
					<font>流程跟踪</font>
				</a>
				<a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i>
					<font>查看意见</font>
				</a>
				<a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i>
					<font>流程图</font>
				</a>
			</div>
		</div>
		<input type="hidden" name="id" id="id" class="id" noReset="true">
		<input type="hidden" name="pmInsId" id="pmInsId" class="pmInsId" noReset="true">
		<input type="hidden" name="processType" id="processType" class="processType" value="B" noReset="true">
		<table border="0" cellpadding="0" th:colspan="6" cellspacing="18">
			<tr>
				<td colspan="4" class="myTitle">
					日常应急调度申请表
				</td>
			</tr>
			<tr>
				<td colspan="4" class="splite">申请信息</td>
			</tr>
			<tr>
				<td align="right" width="150">工单编号：</td>
				<td width="300">
					<input name="orderNumber" class="textAndInput_readonly orderNumber" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="150">申请人：</td>
				<td width="300">
					<input name="userName" class="textAndInput_readonly userName" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
					<input class="trueName" name="trueName" type="hidden" />
				</td>
			</tr>
			<tr>
				<td align="right" width="150">申请人所在单位：</td>
				<td width="300">
					<input name="applicantUnit" class="textAndInput_readonly applicantUnit" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="150">申请人联系方式：</td>
				<td width="300">
					<input name="applicantPhone" class="textAndInput_readonly applicantPhone" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>保障开始时间：
				</td>
				<td width="300">
					<input id="guaranteeStartTime" name="guaranteeStartTime" type="text"
						validType="startDateCheck['guaranteeEndTime','guaranteeStartTime']"
						class="easyui-datetimebox guaranteeStartTime" required="required"
						data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;" />
				</td>
				<td align="right" width="150">
					<font class='col_r'>*</font>保障结束时间：
				</td>
				<td width="300">
					<input id="guaranteeEndTime" name="guaranteeEndTime" type="text" class="easyui-datetimebox guaranteeEndTime"
						required="required" validType="startDateCheck['guaranteeEndTime','guaranteeStartTime']"
						data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>申请应急设备台数：
				</td>
				<td width="300">
					<input class="easyui-validatebox equipmenApplicationsNum" id="equipmenApplicationsNum" required="required" name="equipmenApplicationsNum"
						validType="zinteger" type="text" style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="150">申请应急设备厂家：</td>
				<td width="300">
					<input class="easyui-combobox equipmentManufacturer" name="equipmentManufacturer"
						style="width:300px; height: 32px;" data-options="
						valueField: 'value',
						editable: false,
						ischooseall: true,
						panelHeight: 'auto',
						textField: 'name',
						queryParams:{'dictType':'application_manufacturer'},
						url: web.rootdir+'action/queryDictValue/queryByType'" />
				</td>

			</tr>
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>保障现场联系人电话：
				</td>
				<td width="300">
					<input class="easyui-validatebox siteContactPhone" required="required" name="siteContactPhone"
						validType="phone" type="text" style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="150">
					<font class='col_r'>*</font>保障现场联系人：
				</td>
				<td width="300">
					<input class="easyui-validatebox siteContact" required="required" name="siteContact" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>保障性质：
				</td>
				<td width="300">
					<input class="easyui-combobox securityProperties" name="securityProperties" required="required"
						style="width:300px; height: 32px;" data-options="
						valueField: 'value',
						editable: false,
						ischooseall: true,
						panelHeight: 'auto',
						textField: 'name',
						queryParams:{'dictType':'application_properties'},
						url: web.rootdir+'action/queryDictValue/queryByType'" />
				</td>
				<td align="right" width="150">保障具体地址：</td>
				<td width="300">
					<input class="easyui-validatebox specificAddress" name="specificAddress" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="150">预计人数：</td>
				<td width="300">
					<input class="easyui-validatebox numberExpected" name="numberExpected" validType="zinteger" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="150">
					<font class='col_r'>*</font>活动名称：
				</td>
				<td width="300">
					<input class="easyui-validatebox nameEvent" required="required" name="nameEvent" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			 <!-- <tr>
				 <td align="right" width="150">
					<font class='col_r'>*</font>申请应急设备配置：
				</td>
				<td width="300">
					<input class="easyui-combobox deviceConfiguration" name="deviceConfiguration" required="required"
						style="width:300px; height: 32px;"
						data-options="
						valueField: 'value',
						editable: false,
						ischooseall: true,
						panelHeight: 'auto',
						textField: 'name',
						data: [{value: '卫星车', name: '卫星车'},{value: '大型应急车', name: '大型应急车'},{value: '无人机高空站', name: '无人机高空站'},{value: '卫星便携站', name: '卫星便携站'}] " />
				</td>
			</tr> -->
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>申请应急设备配置：
				</td>
				<td colspan="3">
					<div class="demandValue involve" id="deviceConfigurationContainer">
						<!-- 动态加载的复选框选项将在这里显示 -->
					</div>
				</td>
			</tr>
			
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>事件概要：
				</td>
				<td colspan="3">
					<textarea name="itemOverview" class="easyui-validatebox itemOverview"
						validType="maxLength[300,'itemOverviewRest']" required="required"
						style="width:100%;height:107px;resize:none;line-height: 18px;"
						placeholder="活动简要说明，文字不超过300字，如因疫情原因跨地市调度，需提供出入该地市的相关政策信息"></textarea>
					<span class="itemOverviewRest"></span>
				</td>
			</tr>
			<tr>
				<td align="right" width="150">
					<font class='col_r'>*</font>附件：
				</td>
				<td colspan="3">
					<input name="attachmentList" id="attachmentList" type="text" file="true"
						class="cselectorImageUpload attachmentList" mulaccept="true"
						btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
						href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
				</td>
			</tr>
			<!-- 新增应急设备管理区域 - 只在特定流程节点显示 -->
			<tr class="chooseCarHide emergencyEquipmentManagement">
				<td colspan="4" class="splite">应急设备管理
					<a class="btn small fr selectEmergencyEquipmentBtn" style="margin-left: 10px; display: inline-block !important; background-color: #1890ff; color: white;  padding: 0.01px 10px; text-decoration: none; border-radius: 3px; border: 1px solid #1890ff;">
						<font>选择应急设备</font>
					</a>
				</td>
			</tr>
			<tr class="chooseCarHide emergencyEquipmentManagement">
				<td colspan="4" class="emergencyEquipmentTable">
					<table id="emergencyEquipmentTable"></table>
				</td>
			</tr>
			<!-- 原有的分类表格已移除，统一使用应急设备管理表格 -->
		</table>
	</form>
</body>

</html>