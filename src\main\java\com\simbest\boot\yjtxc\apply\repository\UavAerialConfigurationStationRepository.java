package com.simbest.boot.yjtxc.apply.repository;


import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.UavAerialConfigurationStation;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:22
 * @describe 无人机高空配置库
 */
public interface UavAerialConfigurationStationRepository extends LogicRepository<UavAerialConfigurationStation, String> {

    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name, "+
            "       t.cities," +
            "       t.cities_code," +
            "       t.equipment_manufacturer," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.uav_station_leader," +
            "       t.license_plate," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.uav_station_leader_phone from US_UAV_CONFI t  where t.enabled='1'    ", nativeQuery = true)
    List<Map<String, Object>> findByDrone();


    @Query(value = "select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name, "+
            "       t.cities," +
            "       t.cities_code," +
            "       t.equipment_manufacturer," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.uav_station_leader," +
            "       t.license_plate," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.uav_station_leader_phone from US_UAV_CONFI t where t.enabled='1' and  t.cities=:cities  ", nativeQuery = true)
    List<Map<String, Object>> findByDroneAndCities(@Param("cities") String cities);

}
