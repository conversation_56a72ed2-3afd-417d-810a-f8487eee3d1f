package com.simbest.boot.yjtxc.mainbills.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface UsPmInstenceRepository extends LogicRepository<UsPmInstence,String> {

    String sql1 = "select * from us_pm_instence a WHERE a.pm_ins_id=:pmInsId";

    @Query(value = sql1, nativeQuery = true)
    UsPmInstence findByPmInsId(@Param("pmInsId") String pmInsId);


    @Modifying
    @Query(
            value = "update us_pm_instence set enabled=0 where id = :id",
            nativeQuery = true
    )
    int deleteByFromId(@Param("id") Long id);


    @Modifying
    @Query(
            value = "update us_pm_instence set enabled=0 where id = :id",
            nativeQuery = true
    )
    int deleteByFromIdHexiao(@Param("id") String id);








}

