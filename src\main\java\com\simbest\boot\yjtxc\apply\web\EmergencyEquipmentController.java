package com.simbest.boot.yjtxc.apply.web;

import cn.hutool.core.collection.CollectionUtil;
import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.service.IEmergencyEquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:45
 * @describe 应急设备
 */
@Api(description = "应急设备")
@Slf4j
@RestController
@RequestMapping(value = "/action/emergencyEquipment")
public class EmergencyEquipmentController extends LogicController<EmergencyEquipment, String> {

    private IEmergencyEquipmentService iEmergencyEquipmentService;

    public EmergencyEquipmentController(IEmergencyEquipmentService iEmergencyEquipmentService) {
        super(iEmergencyEquipmentService);
        this.iEmergencyEquipmentService = iEmergencyEquipmentService;
    }

    /**
     * 台账查询
     *
     * @param page
     * @param size
     * @param params
     * @return
     */
    @ApiOperation(value = "台账查询", notes = "台账查询")
    @PostMapping(value = {"/findByParameterQuery", "/api/findByParameterQuery", "/findByParameterQuery/sso"})
    public JsonResponse findByParameterQuery(@RequestParam(required = false, defaultValue = "1") int page,
                                             @RequestParam(required = false, defaultValue = "10") int size,
                                             @RequestBody Map<String, Object> params) {
        return iEmergencyEquipmentService.findByParameterQuery(page, size, params);
    }

    /**
     * 检车车辆是否可用
     *
     * @param params
     * @return
     */
    @ApiOperation(value = "检车车辆是否可用", notes = "检车车辆是否可用")
    @PostMapping(value = {"/findByAvailable", "/api/findByAvailable", "/findByAvailable/sso"})
    public JsonResponse findByAvailable(@RequestBody Map<String, Object> params) {
        return iEmergencyEquipmentService.findByAvailable(params);
    }

    /**
     * 工单信息导出工单查询
     *
     * @param response
     * @param request
     * @param emergencyEquipment
     * @return
     * @throws ParseException
     */
    @ApiOperation(value = "工单信息导出工单查询", notes = "工单信息导出工单查询")
    @PostMapping(value = {"/exportOrder", "/api/exportOrder", "/exportOrder/sso"})

    public JsonResponse exportOrder(HttpServletResponse response, HttpServletRequest request,  EmergencyEquipment emergencyEquipment) throws ParseException {
        iEmergencyEquipmentService.exportOrder(request, response, emergencyEquipment);
        return JsonResponse.defaultSuccessResponse();
    }

}
