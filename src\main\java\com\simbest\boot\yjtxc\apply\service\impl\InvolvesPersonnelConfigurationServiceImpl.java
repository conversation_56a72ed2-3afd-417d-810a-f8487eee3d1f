package com.simbest.boot.yjtxc.apply.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.SimpleOrg;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.yjtxc.apply.model.InvolvesPersonnelConfiguration;
import com.simbest.boot.yjtxc.apply.repository.InvolvesPersonnelConfigurationRepositort;
import com.simbest.boot.yjtxc.apply.service.IInvolvesPersonnelConfigurationService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 16:26
 * @describe 流程固化涉及人员配置业务实现层
 */
@Slf4j
@Service
public class InvolvesPersonnelConfigurationServiceImpl extends LogicService<InvolvesPersonnelConfiguration, String> implements IInvolvesPersonnelConfigurationService {


    private InvolvesPersonnelConfigurationRepositort repository;

    public InvolvesPersonnelConfigurationServiceImpl(InvolvesPersonnelConfigurationRepositort repositort) {
        super(repositort);
        this.repository = repositort;
    }

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    @Autowired
    private SysFileService fileService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    final String param1 = "action/involvesPersonnel/";

    /**
     * 流程固化涉及人员配置
     *
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @Override
    public JsonResponse findByAllInvolvesPersonnelConfiguration(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap) {
        Page<InvolvesPersonnelConfiguration> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");
        try {
            //获取地市
            String cities = MapUtil.getStr(paramMap, "cities");

            Specification<InvolvesPersonnelConfiguration> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> predicateList = Lists.newArrayList();

                if (StrUtil.isNotEmpty(cities)) {
                    predicateList.add(criteriaBuilder.like(root.get("appCities"), "%" + cities + "%"));
                }
                if (true) {
                    predicateList.add(criteriaBuilder.equal(root.get("enabled"), true));
                }
                javax.persistence.criteria.Predicate[] predicates = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicates));
            };
            findAllGarageConfiguration = repository.findAll(specification, pageable);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);
    }

    /**
     * 导入流程固化涉及人员配置信息
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        List<InvolvesPersonnelConfiguration> garageConfigurationList = null;
        try {
            PrintWriter out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            // 附件excel 文件名
            String originalFilename = "";
            for (MultipartFile uploadfile : multipartFiles.values()) {
                /**
                 * 这里要区分是“外置设备”还是“电脑终端设备”,处理之后返回最终的设备信息集合
                 */
                originalFilename = uploadfile.getOriginalFilename();
                // 如果为电脑终端设备
                if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("xls")) {
                    // 先上传至sys_file表,注意sheetName名要与excel保持一致
                    UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"),
                            request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), InvolvesPersonnelConfiguration.class,
                            "流程固化涉及人员配置模板导入");
                    List<InvolvesPersonnelConfiguration> listData = uploadFileResponse.getListData();
                    if (CollectionUtil.isNotEmpty(listData)) {
                        listData.remove(0);
                    }
                    if(listData.size() > 0){
                        //查询 所有二级组织  并匹配拿到 code
                        List<SimpleOrg> pOrgAndCityOrg = uumsSysOrgApi.findPOrgAndCityOrg(Constants.APP_CODE);
                        for (InvolvesPersonnelConfiguration listDatum : listData) {
                            for (SimpleOrg simpleOrg : pOrgAndCityOrg) {
                                // 与现有部门、分公司名称一致
                                if(StrUtil.equals(simpleOrg.getOrgName(), listDatum.getAppCities()) || StrUtil.equals(simpleOrg.getOrgName(), listDatum.getAppCities() + "分公司")){
                                    listDatum.setAppCities(simpleOrg.getOrgName());
                                    listDatum.setAppCitiesCode(simpleOrg.getOrgCode());
                                    break;
                                }
                            }
                            // 申请人OA账号-A角
                            if(StrUtil.isEmpty(listDatum.getAppUsernameA()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailA())){
                                listDatum.setAppUsernameA(listDatum.getAppLinkEmailA().split("@")[0]);
                            }
                            // 申请人OA账号-B角
                            if(StrUtil.isEmpty(listDatum.getAppUsernameB()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailB())){
                                listDatum.setAppUsernameB(listDatum.getAppLinkEmailB().split("@")[0]);
                            }
                            // 网络部经理
                            if(StrUtil.isEmpty(listDatum.getApptUsernameVehicle()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailVehicle())){
                                listDatum.setApptUsernameVehicle(listDatum.getAppLinkEmailVehicle().split("@")[0]);
                            }
                            // 负责人OA账号-A角
                            if(StrUtil.isEmpty(listDatum.getHeadUsernameA()) && StrUtil.isNotEmpty(listDatum.getHeadLinkEmailA())){
                                listDatum.setHeadUsernameA(listDatum.getHeadLinkEmailA().split("@")[0]);
                            }
                            // 负责人OA账号-B角
                            if(StrUtil.isEmpty(listDatum.getHeadUsernameB()) && StrUtil.isNotEmpty(listDatum.getHeadLinkEmailB())){
                                listDatum.setHeadUsernameB(listDatum.getHeadLinkEmailB().split("@")[0]);
                            }

                            // 负责人网络部经理 邮箱
                            listDatum.setHeadLinkEmailVehicle(listDatum.getAppLinkEmailVehicle());

                        }
                    }


                    jsonResponse.setData(uploadFileResponse);
                    jsonResponse.setMessage("上传成功");
                } else {
                    jsonResponse.setMessage("请按照模板上传相关数据!");
                }
            }
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return jsonResponse;
    }

    /**
     * 批量保存流程固化涉及人员配置信息
     *
     * @param involvesPersonnelConfigurationsList
     * @return
     */
    @Override
    public JsonResponse saveInvolvesPersonnelConfiguration(List<InvolvesPersonnelConfiguration> involvesPersonnelConfigurationsList) {
        this.deleteAll(); // 删除旧数据
        saveAll(involvesPersonnelConfigurationsList);
        return JsonResponse.success("数据保存成功");
    }

    /**
     * 根据公司code查询对应的责任经理
     *
     * @param belongCompanyCodes 分公司code
     * @return
     */
    @Override
    public List<String> getCompanyVehicle(List<String> belongCompanyCodes) {
        return this.repository.getCompanyVehicle(belongCompanyCodes);
    }
    @Override
    public InvolvesPersonnelConfiguration edit(InvolvesPersonnelConfiguration listDatum) {
        //查询 所有二级组织  并匹配拿到 code
        List<SimpleOrg> pOrgAndCityOrg = uumsSysOrgApi.findPOrgAndCityOrg(Constants.APP_CODE);
            for (SimpleOrg simpleOrg : pOrgAndCityOrg) {
                // 与现有部门、分公司名称一致
                if(StrUtil.equals(simpleOrg.getOrgName(), listDatum.getAppCities()) || StrUtil.equals(simpleOrg.getOrgName(), listDatum.getAppCities() + "分公司")){
                    listDatum.setAppCities(simpleOrg.getOrgName());
                    listDatum.setAppCitiesCode(simpleOrg.getOrgCode());
                    break;
                }
            }
            // 申请人OA账号-A角
            if(StrUtil.isEmpty(listDatum.getAppUsernameA()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailA())){
                listDatum.setAppUsernameA(listDatum.getAppLinkEmailA().split("@")[0]);
            }
            // 申请人OA账号-B角
            if(StrUtil.isEmpty(listDatum.getAppUsernameB()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailB())){
                listDatum.setAppUsernameB(listDatum.getAppLinkEmailB().split("@")[0]);
            }
            // 网络部经理
            if(StrUtil.isEmpty(listDatum.getApptUsernameVehicle()) && StrUtil.isNotEmpty(listDatum.getAppLinkEmailVehicle())){
                listDatum.setApptUsernameVehicle(listDatum.getAppLinkEmailVehicle().split("@")[0]);
            }
            // 负责人OA账号-A角
            if(StrUtil.isEmpty(listDatum.getHeadUsernameA()) && StrUtil.isNotEmpty(listDatum.getHeadLinkEmailA())){
                listDatum.setHeadUsernameA(listDatum.getHeadLinkEmailA().split("@")[0]);
            }
            // 负责人OA账号-B角
            if(StrUtil.isEmpty(listDatum.getHeadUsernameB()) && StrUtil.isNotEmpty(listDatum.getHeadLinkEmailB())){
                listDatum.setHeadUsernameB(listDatum.getHeadLinkEmailB().split("@")[0]);
            }
            // 负责人网络部经理 邮箱
            listDatum.setHeadLinkEmailVehicle(listDatum.getAppLinkEmailVehicle());
            this.update(listDatum);
        return listDatum;
    }

}
