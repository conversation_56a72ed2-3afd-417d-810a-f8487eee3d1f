package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.SatellitePortableLibrary;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:17
 * @describe 卫星便携站配置库
 */
public interface SatellitePortableLibraryRespository extends LogicRepository<SatellitePortableLibrary, String> {


    @Modifying
    @Query(value = " update US_SATELLITE_PORTABLE sp set sp.enabled='0' , sp.removed_time=sysdate where sp.id=:id ", nativeQuery = true)
    void deleteBySatellitePortableLibrary(@Param("id") String id);

    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name, "+
            "       t.chief_writing_station," +
            "       t.cities," +
            "       t.cities_code," +
            "       t.equipment_manufacturer," +
            "       t.scheduling_condition," +
            "       t.uav_station_leader_phone," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.license_plate," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.spare03 from US_SATELLITE_PORTABLE t where t.enabled='1' and t.scheduling_condition='可调度' ", nativeQuery = true)
    List<Map<String, Object>> findBySatellite();


    @Query(value = " select t.id, " +
            "                   t.created_time, " +
            "                   t.modified_time, " +
            "                   t.creator, " +
            "                   t.enabled, " +
            "                   t.modifier, " +
            "                   t.removed_time, " +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name, "+
            "                   t.chief_writing_station, " +
            "                   t.cities, " +
            "                   t.cities_code, " +
            "                   t.equipment_manufacturer, " +
            "                   t.scheduling_condition, " +
            "                   t.uav_station_leader_phone, " +
            "                   t.spare01, " +
            "                   t.spare02, " +
            "                   t.license_plate," +
            "                   t.car_configu_ration," +
            "                   t.car_configu_ration_parent," +
            "                   t.spare03 from US_SATELLITE_PORTABLE t where t.enabled='1' and t.scheduling_condition='可调度'  and t.cities=:cities", nativeQuery = true)
    List<Map<String, Object>> findBySatelliteAndCities(@Param("cities") String cities);
}
