package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.service.ISchedulingLedgerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:49
 * @describe 应急车调度台账
 */
@Api(description = "应急车调度台账")
@Slf4j
@RestController
@RequestMapping(value = "/action/schedulingLedger")
public class SchedulingLedgerController extends LogicController<SchedulingLedger, String> {

    private ISchedulingLedgerService iSchedulingLedgerService;

    public SchedulingLedgerController(ISchedulingLedgerService iSchedulingLedgerService) {
        super(iSchedulingLedgerService);
        this.iSchedulingLedgerService = iSchedulingLedgerService;
    }


//    @ApiOperation(value = "条件查询应急车调度台账", notes = "条件查询应急车调度台账")
//    @PostMapping({"/findBySchedulingLedger", "/findBySchedulingLedger/sso"})
//    public JsonResponse findBySchedulingLedger(@RequestParam Integer page,
//                                               @RequestParam Integer size,
//                                               @RequestParam(required = false) String source,
//                                               @RequestParam(required = false) String currentUserCode,
//                                               @RequestBody(required = false) Map<String, Object> paramMap) {
//        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
//        Pageable pageable = iSchedulingLedgerService.getPageable(page, size, "desc", "createdTime");
//        return iSchedulingLedgerService.findBySchedulingLedger(source, pageable, currentUserCode, paramMap);
//    }
}
