package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:58
 * @describe 4GFDD基站站号
 */
public interface FourFDDBaseStationRepository extends LogicRepository<FourFDDBaseStation, String> {


    @Modifying
    @Query(value = " update US_FOUR_F_STATION fbs set fbs.enabled='0',fbs.removed_time=sysdate where fbs.four_fdd_id=:pmInsId ", nativeQuery = true)
    void deleteByPmInsId(@Param("pmInsId") String pmInsId);


    @Query(value = "select fs.* from US_FOUR_F_STATION fs where  fs.enabled=1 and fs.four_fdd_id =:pmInsId ", nativeQuery = true)
    List<FourFDDBaseStation> findByPmInsIdFourFDDBaseStation(@Param("pmInsId") String pmInsId);
}
