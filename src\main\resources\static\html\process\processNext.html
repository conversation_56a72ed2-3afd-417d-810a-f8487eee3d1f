﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>流程下一步</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=$svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=$svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=$svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=$svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=$svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=$svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=$svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=$svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=$svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=$svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=$svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=$svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=$svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=$svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        function proNetTreeLoad(id,param,para,type){

            var href = {"location": gps.location};

            if (gps.processInstId) href.processInstId = gps.processInstId;
            href.source="PC";
            var url = tourl(param.getOrgAndUserCmd , href);//获取决策项对应待选人员接口+ (gps.from ? "/sso" : "")
            if(para.decisionId == "yjtxc.GeneralAffairs_copy" && gps.location == "yjtxc.GeneralAffairs") {
                para.organizerCode = gps.organizerCode
            }
            ajaxgeneral({
                url:url,
                data:para,
                contentType: "application/json; charset=utf-8",
                success:function(data){
                    $(window.parent.document).find("#processNextConfirm").linkbutton({disabled:false})
                    if(para.decisionId==$(".decList input:checked").attr("id") || para.decisionName.indexOf("copy#")>-1){
                        getUserOrOrg[type]=data.data;
                        // 防止重复插入元素
                        $(type=="main"?".proNetTreeFT":".copyPhT").html("");
                        $(type=="main"?".proNetTreeFD":".copyPhD").html("");
                        for(var i in data.data){
                            var typeH= type=="main"?"m":"c";
                            var treeH="<div class='treeListD hide' style='height:"+(type=="main"?"374":"98")+"px;'><div class='treeListDT' style='height:"+(type=="main"?"373":"97")+"px;'>"
                                +"<ul id='"+typeH+"tree"+i+"' singleSel='"+data.data[i].singleSel+"' requSel='"+data.data[i].requSel+"' display='"+data.data[i].display+"' page='"+data.data[i].page
                                +"' group='"+data.data[i].group+"' chooseD='"+typeH+"chooseUser"+i+"' type='"+typeH+"'></ul></div><div singleSel='"+data.data[i].singleSel+"' requSel='"+data.data[i].requSel
                                +"' display='"+data.data[i].display+"' page='"+data.data[i].page+"' group='"+data.data[i].group+"' class='chooseUser "+typeH+"chooseUser"+i+" proNetPeoF'></div></div>";
                            var listH="<div class='treeListD hide'><form id='"+typeH+"chooseUserTable"+i+"QueryForm'>"
                                +"	<table border='0' cellpadding='0' cellspacing='6' width='100%'>"
                                +"		<tr>"
                                +"			<td width='50' align='right'>姓名：</td><td width='100'><input name='username' type='text' value='' /></td>"
                                +"			<td width='50' align='right'>电话：</td><td width='120'><input name='preferredMobile' type='text' value='' /></td>"
                                +"			<td width='70'>"
                                +"				<div class='w100'>"
                                +"					<a class='btn a_primary fl searchtable'><span>查询</span></a>"
                                +"				</div>"
                                +"			</td>"
                                +"		</tr>"
                                +"	</table>"
                                +"</form>"
                                +"<div class='"+typeH+"chooseUserTable"+i+"'><table id='"+typeH+"chooseUserTable"+i+"'></table></div></div>";
                            $(type=="main"?".proNetTreeFT":".copyPhT").append("<a type='"+(type=="main"?"proNetTreeFD":"copyPhD")+"'>"+(data.data[i].group=="normalGrouping"?"待选人员":data.data[i].group)+"</a>");
                            $(type=="main"?".proNetTreeFD":".copyPhD").append(data.data[i].page.indexOf("tree")>-1?treeH:listH);
                            if(data.data[i].page.indexOf("tree")>-1){
                                proNextTree(typeH+"tree"+i,data.data[i].user,para,param);
                            }else{
                                proNextTab(typeH+"chooseUserTable"+i,data.data[i].user,para,param);
                            }
                        }
                        if(para.decisionConfigCopyInfo && gps.type=="task"){
                            $("div#processNext").resizable("enable");
                            $("div#processNext").css({"height":"287px","border-bottom":"0px"});
                            $(".proNetTreeF").css("hieght","225px");
                            $(".proNetTreeFD .treeListD").css("height","209px");
                            $(".proNetTreeFD .treeListD .treeListDT").css("height","208px");
                            $(".resizableR").css("width","682px");
                            $(".copyD").show();
                        }
                        $(".proNetTreeFT a:first,.copyPhT a:first").trigger("click");
                    }
                }
            });
        };
        $(function(){
            var param={
                "getDecisionsCmd":"action/applicationForm/getDecisions?applyType="+gps.applyType,
                "getOrgAndUserCmd":"action/applicationForm/getOrgAndUser"
            };
            if(gps.phone) {
                param.getOrgAndUserCmd = "action/applicationForm/getOrgAndUser?phone=" + gps.phone + "&belongCompanyCode=" + gps.belongCompanyCode
            }
            loadProcessNext(param);

        });
    </script>
</head>
<body>
<div id="processNext" class="easyui-resizable" data-options="handles:'s',onStopResize:onStopResize" style="width:942px;height:287px;border-bottom:5px solid #eee;">
    <input id="copyLocation" name="copyLocation" type="hidden" />
    <div style="width:260px;float:left;">
        <h6 class="proNxtTit">填写意见</h6>
        <div style="padding:0 20px 0 0;">
            <fieldset class="title titleA mt10 p10 proNetOptF" style="height:240px;">
                <legend><font class="f14">决策项</font></legend>
                <div class="decList" style="padding:8px 10px 0px 20px;"></div>
            </fieldset>
            <fieldset class="title titleA mt10 p10 proNetMesF" style="height:150px;">
                <legend><font class="f14">意见内容</font></legend>
                <textarea id="message" name="message" style="width: 100%; height: 98px; resize: none;"></textarea>
            </fieldset>
        </div>
    </div>
    <div class="resizableR" style="width:682px;float:left;">
        <h6 class="proNxtTit toDoTitle">办理人</h6>
        <div class="proNetTreeF titleA" style="margin-top:20px;">
            <div class="proNetTreeFT"></div>
            <div class="proNetTreeFD"></div>
        </div>
    </div>
</div>
<div class="copyD" style="width:942px;height:175px;overflow:hidden;">
    <div style="width:260px;float:left;">
        <h6 class="proNxtTit copyH">抄送</h6>
        <div class="copyH" style="padding:0 20px 0 0;">
            <fieldset class="title titleA mt10 p10 copyPh" style="height:124px;">
                <legend><font class="f14">抄送意见内容</font></legend>
                <textarea id="copyMessage" name="copyMessage" style="width: 100%; height: 80px; resize: none;"></textarea>
            </fieldset>
        </div>
    </div>
    <div style="width:682px;float:left;">
        <h6 class="proNxtTit copyH">抄送人</h6>
        <div class="copyPhF titleA" style="margin-top:20px;">
            <div class="copyPhT"></div>
            <div class="copyPhD"></div>
        </div>
    </div>
</div>
</body>
</html>
