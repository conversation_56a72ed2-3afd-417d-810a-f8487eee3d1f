package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.InvolvesPersonnelConfiguration;
import com.simbest.boot.yjtxc.apply.service.IInvolvesPersonnelConfigurationService;
import com.simbest.boot.yjtxc.util.FileTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 16:25
 * @describe 流程固化涉及人员配置
 */
@Api(description = "流程固化涉及人员配置")
@Slf4j
@RestController
@RequestMapping(value = "/action/involvesPersonnel")
public class InvolvesPersonnelConfigurationController extends LogicController<InvolvesPersonnelConfiguration, String> {

    private IInvolvesPersonnelConfigurationService service;


    public InvolvesPersonnelConfigurationController(IInvolvesPersonnelConfigurationService service) {
        super(service);
        this.service = service;
    }

    /**
     * 流程固化涉及人员配置
     *
     * @param page
     * @param size
     * @param source
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @ApiOperation(value = "条件查询流程固化涉及人员配置", notes = "条件查询流程固化涉及人员配置")
    @PostMapping({"/findByAllInvolvesPersonnelConfiguration", "/findByAllInvolvesPersonnelConfiguration/sso"})
    public JsonResponse findByAllInvolvesPersonnelConfiguration(@RequestParam Integer page,
                                                                @RequestParam Integer size,
                                                                @RequestParam(required = false) String source,
                                                                @RequestParam(required = false) String currentUserCode,
                                                                @RequestBody(required = false) Map<String, Object> paramMap) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, "desc", "createdTime");
        return service.findByAllInvolvesPersonnelConfiguration(source, pageable, currentUserCode, paramMap);
    }

    /**
     * 导入流程固化涉及人员配置信息
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导入流程固化涉及人员配置信息", notes = "导入卫星便携站配置库信息")
    @PostMapping({"/importPerson", "/importPerson/sso"})
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return service.importPerson(request, response);

    }

    @ApiOperation(value = "修改信息", notes = "修改信息")
    @PostMapping({"/edit", "/edit/sso"})
    public JsonResponse edit(@RequestBody InvolvesPersonnelConfiguration map) throws Exception {
        return JsonResponse.success(service.edit(map));

    }

    /**
     * 批量保存流程固化涉及人员配置信息
     *
     * @param involvesPersonnelConfigurationsList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量保存流程固化涉及人员配置信息", notes = "批量保存流程固化涉及人员配置信息")
    @PostMapping({"/saveInvolvesPersonnelConfiguration", "/saveInvolvesPersonnelConfiguration/sso"})
    public JsonResponse saveInvolvesPersonnelConfiguration(@RequestBody List<InvolvesPersonnelConfiguration> involvesPersonnelConfigurationsList) throws Exception {
        return service.saveInvolvesPersonnelConfiguration(involvesPersonnelConfigurationsList);

    }

    /**
     * 下载流程固化涉及人员配置模板
     *
     * @param response
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = {"/downloadTemplate", "/downloadTemplate/sso"}, method = RequestMethod.POST)
    public Map<String, Object> downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/流程固化涉及人员配置模板导入.xls");
        FileTool.downloadIn(in, "流程固化涉及人员配置模板导入.xls", response);
        return null;
    }

}
