package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.GarageConfiguration;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:31
 * @describe 应急车库配置持久层
 */
public interface GarageConfigurationRepository  extends LogicRepository<GarageConfiguration, String> {


    @Modifying
    @Query(value = " update us_garage_confi gc set gc.scheduling_condition=:flag where gc.id=:id ", nativeQuery = true)
    void updateGarageConfigurationSchedulingCondition(@Param("flag") String flag,@Param("id") String id);

    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.cities," +
            "       t.cities_code," +
            "       t.enable_localhost," +
            "       t.equipment_manufacturer," +
            "       t.five_base_station," +
            "       t.fivereversefour," +
            "       t.fivetransform," +
            "       t.fourfddbase_station," +
            "       t.fourtddbase_station," +
            "       t.license_plate," +
            "       t.opening_station," +
            "       t.opening_station_phone," +
            "       t.pm_ins_id," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.the_driver," +
            "       t.the_driver_phone from US_GARAGE_CONFI t where t.enabled='1' and t.scheduling_condition='可调度' ", nativeQuery = true)
    List<Map<String, Object>> findByBigCar();


    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.cities," +
            "       t.cities_code," +
            "       t.enable_localhost," +
            "       t.equipment_manufacturer," +
            "       t.five_base_station," +
            "       t.fivereversefour," +
            "       t.fivetransform," +
            "       t.fourfddbase_station," +
            "       t.fourtddbase_station," +
            "       t.license_plate," +
            "       t.opening_station," +
            "       t.opening_station_phone," +
            "       t.pm_ins_id," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.the_driver," +
            "       t.the_driver_phone from US_GARAGE_CONFI t where t.enabled='1' and t.scheduling_condition='可调度' and t.car_configu_ration='卫星车' ", nativeQuery = true)
    List<Map<String, Object>> findByGSMCar();



    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.cities," +
            "       t.cities_code," +
            "       t.enable_localhost," +
            "       t.equipment_manufacturer," +
            "       t.five_base_station," +
            "       t.fivereversefour," +
            "       t.fivetransform," +
            "       t.fourfddbase_station," +
            "       t.fourtddbase_station," +
            "       t.license_plate," +
            "       t.opening_station," +
            "       t.opening_station_phone," +
            "       t.pm_ins_id," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.the_driver," +
            "       t.the_driver_phone from US_GARAGE_CONFI t where t.enabled='1' and t.scheduling_condition='可调度'  and t.cities=:cities", nativeQuery = true)
    List<Map<String, Object>> findByBigCarAndCities(@Param("cities") String cities);



    @Query(value = " select t.id," +
            "       t.created_time," +
            "       t.modified_time," +
            "       t.creator," +
            "       t.enabled," +
            "       t.modifier," +
            "       t.removed_time," +
            "       t.belong_company_code," +
            "       t.belong_company_name," +
            "       t.belong_company_type_dict_value," +
            "       t.belong_department_code," +
            "       t.belong_department_name," +
            "       t.belong_org_code," +
            "       t.belong_org_name," +
            "       t.car_configu_ration," +
            "       t.car_configu_ration_parent," +
            "       t.cities," +
            "       t.cities_code," +
            "       t.enable_localhost," +
            "       t.equipment_manufacturer," +
            "       t.five_base_station," +
            "       t.fivereversefour," +
            "       t.fivetransform," +
            "       t.fourfddbase_station," +
            "       t.fourtddbase_station," +
            "       t.license_plate," +
            "       t.opening_station," +
            "       t.opening_station_phone," +
            "       t.pm_ins_id," +
            "       t.scheduling_condition," +
            "       t.spare01," +
            "       t.spare02," +
            "       t.spare03," +
            "       t.the_driver," +
            "       t.the_driver_phone from US_GARAGE_CONFI t where t.enabled='1' and t.scheduling_condition='可调度'and t.car_configu_ration='卫星车' and t.cities=:cities", nativeQuery = true)
    List<Map<String, Object>> findByGSMCarAndCities(@Param("cities") String cities);


    @Query(value = " select t.* from US_GARAGE_CONFI t WHERE  t.the_driver_phone= :phoneNmu and t.car_configu_ration = :vehicle", nativeQuery = true)
    List<GarageConfiguration> findByIdddd(@Param("phoneNmu") String phoneNmu,@Param("vehicle") String vehicle);

    @Query(value = " select t.* from US_GARAGE_CONFI t WHERE  t.the_driver_phone= :phoneNmu   and t.enabled = '1'", nativeQuery = true)
    GarageConfiguration findByPonte(@Param("phoneNmu") String phoneNmu);

    /**
     * 根据车牌号和手机号查询
     * @param phoneNmu
     * @param protaion
     * @return
     */
    @Query(value = " select t.* from US_GARAGE_CONFI t WHERE  t.the_driver_phone= :phoneNmu  and t.license_plate= :protaion  and t.enabled = '1'", nativeQuery = true)
    GarageConfiguration findByPonteLiattion(@Param("phoneNmu")String phoneNmu, @Param("protaion")String protaion);


    @Query(
            value = " select t.* from US_GARAGE_CONFI t WHERE  t.the_driver_phone= ?1   and t.enabled = '1'",
            nativeQuery = true)
    List<GarageConfiguration> findByPonteList(String phoneNmu);

    /**
     * 查询所有不重复的应急设备大类
     * @return 应急设备大类列表
     */
    @Query(value = "SELECT DISTINCT t.car_configu_ration_parent FROM US_GARAGE_CONFI t " +
            "WHERE t.enabled = '1' AND t.car_configu_ration_parent IS NOT NULL " +
            "ORDER BY t.car_configu_ration_parent", nativeQuery = true)
    List<String> findDistinctCarConfiguRationParent();


    /**
     * 查询所有不重复的应急设备子类
     * @return 应急设备大类列表
     */
    @Query(value = "SELECT DISTINCT t.car_configu_ration FROM US_GARAGE_CONFI t " +
            "WHERE t.enabled = '1' AND t.car_configu_ration IS NOT NULL " +
            "ORDER BY t.car_configu_ration", nativeQuery = true)
    List<String> findDistinctCarConfiguRation();
}
