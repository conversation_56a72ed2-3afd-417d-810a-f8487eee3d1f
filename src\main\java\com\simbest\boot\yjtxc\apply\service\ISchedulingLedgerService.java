package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import org.springframework.data.domain.Pageable;

import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:50
 * @describe 应急车调度台账
 */

public interface ISchedulingLedgerService extends ILogicService<SchedulingLedger, String> {

    /**
     * 保存台账
     * @param applicationForm
     * @return
     */
    //Boolean saveParameter(ApplicationForm applicationForm);

    /**
     * 条件查询应急车调度台账
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    //JsonResponse findBySchedulingLedger(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap);
}
