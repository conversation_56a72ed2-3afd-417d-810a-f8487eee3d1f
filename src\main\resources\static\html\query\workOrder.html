
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待阅列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .w100 a {
            min-width: 66px;
        }
    </style>

    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#workOrderTable",
                "querycmd":"action/applicationForm/findByCount?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "frozenColumns":[],
                "columns":[[
                    { title: "工单标题", field: "ORDER_TITLE", width: 200, rowspan: 1},
                    { title: "工单编号", field: "ORDER_NUMBER", width: 200,tooltip:true},
                    { title: "流程类型", field: "applicationType", width: 100,
                        formatter: function(value, row, index) {
                            var th=appNameTH(web.appName,web.appHtml,row.PROCESS_TYPE);
                            return th.type
                        }
                    },
                    { title: "申请应急设备配置", field: "DEVICE_CONFIGURATION", width: 100 },
                    { title: "应急车车牌号", field: "LICENSE_PLATE", width: 100 },
                    { title: "活动名称", field: "NAME_EVENT", width: 100 },
                    { title: "保障开始时间", field: "GUARANTEE_START_TIME", width: 150
                        /*formatter: function(value, row, index) {
                            var time = getTimeDate(value, "yyyy-MM-dd hh:mm:ss");
                            return time.substring(0 , time.length - 3);
                        }*/
                    },
                    {
                        title: "保障结束时间", field: "GUARANTEE_END_TIME", width: 150
                        /*formatter: function (value, row, index) {
                            var time = getTimeDate(value, "yyyy-MM-dd hh:mm:ss");
                            return time.substring(0 , time.length - 3);
                        }*/
                    },
                    { title: "申请组织", field: "APPLICANT_UNIT", width: 150 },
                    { title: "申请人", field: "USER_NAME", width: 100 },
                    { title: "申请发起时间", field: "CREATEDTIME", width: 100},
                    { title: "当前环节", field: "ACTIVITY_INST_NAME", width: 100,
                        formatter: function(value, row, index) {
                            return row.CURRENT_STATE == 7 ? "已归档" : value
                        }
                    },
                    { title: "操作", field: "opt", width: 100,
                        formatter: function(value, row, index) {
                            return "<a href='#' orderTitle='"+row.ORDER_TITLE+"' processType='"+row.PROCESS_TYPE+"' location='"+row.ACTIVITY_DEF_ID+"' pmInsId='"+row.PM_INS_ID+"' onclick='toDetail(this)'>查看</a>"
                        }
                    }
                ]]
            }
        };
        $(function(){
            loadGrid(pageparam);
            $(".export").click(function () {
                $("#workOrderTableQueryForm").attr({"action":web.rootdir + "action/applicationForm/exportOrder","method":"post"});
                $("#workOrderTableQueryForm").submit()
            })
        });
        function toDetail(param) {
            var th=appNameTH(web.appName,web.appHtml,$(param).attr("processType"))
            var url = th.html + "?location=" + $(param).attr("location") + "&pmInsId=" + $(param).attr("pmInsId") + "&type=workOrder"
            top.dialogP(url, window.name, $(param).attr("orderTitle"), "detailCB", true, "maximized")
        }
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="workOrderTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="80" align="right">申请组织：</td>
            <td width="180">
                <input class="applicantUnit easyui-combobox" name="applicantUnit" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_type'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td width="80" align="right">工单标题：</td>
            <td width="180">
                <input name="orderTitle" type="text" value="" class="easyui-validatebox" />
            </td>
            <td width="80" align="right">工单编号：</td>
            <td width="180">
                <input name="pmInsId" type="text" value="" class="easyui-validatebox" />
            </td>
            <td width="80" align="right">车牌号：</td>
            <td width="180">
                <input name="licensePlate" type="text" value="" class="easyui-validatebox" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl ml15 searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
        <tr>
            <td width="80" align="right">申请人：</td>
            <td width="180">
                <input name="trueName" type="text" value="" class="easyui-validatebox" />
            </td>
            <td width="80" align="right">流程类型：</td>
            <td width="180">
                <input class="processType easyui-combobox" name="processType" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_processType'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
           <!-- <td></td>
            <td></td>-->
            <td width="80" align="right">活动名称：</td>
            <td width="180">
                <input name="nameEvent" type="text" value="" class="easyui-validatebox" />
            </td>

            <td width="80" align="right">保障时间：</td>
            <td width="120">
                <input name="guaranteeStartTime" type="text" class="easyui-datebox"
                       data-options="panelHeight:'auto',editable:false"
                       style="width:100%;height:32px;"/>
            </td>

            <td>
                <div class="w100">
                    <a class="btn fl ml15 a_warning export"><font>导出</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="workOrderTable"><table id="workOrderTable"></table></div>
</body>
</html>
