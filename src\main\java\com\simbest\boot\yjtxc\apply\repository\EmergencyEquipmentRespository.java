package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:47
 * @describe 应急设备
 */
public interface EmergencyEquipmentRespository extends LogicRepository<EmergencyEquipment, String> {

    @Query(value = " select ee.* " +
            "  from US_EMERGENCY_EQUIPMENT ee" +
            " where ee.enabled = '1'" +
            "   and ee.ee_id = :pmInsId",
            nativeQuery = true)
    List<EmergencyEquipment> findByPmInsId(@Param("pmInsId") String pmInsId);

    /**
     * 通过id查询
     * @param pmInsId
     * @return
     */
    @Query(value = " select ee.* from US_EMERGENCY_EQUIPMENT ee  where ee.enabled=1 and  ee.ee_id=:pmInsId ", nativeQuery = true)
    EmergencyEquipment getEByPmInsId(@Param("pmInsId") String pmInsId);



    @Modifying
    @Query(value = " delete from US_EMERGENCY_EQUIPMENT ee where ee.ee_id=:pmInsId ", nativeQuery = true)
    void deleteByPmInsId(@Param("pmInsId") String pmInsId);

    @Query(value = "select t.*, rowID" +
            "  from US_EMERGENCY_EQUIPMENT t" +
            " where t.license_plate = :licensePlate" +
            "   and t.cities = :cities" +
            "      and t.enabled='1'" +
            "   and t.end_time >= :today ",
            nativeQuery = true)
    EmergencyEquipment findByGarageConfiguration(@Param("licensePlate") String licensePlate, @Param("cities") String cities, @Param("today") String today);

    /**
     * 根据主单据ID删除 旧数据
     * @param pmInsId 主单据ID
     */
    @Modifying
    @Query(value = "update US_EMERGENCY_EQUIPMENT ee set ee.enabled = 0 , ee.removed_time = sysdate where ee.ee_id=:pmInsId and ee.enabled='1' ", nativeQuery = true)
    void deleteBypmIntId(@Param("pmInsId") String pmInsId);

    @Query(value = "select * from US_EMERGENCY_EQUIPMENT t where t.license_plate=:licensePlate and t.cities=:belongCompanyName and t.enabled='1'  " +
            "   and t.archive_Time>:startTime and t.start_time <:endTime and t.end_State is null", nativeQuery = true)
    List<Map<String, Object>> findByCarTime(@Param("licensePlate") String licensePlate, @Param("belongCompanyName") String belongCompanyName, @Param("startTime") String startTime,@Param("endTime") String endTime);



    /**
     * 查询四种车辆
     * @param pmInsId
     * @param bxz
     * @return
     */
    @Query(value = " select ee.* " +
            "  from US_EMERGENCY_EQUIPMENT ee" +
            " where ee.enabled = '1'" +
            "   and ee.ee_id = :pmInsId"+
            " and  ee.car_configu_ration = :bxz",
            nativeQuery = true)
    List<EmergencyEquipment> findByPmInsIdC(@Param("pmInsId")String pmInsId, @Param("bxz")String bxz);


    @Query(value = " select ee.* from US_EMERGENCY_EQUIPMENT ee  where ee.enabled=1 and  ee.the_driver_phone= :phoneNmu and ee.ee_id = :pmInsId ", nativeQuery = true)
    EmergencyEquipment findddd(@Param("phoneNmu")String phoneNmu,@Param("pmInsId")String pmInsId);

    @Query(value = " select ee.* from US_EMERGENCY_EQUIPMENT ee  where ee.enabled=1 and  ee.the_driver_phone= ?1 and ee.ee_id =?2 ", nativeQuery = true)
    List<EmergencyEquipment> finEmfindList(String phoneNmu, String pmInsId);

    /**
     * 根据车牌号和查询时间来获取信息
     *
     * @param licensePlate
     * @param
     * @return
     */
    @Query(value = "select * from US_EMERGENCY_EQUIPMENT t where t.license_plate=:licensePlate and :chooseTime between  t.start_time and t.end_time and t.enabled='1'", nativeQuery = true)
    List<EmergencyEquipment> findByLicensePlateAndChooseTime(@Param("licensePlate") String licensePlate, @Param("chooseTime") String chooseTime);

    /**
     * 根据车牌号获取信息
     *
     * @param licensePlate
     * @param
     * @return
     */
    @Query(value = "select * from US_EMERGENCY_EQUIPMENT t where t.license_plate=:licensePlate and t.enabled='1'", nativeQuery = true)
    List<EmergencyEquipment> findByLicensePlate(@Param("licensePlate") String licensePlate);


    @Query(value = " select ee.* " +
            "  from US_EMERGENCY_EQUIPMENT ee" +
            " where ee.enabled = '1'" +
            "   and ee.ee_id = ?1"+
            " and  ee.car_configu_ration = ?2",
            nativeQuery = true)
    List<EmergencyEquipment> findByPmInsIdAndD(String pmInsId , String bxz);
    @Modifying
    @Query(value = " update US_EMERGENCY_EQUIPMENT ee set ee.archive_Time = :archiveTime where ee.ee_id=:pmInsId ", nativeQuery = true)
    void updateArchiveTimeByPmInsId(@Param("archiveTime")String archiveTime, @Param("pmInsId")String pmInsId);

    @Query(value = " select ee.* " +
            "  from US_EMERGENCY_EQUIPMENT ee" +
            " where ee.enabled = '1'" +
            "   and ee.ee_id = :pmInsId",
            nativeQuery = true)
    List<EmergencyEquipment> findByAll(@Param("pmInsId")String pmInsId);

    @Query(value = " select ee.* " +
            "  from US_EMERGENCY_EQUIPMENT ee" +
            " where ee.enabled = '1'" +
            "   and ee.ee_id = ?1",
            nativeQuery = true)
    List<EmergencyEquipment> findByPmInsIdAll(String pmInsId);
}
