package com.simbest.boot.yjtxc.wfquey.web;

import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.wfquey.service.IQueryDictValueService;
import com.simbest.boot.sys.model.SysDictValue;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  用途：数据字典 控制层
 *  作者：zhangshaofeng
 *  时间：2018/07/09
 */
@Api(description = "数据字典相关接口")
@Slf4j
@RestController
@RequestMapping(value = "/action/queryDictValue")
public class QueryDictValueController {

    @Autowired
    private IQueryDictValueService queryDictValueService;

    /**
     * 数据字典查询
     * @param dictType
     * @return
     */
    @ApiOperation(value = "查询来源类型", notes = "数据字典中查询类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dictType", value = "数据类型", dataType = "String", paramType = "query"),
    })
    @PostMapping(value ={"/queryByType","/api/queryByType","/queryByType/sso"} )
    public JsonResponse queryByType(@RequestParam String dictType){
        return JsonResponse.success(queryDictValueService.queryByType(dictType));
    }

}
