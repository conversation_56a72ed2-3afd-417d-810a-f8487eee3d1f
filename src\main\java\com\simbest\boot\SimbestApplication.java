package com.simbest.boot;

import com.simbest.boot.base.repository.CustomJpaRepositoryFactoryBean;
import com.simbest.boot.security.IUser;
import com.simbest.boot.util.redis.RedisUtil;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.context.WebServerApplicationContext;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.boot.web.server.WebServer;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.net.InetAddress;

/**
 * <strong>Title</strong> : SimbestApplication.java<br>
 * <strong>Description</strong> : <br>
 * <strong>Create on</strong> : 2018/1/15<br>
 * <strong>Modify on</strong> : 2018/1/15<br>
 * <strong>Copyright (C) ___ Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version v0.0.1
 */
@Slf4j
@SpringBootApplication//(exclude= OAuth2AutoConfiguration.class)  //OAuth2MethodSecurityExpressionHandler
@ServletComponentScan
@EnableJpaRepositories(repositoryFactoryBeanClass = CustomJpaRepositoryFactoryBean.class)
@EntityScan(basePackageClasses = { SimbestApplication.class, Jsr310JpaConverters.class })
public class SimbestApplication implements ApplicationListener<WebServerInitializedEvent> {

    @Autowired
    private ApplicationContext appContext;


    /**
     * @param args 默认参数
     */
    public static void main(String[] args) {
        SpringApplication.run(SimbestApplication.class, args);
    }

    @SneakyThrows
    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        String[] activeProfiles = appContext.getEnvironment().getActiveProfiles();
        for (String profile : activeProfiles) {
            log.warn("加载环境信息为: 【{}】", profile);
            log.warn("Application started successfully, lets go and have fun......");
        }
        WebServer server = event.getWebServer();
        WebServerApplicationContext context = event.getApplicationContext();
        Environment env = context.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        int port = server.getPort();
        String contextPath = env.getProperty("server.servlet.context-path");
        contextPath = StringUtils.isEmpty(contextPath) ? "":contextPath;
        log.warn("\n---------------------------------------------------------\n" +
                "\t应用已成功启动，运行地址如下：:\n" +
                "\tLocal:\t\thttp://localhost:{}{}" +
                "\n\tExternal:\thttp://{}:{}{}" +
                "\nAplication started successfully, lets go and have fun......" +
                "\n---------------------------------------------------------\n", port, contextPath, ip, port, contextPath);
    }

}

