package com.simbest.boot.yjtxc.todo.service.impl;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.yjtxc.todo.model.UsTodoModel;
import com.simbest.boot.yjtxc.todo.repository.UsTodoModelRepository;
import com.simbest.boot.yjtxc.todo.service.IUsTodoModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <strong>Title : UsTodoModelServiceImpl</strong><br>
 * <strong>Description : 用户业务待办业务操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service
public class UsTodoModelServiceImpl extends LogicService<UsTodoModel,Long> implements IUsTodoModelService {

    private UsTodoModelRepository usTodoModelRepository;

    @Autowired
    public UsTodoModelServiceImpl(UsTodoModelRepository repository){
        super(repository);
        this.usTodoModelRepository = repository;
    }

    /**
     * 保存推送待办数据到本地
     * @param usTodoModel      待办对象
     * @return
     */
    @Override
    public UsTodoModel savaLocalTodoData(UsTodoModel usTodoModel){
        try {
            usTodoModel.setEnabled( true );
            return usTodoModelRepository.save( usTodoModel );
        }catch ( Exception e ){
            log.debug( "UsTodoModelServiceImpl>>>>>>>openTodo>>>>>调用接口平台推送统一代办异常" + e.getMessage());
            Exceptions.printException(e);
        }
        return null;
    }
}
