
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>导入操作</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <!--    <link href="${ctx}../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet"/>-->
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .satellite,
        .uav,
        .process,
        .car {
            display: none;
        }
    </style>
    <script type="text/javascript">
    var uavCols = [
        [
            { title: "地市", field: "cities", width: 150, rowspan: 2, align: "center"},
            { title: "重保分队装备", width: 150,tooltip:true,colspan: 5, align: "center" }
            // { title: "调度状态", field: "schedulingCondition", width: 150,tooltip:true, rowspan: 2, align: "center" }
        ],
        [
            { title: "设备厂家", field: "equipmentManufacturer", width: 150,tooltip:true, align: "center" },
            { title: "无人机高空站配置库编号", field: "licensePlate", width: 150,tooltip:true, align: "center" },
            { title: "无人机高空站类型", field: "carConfiguRation", width: 150,tooltip:true, align: "center" },
            { title: "无人机高空基站负责人", field: "uavStationLeader", width: 150,tooltip:true, align: "center" },
            { title: "负责人手机号", field: "uavStationLeaderPhone", width: 150,tooltip:true, align: "center" }
        ]
    ]
    var satelliteCols = [
        [
            { title: "地市", field: "cities", width: 150, rowspan: 2, align: "center"},
            { title: "重保分队装备", width: 150,tooltip:true,colspan:5, align: "center" },
            // { title: "调度状态", field: "schedulingCondition", width: 150,tooltip:true, rowspan: 2, align: "center" }
        ],
        [
            { title: "设备厂家", field: "equipmentManufacturer", width: 200,tooltip:true, align: "center" },
            { title: "卫星便携站编号", field: "licensePlate", width: 200,tooltip:true, align: "center" },
            { title: "卫星便携站配置类型", field: "carConfiguRation", width: 200,tooltip:true, align: "center" },
            { title: "卫星便携站负责人", field: "chiefWritingStation", width: 200,tooltip:true, align: "center" },
            { title: "负责人手机号", field: "uavStationLeaderPhone", width: 200,tooltip:true, align: "center" }
        ]
    ]
    var processCols = [
        [
            { title: "地市公司", field: "appCities", width: 150, rowspan: 2, align: "center"},
            { title: "应急车申请人", width: 150,tooltip:true,colspan: 6, align: "center" },
            { title: "网络部经理", width: 150,tooltip:true,colspan: 3, align: "center" },
            { title: "应急车负责人（开站人）", width: 150,tooltip:true,colspan: 6, align: "center" },
            { title: "网络部经理", width: 150,tooltip:true,colspan: 3, align: "center" },
            { title: "应急配置类型", field: "emerConfiType", width: 150,tooltip:true, rowspan: 2, align: "center" },
            { title: "对应车牌号", field: "correspondsNumber", width: 150,tooltip:true, rowspan: 2, align: "center" },
            { title: "设备厂家", field: "equipmentManu", width: 150,tooltip:true, rowspan: 2, align: "center" }
        ],
        [
            { title: "姓名（A角）", field: "appUsernameA", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "appphoneNumA", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "appLinkEmailA", width: 150,tooltip:true, align: "center" },
            { title: "姓名（B角）", field: "appTruenameB", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "appPhoneNumB", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "appLinkEmailB", width: 150,tooltip:true, align: "center" },
            { title: "姓名", field: "appTruenameVehicle", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "appPhoneVehicle", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "appLinkEmailVehicle", width: 150,tooltip:true, align: "center" },
            { title: "姓名（A角）", field: "headTruenameA", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "headPhoneNumA", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "headLinkEmailA", width: 150,tooltip:true, align: "center" },
            { title: "姓名（B角）", field: "headTruenameB", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "headPhoneNumB", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "headLinkEmailB", width: 150,tooltip:true, align: "center" },
            { title: "姓名", field: "headTruenameVehicle", width: 100,tooltip:true, align: "center" },
            { title: "电话", field: "headPhoneNumVehicle", width: 150,tooltip:true, align: "center" },
            { title: "邮件", field: "headLinkEmailVehicle", width: 150,tooltip:true, align: "center" }
        ]
    ]
    var carCols = [
        [
            { title: "地市", field: "cities", width: 100, rowspan: 3, align: "center"},
            { title: "设备厂家", field: "equipmentManufacturer", width: 100,tooltip:true,rowspan: 3, align: "center" },
            { title: "应急配置类型", field: "carConfiguRation", width: 100,tooltip:true,rowspan: 3, align: "center" },
            { title: "重保分队装备", width: 150,tooltip:true,colspan: 5, align: "center" },
            { title: "5G改造情况", field: "fivetransform", width: 100,tooltip:true,rowspan: 3, align: "center" },
            { title: "站号", width: 150,tooltip:true,colspan: 4, align: "center" },
            // { title: "调度状态", field: "schedulingCondition", width: 100,tooltip:true,rowspan: 3, align: "center" }
        ],
        [
            { title: "大型应急车+卫星车", width: 100, colspan: 5, tooltip:true, align: "center" },
            { title: "5G基站站号", field: "fiveBaseStation", width: 130, rowspan: 2, tooltip:true, align: "center"},
            { title: "5G反开4G基站站号", field: "fivereversefour", width: 130, rowspan: 2, tooltip:true, align: "center" },
            { title: "4G FDD 基站站号", field: "fourFDDBaseStation", width: 130, rowspan:2, tooltip:true, align: "center"},
            { title: "4G TDD 基站站号", field: "fourTDDBaseStation", width: 130, rowspan:2, tooltip:true, align: "center"},
        ],
        [
            { title: "应急车车牌号", field: "licensePlate", width: 100, tooltip:true, align: "center" },
            { title: "司机", field: "theDriver", width: 100, tooltip:true, align: "center" },
            { title: "司机手机号", field: "theDriverPhone", width: 100, tooltip:true, align: "center" },
            { title: "负责开站人员", field: "openingStation", width: 100, tooltip:true, align: "center" },
            { title: "开站人员手机号", field: "openingStationPhone", width: 100, tooltip:true, align: "center" }
        ]
    ]
    var gps = getQueryString()
    $(function () {
        $("."+gps.type).show()
        loadForm('form')
    });
    function uavImportCB(data) {
        loadTable(data, uavCols)
    }
    function satelliteImportCB(data) {
        loadTable(data, satelliteCols)
    }
    function processImportCB(data, $t) {
        loadTable(data, processCols, true)
    }
    function carImportCB(data) {
        loadTable(data, carCols)
    }
    function loadTable(data, cols, fit) {
        top.mesShow("温馨提示！", data.message, 2000, data.message == "上传成功" ? "green" : "red")
        var params = {
            "listtable":{
                "listname":"#lzTable",
                "data":{data:{listData:data.data.listData,total:data.data.listData.length}},
                "nowrap": true,
                "styleClass":"noScroll",
                "loadFilter": pagerFilter,
                "fitColumns": fit?false:true,
                "frozenColumns":[],
                "columns": cols
            }
        }
        loadGrid(params)
    }

    window.getchoosedata=function(){
        var importData = $('#lzTable').datagrid('getData').data.originalRows;
        return {"data":importData,"state":1};
    };
</script>
</head>
<body>
<div>
    <form id="form" method="post">
        <fieldset class="title">
            <legend><font>友情提示</font></legend><br>
            <font class="ml15 f12 col_r"><strong>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1、导入excel需要根据模板填写，不能含有公式。</br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2、IE8及IE9，请双击鼠标进行上传。</strong></font>
        </fieldset>
        <table border="0" cellpadding="0" cellspacing="10" width="100%">
            <tr>
                <td align="right" width="140" class="lh32"></td>
                <td class="uav" width="320" valign="top"><!-- extension="jpg,png,jpeg,gif"-->
                    <input id="uav" name="uav" type="text" class="cselectorImageUpload" btnmsg="选择文件"
                           extension="xls,xlsx" href="action/uavAerialConfigurationStation/importPerson"
                           file="list" otherinfo="uavImportCB"/>
                </td>
                <td class="satellite" width="320" valign="top">
                    <input id="satellite" name="satellite" type="text" class="cselectorImageUpload" btnmsg="选择文件"
                           extension="xls,xlsx" href="action/satellitePortableLibrary/importPerson"
                           file="list" otherinfo="satelliteImportCB"/>
                </td>
                <td class="process" width="320" valign="top">
                    <input id="process" name="process" type="text" class="cselectorImageUpload" btnmsg="选择文件"
                           extension="xls,xlsx" href="action/involvesPersonnel/importPerson"
                           file="list" otherinfo="processImportCB"/>
                </td>
                <td class="car" width="320" valign="top">
                    <input id="car" name="car" type="text" class="cselectorImageUpload" btnmsg="选择文件"
                           extension="xls,xlsx" href="action/garageConfiguration/importPerson"
                           file="list" otherinfo="carImportCB"/>
                </td>
            </tr>
        </table>
    </form>
    <div class="lzTable" style="margin-top: 50px;">
        <table id="lzTable"></table>
    </div>
</div>
</body>
</html>
