<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>应急设备配置库</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .col_b {
            color: rgb(60,185,252);
            cursor: pointer;
        }
    </style>
    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#carTable",
                "querycmd":"action/garageConfiguration/findByAllGarageConfiguration?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "fitColumns": true,
                "frozenColumns":[],
                "columns":[
                    [
                        { title: "地市", field: "cities", width: 100, rowspan: 3, align: "center"},
                        { title: "设备厂家", field: "equipmentManufacturer", width: 100,tooltip:true,rowspan: 3, align: "center" },
                        { title: "应急设备分类", field: "carConfiguRationParent", width: 120,tooltip:true,rowspan: 3, align: "center" },
                        { title: "应急设备子类", field: "carConfiguRation", width: 100,tooltip:true,rowspan: 3, align: "center" },
                        { title: "重保分队装备", width: 150,tooltip:true,colspan: 5, align: "center" },
                        { title: "5G改造情况", field: "fivetransform", width: 100,tooltip:true,rowspan: 3, align: "center" },
                        { title: "站号", width: 150,tooltip:true,colspan: 4, align: "center" },
                        { title: "调度状态", field: "schedulingCondition", width: 100,tooltip:true,rowspan: 3, align: "center" },
                        { title: "操作", field: "opt", width: 150, rowspan: 3, align: "center",
                            formatter: function(value, row, index) {
                                var g = []
                                g.push("<a href='#' onclick='optState(this)' id='"+row.id+"'>停用</a>")
                                g.push("<a href='#' onclick='optState(this)' id='"+row.id+"'>启用</a>")
                                g.push("<a href='#' id='"+row.id+"' onclick='editList(this)'>修改</a>")
                                g.push("<a href='#' id='"+row.id+"' onclick='deleteList(this)'> 删除 </a>")

                                return g.join(" ")
                            }
                        }
                    ],
                    [
                        { title: "大型应急车+卫星车", width: 100, colspan: 5, tooltip:true, align: "center" },
                        { title: "5G基站站号", field: "fiveBaseStation", width: 130, rowspan: 2, tooltip:true, align: "center",
                            formatter: function(value, row, index) {
                                return "<span class='col_b' type='A' pmInsId='"+row.pmInsId+"' onclick='toList(this)'>"+value+"</span>"
                            }
                        },
                        { title: "5G反开4G基站站号", field: "fivereversefour", width: 130, rowspan: 2, tooltip:true, align: "center" },
                        { title: "4G FDD 基站站号", field: "fourFDDBaseStation", width: 130, rowspan:2, tooltip:true, align: "center",
                            formatter: function(value, row, index) {
                                return "<span class='col_b' type='B' pmInsId='"+row.pmInsId+"' onclick='toList(this)'>"+value+"</span>"
                            }
                        },
                        { title: "4G TDD 基站站号", field: "fourTDDBaseStation", width: 130, rowspan:2, tooltip:true, align: "center",
                            formatter: function(value, row, index) {
                                return "<span class='col_b' type='C' pmInsId='"+row.pmInsId+"' onclick='toList(this)'>"+value+"</span>"
                            }
                        },
                    ],
                    [
                        { title: "应急车车牌号", field: "licensePlate", width: 100, tooltip:true, align: "center" },
                        { title: "司机", field: "theDriver", width: 100, tooltip:true, align: "center" },
                        { title: "司机手机号", field: "theDriverPhone", width: 100, tooltip:true, align: "center" },
                        { title: "负责开站人员", field: "openingStation", width: 100, tooltip:true, align: "center" },
                        { title: "开站人员手机号", field: "openingStationPhone", width: 100, tooltip:true, align: "center" }
                    ]
                ]
            }
        };

        $(function(){
            loadGrid(pageparam);
            // 加载应急设备分类下拉框数据
            loadCarConfigurationParentList();
            // 加载应急设备子类下拉框数据
            loadCarConfigurationList();

            $(".import").click(function() {
                top.dialogP("html/sysManagement/import.html?type=car", window.name, "模板导入", "importCB", false, "maximized", "maximized")
            })
            $(".downLoad").click(function () {
                $("#downloadForm").attr("action", web.rootdir + "action/garageConfiguration/downloadTemplate");
                $("#downloadForm .downloadSubmit").trigger("click");
            })
            $(".resetForm").click(function() {
                formreset("carTableQueryForm")
                $(".searchtable").trigger("click")
            })
        });

        // 动态加载应急设备分类数据
        function loadCarConfigurationParentList() {
            ajaxgeneral({
                url: "action/garageConfiguration/getCarConfiguRationParentList",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                success: function(response) {
                    try {
                        if (response && response.errcode === 0 && response.data) {
                            var options = [];


                            // 遍历返回的数据数组，构建下拉框选项
                            for (var i = 0; i < response.data.length; i++) {
                                var item = response.data[i];
                                if (item && item.trim() !== '') {
                                    options.push({value: item, name: item});
                                }
                            }

                            // 更新下拉框数据
                            $('.carConfiguRationParent').combobox('loadData', options);
                        } else {
                            console.error('获取应急设备分类数据失败：', response);
                            $.messager.alert('错误', '获取应急设备分类数据失败，请稍后重试', 'error');
                        }
                    } catch (e) {
                        console.error('解析应急设备分类数据时出错：', e);
                        $.messager.alert('错误', '解析数据时出错，请稍后重试', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('调用应急设备分类接口失败：', error);
                    $.messager.alert('错误', '网络请求失败，请检查网络连接后重试', 'error');
                }
            });
        }

        // 动态加载应急设备子类数据
        function loadCarConfigurationList() {
            ajaxgeneral({
                url: "action/garageConfiguration/getCarConfiguRationList",
                type: "POST",
                contentType: "application/json; charset=utf-8",
                success: function(response) {
                    try {
                        if (response && response.errcode === 0 && response.data) {
                            var options = [];
                            // 添加"全部"选项

                            // 遍历返回的数据数组，构建下拉框选项
                            for (var i = 0; i < response.data.length; i++) {
                                var item = response.data[i];
                                if (item && item.trim() !== '') {
                                    options.push({value: item, name: item});
                                }
                            }

                            // 更新下拉框数据
                            $('.carConfiguRation').combobox('loadData', options);
                        } else {
                            console.error('获取应急设备子类数据失败：', response);
                            $.messager.alert('错误', '获取应急设备子类数据失败，请稍后重试', 'error');
                        }
                    } catch (e) {
                        console.error('解析应急设备子类数据时出错：', e);
                        $.messager.alert('错误', '解析数据时出错，请稍后重试', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('调用应急设备子类接口失败：', error);
                    $.messager.alert('错误', '网络请求失败，请检查网络连接后重试', 'error');
                }
            });
        }
        function editList(param) {
            top.dialogP("html/sysManagement/emergencyCarAddForm.html?id="+$(param).attr("id"), window.name, "修改", "addCB", false, 1000, 400)
        }
        function addCB(data) {
            var param =data.data
            ajaxgeneral({
                url: "action/garageConfiguration/update",
                data: param,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#carTable").datagrid("reload")
                }
            })
        }
        function importCB(data) {
            ajaxgeneral({
                url: "action/garageConfiguration/saveGarageConfiguration",
                data: data.data,
                contentType: "application/json; charset=utf-8",
                success: function(data) {
                    $("#carTable").datagrid("reload")
                }
            })
        }
        function toList(param) {
            var title = $(param).attr("type")=="A"?"5G设备情况统计":"LTE FDD设备情况统计"
            top.dialogP("html/sysManagement/carList.html?pmInsId="+$(param).attr("pmInsId")+"&type="+$(param).attr("type"), window.name, title, "listCB", true, 1200, 600)
        }
        function deleteList(param) {
            $.messager.confirm("温馨提示！", "确认删除吗？", function(r) {
                if(r) {
                    ajaxgeneral({
                        url: "action/garageConfiguration/deleteById?id="+$(param).attr("id"),
                        success: function(data) {
                            $("#carTable").datagrid("reload")
                        }
                    })
                }
            })
        }
        function optState(param) {
            $.messager.confirm("温馨提示！", "请确认操作！", function(r) {
                if(r) {
                    ajaxgeneral({
                        url: "action/garageConfiguration/updateGarageConfiguration?id="+$(param).attr("id")+"&flag="+encodeURI($(param).text() == "停用" ? "停用" : "可调度"),
                        success: function(data) {
                            $("#carTable").datagrid("reload")
                        }
                    })
                }
            })
        }
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="carTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="110" align="right">地市：</td>
            <td width="170">
                <input name="cities" type="text" value="" />
            </td>
            <td width="110" align="right">设备厂家：</td>
            <td width="170">
                <input name="equipmentManufacturer" type="text" value="" />
            </td>
            <td width="110" align="right">调度状态：</td>
            <td width="170">
                <input name="schedulingCondition" class="easyui-combobox schedulingCondition"  style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       editable: false,
                       panelHeight: 'auto',
                       textField: 'name',
                       data: [{value:'可调度', name:'可调度'},{value:'不可调度', name:'不可调度'},{value:'停用', name:'停用'}] "/>
            </td>
            <td></td>
        </tr>
        <tr>
            <td width="110" align="right">应急设备分类：</td>
            <td width="170">
                <input name="carConfiguRationParent" class="easyui-combobox carConfiguRationParent"  style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       editable: false,
                       panelHeight: 'auto',
                       textField: 'name',
                       data: [] "/>
            </td>

            <td width="110" align="right">应急设备子类：</td>
            <td width="170">
                <input name="carConfiguRation" class="easyui-combobox carConfiguRation"  style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       editable: false,
                       panelHeight: 'auto',
                       textField: 'name',
                       data: [] "/>
            </td>

            <td width="110" align="right">应急车车牌号：</td>
            <td width="170">
                <input name="licensePlate" type="text" value="" />
            </td>
            <td width="110" align="right"></td>
            <td colspan="2" align="right">
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                    <a class="btn fl ml15 a_success resetForm"><font>重置</font></a>
                </div>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fr a_warning ml15 import"><font>模板导入</font></a>
                    <a class="btn fr a_success downLoad"><font>模板下载</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="carTable"><table id="carTable"></table></div>
<form id="downloadForm" class="hide" method="post">
    <input type="submit" class="downloadSubmit"/>
</form>
</body>
</html>
