package com.simbest.boot.yjtxc.apply.service;


import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import org.springframework.data.repository.query.Param;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:46
 * @describe 应急设备
 */
public interface IEmergencyEquipmentService extends ILogicService<EmergencyEquipment, String> {

    /**
     * 根据pmInsId回显应急设备
     * @param pmInsId
     * @return
     */
    List<EmergencyEquipment> findByPmInsId(String pmInsId);

    /**
     * 根据主单据ID删除信息
     * @param pmInsId
     */
    void deleteByPmInsId(String pmInsId);

    /**
     * 根据地市和车牌查是否被占用
     * @param licensePlate
     * @param cities
     * @return
     */
    Boolean findByGarageConfiguration(String licensePlate, String cities);

    /**
     * 台账查询
     * @param page
     * @param size
     * @param params
     * @return
     */
    JsonResponse findByParameterQuery(int page, int size, Map<String, Object> params);

    /**
     * 工单信息导出工单查询
     * @param request
     * @param response
     * @param emergencyEquipment
     */
    void exportOrder(HttpServletRequest request, HttpServletResponse response, EmergencyEquipment emergencyEquipment);

    /**
     * 根据主单据ID删除 旧数据
     * @param pmInstId 主单据ID
     */
    void deleteBypmIntId(String pmInstId);

    /**
     * 检车车辆是否可用
     * @param params
     * @return
     */
    JsonResponse findByAvailable(Map<String, Object> params);

    /**
     * 根据车牌、地市、开始时间、结束时间找车
     * @param licensePlate
     * @param belongCompanyName
     * @return
     */
    List<Map<String, Object>> findByCarTime(String licensePlate, String belongCompanyName,String startTime,String endTime );

    void updateArchiveTimeByPmInsId(String time,String pmInsId);

    /**
     * 通过eeid查询车牌号
     * @param pmInsId
     * @return
     */
    EmergencyEquipment getEByPmInsId(String pmInsId);

    /**
     * 查询四种类型车
     * @param pmInsId
     * @param bxz
     * @return
     */
    List<EmergencyEquipment> findByPmInsIdC(String pmInsId, String bxz);

    EmergencyEquipment findddd(String phoneNmu ,String pmInsId);


    List<EmergencyEquipment> finEmfindList(String phoneNmu ,String pmInsId);

    List<EmergencyEquipment> findByPmInsIdAndD(String pmInsId ,String  sbr);

    List<EmergencyEquipment> findByAll(String pmInsId);

    List<EmergencyEquipment> findByPmInsIdAll(String pmInsId);
}
