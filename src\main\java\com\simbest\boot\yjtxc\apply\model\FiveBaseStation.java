package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import com.simbest.boot.base.model.SystemModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 14:20
 * @describe 5G基站站号
 */

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_five_base_station")
@ApiModel(value = "5G基站站号")
public class FiveBaseStation extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "fbs") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID")  //应急车库配置
    protected String fiveBaseId;


    @Column(length = 100)
    @ApiModelProperty(value = "5G设备厂家")
    protected String equipmentManufacturer;

    @Column(length = 100)
    @ApiModelProperty(value = "BBU型号")
    protected String bbuModel;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板是否支持反开4G")
    protected String  basebandSupports;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板型号1")
    protected String basebandBoardModelOne;

    @Column(length = 40)
    @ApiModelProperty(value = "基带板型号2")
    protected String basebandBoardModelTwo;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板数量1")
    protected String basebandBoardNumberOne;

    @Column(length = 100)
    @ApiModelProperty(value = "基带板数量2")
    protected String basebandBoardNumberTwo;

    @Column(length = 100)
    @ApiModelProperty(value = "AAU型号")
    protected String aauModel;

    @Column(length = 100)
    @ApiModelProperty(value = "AAU数量")
    protected String aauNumber;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;


}
