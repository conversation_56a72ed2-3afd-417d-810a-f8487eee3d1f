package com.simbest.boot.yjtxc.apply.dto;

import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/10 11:35
 * @describe
 */
@Data
public class ParameterQuery implements Serializable {


    @ExcelVOAttribute(name = "应急车配置类型", column = "A")
    private String carConfiguRation;

    @ExcelVOAttribute(name = "车牌号", column = "B")
    private String licensePlate;

    @ExcelVOAttribute(name = "车辆归属地", column = "C")
    private String placeOwnership;

    @ExcelVOAttribute(name = "设备厂家", column = "D")
    private String equipmentManufacturer;

    @ExcelVOAttribute(name = "出车次数", column = "E")
    protected String carNumber;

    @ExcelVOAttribute(name = "出车时长", column = "F")
    protected String carTime;

    @ExcelVOAttribute(name = "出车地市", column = "G")
    protected String carCities;


}
