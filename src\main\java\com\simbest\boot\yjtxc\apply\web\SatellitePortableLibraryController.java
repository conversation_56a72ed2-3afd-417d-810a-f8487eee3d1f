package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.SatellitePortableLibrary;
import com.simbest.boot.yjtxc.apply.service.ISatellitePortableLibraryService;
import com.simbest.boot.yjtxc.util.FileTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:13
 * @describe
 */
@Api(description = "卫星便携站配置库")
@Slf4j
@RestController
@RequestMapping(value = "/action/satellitePortableLibrary")
public class SatellitePortableLibraryController  extends LogicController<SatellitePortableLibrary, String> {

    private ISatellitePortableLibraryService service;
    public SatellitePortableLibraryController(ISatellitePortableLibraryService service) {
        super(service);
        this.service=service;
    }



    /**
     * 导入卫星便携站配置库信息
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导入卫星便携站配置库信息", notes = "导入卫星便携站配置库信息")
    @PostMapping({"/importPerson", "/importPerson/sso"})
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return service.importPerson(request, response);

    }


    /**
     * 条件查询卫星便携站配置库
     * @param page
     * @param size
     * @param source
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @ApiOperation(value = "条件查询卫星便携站配置库", notes = "条件查询卫星便携站配置库")
    @PostMapping({"/findByAllSatellitePortableLibrary", "/findByAllSatellitePortableLibrary/sso"})
    public JsonResponse findByAllSatellitePortableLibrary(@RequestParam Integer page,
                                                          @RequestParam Integer size,
                                                          @RequestParam(required = false) String source,
                                                          @RequestParam(required = false) String currentUserCode,
                                                          @RequestBody(required = false) Map<String, Object> paramMap) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, "desc", "createdTime");
        return service.findByAllUavAerialConfigurationStation(source, pageable, currentUserCode, paramMap);
    }

    /**
     * 下载卫星便携战配置库信息模板
     *
     * @param response
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = {"/downloadTemplate", "/downloadTemplate/sso"}, method = RequestMethod.POST)
    public Map<String, Object> downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/卫星便携站配置库.xls");
        FileTool.downloadIn(in, "卫星便携站配置库.xls", response);
        return null;
    }


    /**
     * 批量导入卫星便携战配置库
     * @param satellitePortableLibraryList
     * @return
     */
    @ApiOperation(value = "保存卫星便携战配置库", notes = "保存卫星便携战配置库")
    @PostMapping({"/saveSatellitePortableLibrary", "/saveSatellitePortableLibrary/sso"})
    public JsonResponse saveSatellitePortableLibrary(@RequestBody List<SatellitePortableLibrary> satellitePortableLibraryList) {
        return service.saveSatellitePortableLibrary(satellitePortableLibraryList);
    }

    @ApiOperation(value = "修改卫星便携战配置库", notes = "修改卫星便携战配置库")
    @PostMapping({"/updateSatellitePortableLibrary", "/updateSatellitePortableLibrary/sso"})
    public JsonResponse updateSatellitePortableLibrary(@RequestBody SatellitePortableLibrary satellitePortableLibraryList) {
        return service.updateSatellitePortableLibrary(satellitePortableLibraryList);
    }

    @ApiOperation(value = "删除卫星便携战配置库", notes = "删除卫星便携战配置库")
    @PostMapping({"/deleteBySatellitePortableLibrary", "/deleteBySatellitePortableLibrary/sso"})
    public JsonResponse deleteBySatellitePortableLibrary(@RequestParam String id ) {
        return service.deleteBySatellitePortableLibrary(id);
    }

}
