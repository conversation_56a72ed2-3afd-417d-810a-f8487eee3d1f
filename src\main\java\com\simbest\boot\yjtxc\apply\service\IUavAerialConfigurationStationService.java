package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.UavAerialConfigurationStation;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:19
 * @describe 无人机高空配置库
 */
public interface IUavAerialConfigurationStationService extends ILogicService<UavAerialConfigurationStation, String> {

    /**
     * 导入无人机高空配置库
     * @param request
     * @param response
     * @return
     */
    JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response);

    /**
     * 条件查询流程固化涉及人员配置
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    JsonResponse findByAllUavAerialConfigurationStation(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap);

    /**
     * 无人机高空配置库信息
     * @param uavAerialConfigurationStation
     * @return
     */
    JsonResponse saveUavAerialConfigurationStation(List<UavAerialConfigurationStation> uavAerialConfigurationStation);

    /**
     * 修改无人机高空配置库信息
     * @param uavAerialConfigurationStation
     * @return
     */
    JsonResponse updateUavAerialConfigurationStation(UavAerialConfigurationStation uavAerialConfigurationStation);

    /**
     * 删除无人机高空配置库信息
     * @param id
     * @return
     */
    JsonResponse deleteByUavAerialConfigurationStation(String id);

    /**
     *
     * @return
     */
    List<Map<String,Object>> findByDrone();

    /**
     * 无人机高空站按照地市条件查询
     * @return
     */
    List<Map<String, Object>> findByDroneAndCities(String cities);
}
