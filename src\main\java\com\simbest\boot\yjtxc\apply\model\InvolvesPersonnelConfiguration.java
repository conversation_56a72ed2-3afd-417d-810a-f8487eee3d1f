package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 15:48
 * @describe
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_personnel_config")
@ApiModel(value = "流程固化涉及人员配置")
public class InvolvesPersonnelConfiguration extends LogicModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "pc") //主键前缀，此为可选项注解
    private String id;


    /**
     * ------------------------------------------------------应急车申请人A角-------------------------------------------
     */
    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    @ExcelVOAttribute(name = "地市", column = "A")
    private String appCities;

    @Column(length = 100)
    @ApiModelProperty(value = "地市Code")
    private String appCitiesCode;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人OA账号-A角")
    @ExcelVOAttribute(name = "姓名(A角)", column = "B")
    private String appUsernameA;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人姓名-A角")
    @ExcelVOAttribute(name = "OA账号", column = "C")
    private String appTruenameA;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人手机号-A角")
    @ExcelVOAttribute(name = "电话", column = "D")
    private String appphoneNumA;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "申请人应急车申请人邮箱-A角")
    @ExcelVOAttribute(name = "邮件", column = "E")
    private String appLinkEmailA;

    /**
     * ------------------------------------------------------应急车申请人B角-------------------------------------------
     */


    @Column(length = 100)
    @ApiModelProperty(value = "申请人姓名-B角")
    @ExcelVOAttribute(name = "申请人姓名", column = "F")
    private String appTruenameB;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人OA账号-B角")
    @ExcelVOAttribute(name = "申请人OA账号", column = "G")
    private String appUsernameB;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人手机号-B角")
    @ExcelVOAttribute(name = "申请人手机号", column = "H")
    private String appPhoneNumB;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "申请人应急车申请人邮箱-B角")
    @ExcelVOAttribute(name = "申请人应急车申请人邮箱", column = "I")
    private String appLinkEmailB;

    /**
     * ------------------------------------------------------应急车申请人网络部经理------------------------------------
     */

    @Column(length = 100)
    @ApiModelProperty(value = "申请人网络部经理姓名-B角")
    @ExcelVOAttribute(name = "申请人网络部经理姓名", column = "J")
    private String appTruenameVehicle;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人网络部经理OA账号-B角")
    @ExcelVOAttribute(name = "申请人网络部经理OA账号", column = "K")
    private String apptUsernameVehicle;

    @Column(length = 100)
    @ApiModelProperty(value = "申请人网络部经理手机号-B角")
    @ExcelVOAttribute(name = "申请人网络部经理手机号", column = "L")
    private String appPhoneVehicle;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "申请人网络部经理应急车申请人邮箱-B角")
    @ExcelVOAttribute(name = "申请人网络部经理应急车申请人邮箱", column = "M")
    private String appLinkEmailVehicle;

    /**
     * ------------------------------------------------------应急车负责人(开站人)A角------------------------------------
     */

    @Column(length = 100)
    @ApiModelProperty(value = "负责人姓名-A角")
    @ExcelVOAttribute(name = "负责人姓名", column = "N")
    private String headTruenameA;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人OA账号-A角")
    @ExcelVOAttribute(name = "负责人OA账号", column = "O")
    private String headUsernameA;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人手机号-A角")
    @ExcelVOAttribute(name = "负责人手机号", column = "P")
    private String headPhoneNumA;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "应急车申请人邮箱-A角")
    @ExcelVOAttribute(name = "应急车申请人邮箱", column = "Q")
    private String headLinkEmailA;

    /**
     * ------------------------------------------------------应急车负责人(开站人)B角------------------------------------
     */

    @Column(length = 100)
    @ApiModelProperty(value = "负责人姓名-B角")
    @ExcelVOAttribute(name = "负责人姓名-B角", column = "R")
    private String headTruenameB;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人OA账号-B角")
    @ExcelVOAttribute(name = "负责人OA账号-B角", column = "S")
    private String headUsernameB;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人手机号-B角")
    @ExcelVOAttribute(name = "负责人手机号-B角", column = "T")
    private String headPhoneNumB;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "应急车申请人邮箱-B角")
    @ExcelVOAttribute(name = "应急车申请人邮箱-B角", column = "U")
    private String headLinkEmailB;


    /**
     * ------------------------------------------------------应急车负责人网络部经理-- 暂时无用----------------------------------
     */

    @Column(length = 100)
    @ApiModelProperty(value = "负责人网络部经理姓名-B角")
    private String headTruenameVehicle;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人网络部经理OA账号-B角")
    private String headUsernameVehicle;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人网络部经理手机号-B角")
    private String headPhoneNumVehicle;

    @Column(nullable = false, length = 100)
    @ApiModelProperty(value = "应急车申请人邮箱-B角")
    private String headLinkEmailVehicle;


    /**
     * ------------------------------------------------------通用------------------------------------
     */

    @Column(length = 100)
    @ApiModelProperty(value = "应急配置类型")
    @ExcelVOAttribute(name = "应急配置类型", column = "V")
    private String emerConfiType;

    @Column(length = 100)
    @ApiModelProperty(value = "对应车牌号")
    @ExcelVOAttribute(name = "对应车牌号", column = "W")
    private String correspondsNumber;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    @ExcelVOAttribute(name = "设备厂家", column = "X")
    private String equipmentManu;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;

}
