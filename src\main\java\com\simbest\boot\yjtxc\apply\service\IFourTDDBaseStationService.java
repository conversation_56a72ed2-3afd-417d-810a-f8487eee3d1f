package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import com.simbest.boot.yjtxc.apply.model.FourTDDBaseStation;

import java.util.List;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:37
 * @describe 4GTDD基站站号
 */
public interface IFourTDDBaseStationService extends ILogicService<FourTDDBaseStation, String> {


    void deleteByFourTDDBaseStationServicePmInsId(String pmInsId);

    /**
     * 维护4GTDD基站站号
     * @param fourFDDBaseStation
     * @return
     */
    JsonResponse updateFourTDDBaseStation(FourTDDBaseStation fourFDDBaseStation);

    /**
     * 查询4GTDD基站站号
     * @param pmInsId
     * @return
     */
    JsonResponse findByPmInsIdFourTDDBaseStation(String pmInsId);

    /**
     * 维护4GTDD基站站号
     * @param fourTDDBaseStationList
     * @return
     */
    JsonResponse saveFourTDDBaseStation(List<FourTDDBaseStation> fourTDDBaseStationList,String pmInsId);
}
