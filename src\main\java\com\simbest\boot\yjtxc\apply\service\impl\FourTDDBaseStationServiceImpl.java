package com.simbest.boot.yjtxc.apply.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import com.simbest.boot.yjtxc.apply.model.FourTDDBaseStation;
import com.simbest.boot.yjtxc.apply.repository.FourTDDBaseStationRepository;
import com.simbest.boot.yjtxc.apply.service.IFourTDDBaseStationService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:37
 * @describe 4GTDD基站站号
 */
@Slf4j
@Service
public class FourTDDBaseStationServiceImpl extends LogicService<FourTDDBaseStation, String> implements IFourTDDBaseStationService {

    private FourTDDBaseStationRepository repository;

    public FourTDDBaseStationServiceImpl(FourTDDBaseStationRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    public void deleteByFourTDDBaseStationServicePmInsId(String pmInsId) {
        repository.deleteByPmInsId(pmInsId);
    }


    @Autowired
    private ISysOperateLogService sysOperateLogService;


    final String param1 = "action/fourTDDBaseStation/";

    /**
     * 维护4GTDD基站站号
     *
     * @param fourFDDBaseStation
     * @return
     */
    @Override
    public JsonResponse updateFourTDDBaseStation(FourTDDBaseStation fourFDDBaseStation) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("FourTDDBaseStation", fourFDDBaseStation);
        optLogParam.put("operateInterface", param1 + "updateFourFDDBaseStation");
        try {
            deleteById(fourFDDBaseStation.getId());
            insert(fourFDDBaseStation);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询4GTDD基站站号
     *
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse findByPmInsIdFourTDDBaseStation(String pmInsId) {
        return JsonResponse.success(repository.findByPmInsIdFourTDDBaseStation(pmInsId));
    }

    /**
     * 维护4GTDD基站站号
     *
     * @param fourTDDBaseStationList
     * @return
     */
    @Override
    public JsonResponse saveFourTDDBaseStation(List<FourTDDBaseStation> fourTDDBaseStationList,String pmInsId) {
        List<FourTDDBaseStation> list = CollectionUtil.newArrayList();
        Iterator<FourTDDBaseStation> iterator = fourTDDBaseStationList.iterator();
        while (iterator.hasNext()) {
            FourTDDBaseStation fourTDDBaseStation = iterator.next();
            String fourTddId = fourTDDBaseStation.getFourTddId();
            if (StrUtil.isNotEmpty(fourTddId)) {
                repository.deleteByPmInsId(fourTddId);
            }
            fourTDDBaseStation.setId("");
            list.add(fourTDDBaseStation);
        }
        if (ObjectUtil.isNotEmpty(list)) {
            saveAll(list);
        }else {
            repository.deleteByPmInsId(pmInsId);
        }
        return JsonResponse.success("数据保存成功");
    }
}
