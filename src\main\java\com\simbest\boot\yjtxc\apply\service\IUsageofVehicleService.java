package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.model.UsageofVehicle;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 车辆使用情况
 */
public interface IUsageofVehicleService extends ILogicService<UsageofVehicle, String> {


    /**
     * 车辆使用情况查询
     * @param page
     * @param size
     * @param params
     * @return
     */
    JsonResponse findByParameterQuery(int page, int size, Map<String, Object> params);

    /**
     * 导出
     * @param request
     * @param response
     * @param emergencyEquipment
     */
    void exportOrder(HttpServletRequest request, HttpServletResponse response, EmergencyEquipment emergencyEquipment);
}
