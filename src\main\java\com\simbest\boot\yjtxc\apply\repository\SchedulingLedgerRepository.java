package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:51
 * @describe 应急车调度台账
 */
public interface SchedulingLedgerRepository extends LogicRepository<SchedulingLedger, String> {

    @Query(value = " select * from US_SCHEDULING_LEDGER t where t.license_number=:licensePlate and t.enabled='1'", nativeQuery = true)
    SchedulingLedger findByLicensePlate(@Param("licensePlate") String licensePlate);

    /**
     * 根据无人机或者卫星和地市查找信息
     *
     * @param licensePlate
     * @param cities
     * @return
     */
    @Query(value = " select * from US_SCHEDULING_LEDGER t where t.license_number=:licensePlate and t.enabled='1' and t.place_ownership=:cities ", nativeQuery = true)
    SchedulingLedger findByLicensePlateAndCities(@Param("licensePlate") String licensePlate, @Param("cities") String cities);
}
