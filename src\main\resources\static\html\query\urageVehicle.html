
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>车辆使用情况</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .w100 a {
            min-width: 66px;
        }
    </style>

    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#standingBookTable",
                // "querycmd":"action/schedulingLedger/findBySchedulingLedger?source=PC",
                "querycmd":"action/usageofVehicle/findByParameterQuery?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "frozenColumns":[],
                "columns":[[
                    { title: "应急车配置类型", field: "carConfiguRation", width: 200, rowspan: 1},
                    { title: "车牌号", field: "licensePlate", width: 200,tooltip:true},
                    { title: "车辆归属地", field: "cities", width: 100 },
                    { title: "设备厂家", field: "equipmentManufacturer", width: 150 },
                    { title: "活动名称", field: "nameEvent", width: 100 },
                    { title: "保障开始时间", field: "guaranteeStartTime", width: 100,
                        formatter: function(value, row, index) {
                            if(value){
                                return value;
                            }else{
                                return "";
                            }
                        }},
                    { title: "保障结束时间", field: "guaranteeEndTime", width: 100,
                        formatter: function(value, row, index) {
                            if(value){
                                return value;
                            }else{
                                return "";
                            }
                        }},
                    { title: "调度状态", field: "schedule", width: 100},
                    { title: "出车地市", field: "carCities", width: 100},
                ]]
            }
        };
        $(function(){
            loadGrid(pageparam);
            $(".export").click(function () {
                $("#standingBookTableQueryForm").attr({"action":web.rootdir + "action/usageofVehicle/exportOrder","method":"post"});
                $("#standingBookTableQueryForm").submit()
            })
        });
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="standingBookTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6">
        <tr>
            <td width="120" align="right">应急车配置类型：</td>
            <td width="160">
                <input class="easyui-combobox vehicleConfiguration" name="carConfiguRation" style="width:100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       editable: false,
                       ischooseall: true,
                       panelHeight: 'auto',
                       textField: 'name',
                       data: [{value: '卫星车', name: '卫星车'},{value: '大型应急车', name: '大型应急车'},{value: '无人机高空站', name: '无人机高空站'},{value: '卫星便携站', name: '卫星便携站'}] "/>
            </td>
            <td width="80" align="right">车牌号：</td>
            <td colspan="2">
                <input name="licensePlate" type="text" value="" class="easyui-validatebox" />
            </td>
            <td width="105" align="right">车牌归属地市：</td>
            <td width="160">
                <input class="placeOwnership easyui-combobox" name="cities" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_type'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl ml15 searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
        <tr>
            <td width="120" align="right">设备厂家：</td>
            <td width="160">
                <input class="equipmentManufacturer easyui-combobox" name="equipmentManufacturer" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_manufacturer'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td width="120" align="right">查询日期：</td>
            <td width="120">
                <input name="chooseTime" type="text" class="easyui-datebox"
                       data-options="panelHeight:'auto',editable:false"
                       style="width:100%;height:32px;"/>
            </td>
            <td></td>
            <td width="120" align="right">调度状态：</td>
            <td width="160">
                <input class="easyui-combobox vehicleConfiguration" name="schedule" style="width:100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       editable: false,
                       ischooseall: true,
                       panelHeight: 'auto',
                       textField: 'name',
                       data: [{value: '可调度', name: '可调度'},{value: '调度中', name: '调度中'}] "/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl ml15 a_warning export"><font>导出</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="standingBookTable"><table id="standingBookTable"></table></div>
</body>
</html>
