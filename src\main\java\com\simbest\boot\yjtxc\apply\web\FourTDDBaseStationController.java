package com.simbest.boot.yjtxc.apply.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.FourFDDBaseStation;
import com.simbest.boot.yjtxc.apply.model.FourTDDBaseStation;
import com.simbest.boot.yjtxc.apply.service.IFourTDDBaseStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:33
 * @describe 4GTDD基站站号||LTE TDD 设备情况统计
 */
@Api(description = "4GTDD基站站号")
@Slf4j
@RestController
@RequestMapping(value = "/action/fourTDDBaseStation")
public class FourTDDBaseStationController extends LogicController<FourTDDBaseStation, String> {

    private IFourTDDBaseStationService service;

    public FourTDDBaseStationController(IFourTDDBaseStationService service) {
        super(service);
        this.service=service;
    }


    /**
     * 4GTDD基站站号
     * @return
     */
    @ApiOperation(value = "维护4GTDD基站站号", notes = "维护4GTDD基站站号")
    @PostMapping({"/updateFourTDDBaseStation", "/updateFourTDDBaseStation/sso"})
    public JsonResponse updateFourTDDBaseStation(@RequestBody FourTDDBaseStation fourTDDBaseStation){
        return service.updateFourTDDBaseStation(fourTDDBaseStation);
    }


    /**
     * 查询4GTDD基站站号
     * @param pmInsId
     * @return
     */
    @ApiOperation(value = "查询4GTDD基站站号", notes = "查询4GTDD基站站号")
    @PostMapping({"/findByPmInsIdFourTDDBaseStation", "/findByPmInsIdFourTDDBaseStation/sso"})
    public JsonResponse findByPmInsIdFourTDDBaseStation(@RequestParam String pmInsId){
        return service.findByPmInsIdFourTDDBaseStation(pmInsId);
    }

    /**
     * 维护4GTDD基站站号
     * @param fourTDDBaseStationList
     * @return
     */
    @ApiOperation(value = "维护4GTDD基站站号", notes = "维护4GTDD基站站号")
    @PostMapping({"/saveFourTDDBaseStation", "/saveFourTDDBaseStation/sso"})
    public JsonResponse saveFourTDDBaseStation(@RequestBody List<FourTDDBaseStation> fourTDDBaseStationList,@RequestParam String  pmInsId){
        return service.saveFourTDDBaseStation(fourTDDBaseStationList,pmInsId);
    }

}
