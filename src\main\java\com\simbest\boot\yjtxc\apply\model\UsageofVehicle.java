package com.simbest.boot.yjtxc.apply.model;


import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 *车辆使用情况
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_agof_vehicle")
@ApiModel(value = "车辆使用情况")
public class UsageofVehicle extends LogicModel {

    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ag") //主键前缀，此为可选项注解
    private String id;


    @Column(length = 100)
    @ApiModelProperty(value = "应急车配置类型")
    protected String vehicleConfiguration;

    @Column(length = 100)
    @ApiModelProperty(value = "车牌号")
    protected String licenseNumber;

    @Column(length = 100)
    @ApiModelProperty(value = "车辆归属地")
    protected String placeOwnership;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    protected String equipmentManufacturer;

    @Column(length = 100)
    @ApiModelProperty(value = "活动名称")
    protected String nameEvent;

    @Column(length = 100)
    @ApiModelProperty(value = "保障开始时间")
    protected String guaranteeStartTime;

    @Column(length = 100)
    @ApiModelProperty(value = "保障结束时间")
    protected String guaranteeEndTime;


    @Column(length = 40)
    @ApiModelProperty(value = "调度状态")
 //   @ExcelVOAttribute(name = "调度状态", column = "N")
    private String schedulingCondition;


    @Column(length = 100)
    @ApiModelProperty(value = "出车地市")
    protected String carCities;



}
