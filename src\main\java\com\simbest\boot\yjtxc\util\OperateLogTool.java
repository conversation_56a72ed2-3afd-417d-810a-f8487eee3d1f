package com.simbest.boot.yjtxc.util;

import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.security.IUser;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.util.security.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用途：操作日志相关操作
 * 作者：zsf
 * 时间：2018-10-11
 */
@Component
public class OperateLogTool {

    public static void  saveOperateLog(){}

    @Autowired
    private LoginUtils loginUtils;

    /**
     * 给操作日志对象赋值创建人
     * @param operateLog
     * @param param
     */
    public static void setParam(SysOperateLog operateLog, String param){
        operateLog.setCreator( param );
        operateLog.setModifier( param );
    }

    /**
     * 给操作日志对象赋值错误信息
     * @param operateLog
     * @param param
     */
    public static void setParams(SysOperateLog operateLog, String param){
        operateLog.setCreator( param );
        operateLog.setModifier( param );
        operateLog.setErrorMsg( param );
    }

    /**
     *  记录操作日志
     * @param source 来源
     * @param userCode 用户账户
     * @param param1 参数1
     * @param param2 接口名
     * @param operateLog 操作对象日志对象
     * @return
     */
    public  JsonResponse operationSource( String source, String userCode, String param1,String param2,SysOperateLog operateLog){
        JsonResponse jsonResponse = null;
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            if (  StringUtils.isNotEmpty( source ) && "MOBILE".equals( source ) ){
                operateLog.setOperateFlag( "MOBILE" );
                operateLog.setOperateInterface( param1+"/api"+param2 );
            }else {
                operateLog.setOperateFlag( "PC" );
                operateLog.setOperateInterface( param1 + param2 );
            }
            /**获取用户session**/
            IUser user = SecurityUtils.getCurrentUser();
            if ( user != null ){
                String userName = user.getUsername();
                setParam( operateLog,userName );
            }else {
                if ( StringUtils.isNotEmpty( userCode ) && "MOBILE".equals( source ) ){//不为空时做登录操作
                    loginUtils.manualLogin( userCode , Constants.APP_CODE);
                    setParam( operateLog, SecurityUtils.getCurrentUserName() );
                }else {
                    setParams( operateLog,"手机端账号为空");
                    jsonResponse = JsonResponse.fail(null,"OA账户不能为空！");
                }
            }
        }catch ( Exception e ){
            Exceptions.printException( e );
        }
        return jsonResponse;
    }

    /**
     * 判断手机端来源
     *
     * @param source     来源
     * @param userCode   用户账户
     * @return
     */
    public JsonResponse operationSource(String source, String userCode) {
        JsonResponse jsonResponse = null;
        /**判断是否是从手机端还是PC端记录操作日志**/
        try {
            if ( Constants.MOBILE.equals(source)) {
                if (StringUtils.isNotEmpty(userCode)) {
                    loginUtils.manualLogin(userCode, Constants.APP_CODE);
                } else {
                    jsonResponse = JsonResponse.fail("OA账户不能为空!");
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return jsonResponse;
    }



    /**
     * 根据用户名，自动登录
     * @param source 来源
     * @param userCode 用户名（加密）
     */
    public void userLogin(String source, String userCode){
        /**获取用户session**/
        IUser user = SecurityUtils.getCurrentUser();
        if ( user == null ){
            if ( StringUtils.isNotEmpty( userCode ) && "MOBILE".equals( source ) ){//不为空时做登录操作
                loginUtils.manualLogin( userCode ,Constants.APP_CODE);
            }
        }
    }
}
