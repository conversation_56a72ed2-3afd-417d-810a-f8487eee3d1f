﻿//声明字典命名空间
if(!window.web){ web={}; }
if(!window.dictionary){ dictionary={}; }
if(!window.dictionarys){ dictionarys={}; }

web.appCode="yjtxc";
//ABC代表的是流程类型,后边的是对应的需要跳转的页面路径
web.appHtml={
    "A": "html/apply/dailyLocal.html",
    "B": "html/apply/dailyAcross.html",
    "C": "html/apply/wartime.html"
};
//ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部
web.appName ={
    "A": "地市内调度",
    "B": "跨地市调度",
    "C": "战时调度"
};
