package com.simbest.boot.yjtxc.apply.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.wenhao.jpa.Specifications;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.yjtxc.apply.dto.ExportOrder;
import com.simbest.boot.yjtxc.apply.dto.ParameterQuery;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.SchedulingLedger;
import com.simbest.boot.yjtxc.apply.repository.EmergencyEquipmentRespository;
import com.simbest.boot.yjtxc.apply.service.IEmergencyEquipmentService;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.yjtxc.util.FileTool;
import com.simbest.boot.yjtxc.util.PageTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:47
 * @describe 应急设备
 */
@Slf4j
@Service
public class EmergencyEquipmentServiceImpl extends LogicService<EmergencyEquipment, String> implements IEmergencyEquipmentService {

    private EmergencyEquipmentRespository emergencyEquipmentRespository;

    public EmergencyEquipmentServiceImpl(EmergencyEquipmentRespository emergencyEquipmentRespository) {
        super(emergencyEquipmentRespository);
        this.emergencyEquipmentRespository = emergencyEquipmentRespository;
    }

    final String param1 = "/action/emergencyEquipment/";

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;

    /**
     * 根据pmInsId回显应急设备
     *
     * @param pmInsId
     * @return
     */
    @Override
    public List<EmergencyEquipment> findByPmInsId(String pmInsId) {
        return emergencyEquipmentRespository.findByPmInsId(pmInsId);
    }

    /**
     * 根据主单据ID删除信息
     *
     * @param pmInsId
     */
    @Override
    public void deleteByPmInsId(String pmInsId) {
        emergencyEquipmentRespository.deleteByPmInsId(pmInsId);
    }

    /**
     * 根据地市和车牌查是否被占用
     *
     * @param licensePlate
     * @param cities
     * @return
     */
    @Override
    public Boolean findByGarageConfiguration(String licensePlate, String cities) {
        String today = DateUtil.today();
        EmergencyEquipment emergencyEquipment = emergencyEquipmentRespository.findByGarageConfiguration(licensePlate, cities, today);
        if (ObjectUtil.isNotEmpty(emergencyEquipment)) {
            return false;
        }
        return true;
    }


    /**
     * 台账查询
     *
     * @param page
     * @param size
     * @param paramMap
     * @return
     */
    @Override
    public JsonResponse findByParameterQuery(int page, int size, Map<String, Object> paramMap) {

        Page<SchedulingLedger> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");

        try {
            if (page == 0) {
                page = StrUtil.isNotEmpty(MapUtil.getStr(paramMap, "page")) ? Integer.parseInt(MapUtil.getStr(paramMap, "page")) : 1;
            }
            if (size == 0) {
                size = StrUtil.isNotEmpty(MapUtil.getStr(paramMap, "size")) ? Integer.parseInt(MapUtil.getStr(paramMap, "size")) : 99;
            }
            //获取地市
            String placeOwnership = cn.hutool.core.map.MapUtil.getStr(paramMap, "placeOwnership");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //应急车配置类型
            String vehicleConfiguration = MapUtil.getStr(paramMap, "vehicleConfiguration");
            //车牌号
            String licenseNumber = MapUtil.getStr(paramMap, "licenseNumber");
            //开始使用时间
            String startTime = MapUtil.getStr(paramMap, "startTime");
            //结束使用时间
            String endTime = MapUtil.getStr(paramMap, "endTime");
            Map<String, Object> params = CollectionUtil.newHashMap();
            StringBuilder basqSql = new StringBuilder("select t.cities as placeOwnership, " +
                    "       t.equipment_manufacturer as equipmentManufacturer, " +
                    "       t.car_configu_ration as vehicleConfiguration, " +
                    "       t.license_plate as licenseNumber, " +
                    "       t.car_cities as carCities, " +
                    "       count(t.license_plate) carNumber, " +
                    "       sum(t.car_time) carTime " +
                    "  from US_EMERGENCY_EQUIPMENT t " +
                    " where t.enabled = '1' ");

            //判断车辆归属地市是否为空
            if (StrUtil.isNotEmpty(placeOwnership)) {
                basqSql.append(" and t.cities like:placeOwnership");
                params.put("placeOwnership", "%" + placeOwnership + "%");
            }
            //判断设备厂家是否为空
            if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                basqSql.append(" and t.equipment_manufacturer like:equipmentManufacturer");
                params.put("equipmentManufacturer", "%" + equipmentManufacturer + "%");
            }
            //判断应急车配置类型是否为空
            if (StrUtil.isNotEmpty(vehicleConfiguration)) {
                basqSql.append(" and t.car_configu_ration=:vehicleConfiguration");
                params.put("vehicleConfiguration", vehicleConfiguration);
            }

            //判断车牌号是否为空
            if (StrUtil.isNotEmpty(licenseNumber)) {
                basqSql.append(" and t.license_plate like" + "'%" + licenseNumber + "%'");
                // params.put("licenseNumber", "%" + licenseNumber + "%");
            }

            //判断开始时间是否为空
            if (StrUtil.isNotEmpty(startTime)) {
                basqSql.append("  and t.start_time<=:startTime");
                params.put("startTime", startTime);
            }
            //判断结束时间是否为空
            if (StrUtil.isNotEmpty(endTime)) {
                basqSql.append("  and t.end_time>=:endTime");
                params.put("endTime", endTime);
            }
            basqSql.append("  group by t.cities, " +
                    "          t.equipment_manufacturer, " +
                    "          t.car_configu_ration, " +
                    "          t.license_plate, " +
                    "          t.car_cities");
            List<Map<String, Object>> resultList = customDynamicWhere.queryNamedParameterForList(basqSql.toString(), paramMap);
            List<Map<String, Object>> list = com.simbest.boot.util.MapUtil.formatHumpNameForList(resultList);
            if (list != null && list.size() > 0) {
                int row = list.size();
                Pageable pageable1 = getPageable(page, size, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(list, page, size);
                return JsonResponse.success(new PageImpl(listPart, pageable1, row));
            }
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);

    }


    public List<Map<String, Object>> findByParameterQuery(Map<String, Object> paramMap) {

        List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");

        try {

            //获取地市
            String placeOwnership = cn.hutool.core.map.MapUtil.getStr(paramMap, "placeOwnership");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //应急车配置类型
            String vehicleConfiguration = MapUtil.getStr(paramMap, "vehicleConfiguration");
            //车牌号
            String licenseNumber = MapUtil.getStr(paramMap, "licenseNumber");
            //开始使用时间
            String startTime = MapUtil.getStr(paramMap, "startTime");
            //结束使用时间
            String endTime = MapUtil.getStr(paramMap, "endTime");
            Map<String, Object> params = CollectionUtil.newHashMap();
            StringBuilder basqSql = new StringBuilder("select t.cities as placeOwnership, " +
                    "       t.equipment_manufacturer as equipmentManufacturer, " +
                    "       t.car_configu_ration as vehicleConfiguration, " +
                    "       t.license_plate as licenseNumber, " +
                    "       t.car_cities as carCities, " +
                    "       count(t.license_plate) carNumber, " +
                    "        TRUNC(sum(t.car_time)/60)||'小时'||MOD(sum(t.car_time), 60)||'分钟' carTime  " +
                    "  from US_EMERGENCY_EQUIPMENT t " +
                    " where t.enabled = '1' ");

            //判断车辆归属地市是否为空
            if (StrUtil.isNotEmpty(placeOwnership)) {
                basqSql.append(" and t.cities like:placeOwnership");
                params.put("placeOwnership", "%" + placeOwnership + "%");
            }
            //判断设备厂家是否为空
            if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                basqSql.append(" and t.equipment_manufacturer like:equipmentManufacturer");
                params.put("equipmentManufacturer", "%" + equipmentManufacturer + "%");
            }
            //判断应急车配置类型是否为空
            if (StrUtil.isNotEmpty(vehicleConfiguration)) {
                basqSql.append(" and t.car_configu_ration=:vehicleConfiguration");
                params.put("vehicleConfiguration", vehicleConfiguration);
            }

            //判断车牌号是否为空
            if (StrUtil.isNotEmpty(licenseNumber)) {
                basqSql.append(" and t.license_plate like" + "'%" + licenseNumber + "%'");
                // params.put("licenseNumber", "%" + licenseNumber + "%");
            }

            //判断开始时间是否为空
            if (StrUtil.isNotEmpty(startTime)) {
                basqSql.append("  and t.start_time<=:startTime");
                params.put("startTime", startTime);
            }
            //判断结束时间是否为空
            if (StrUtil.isNotEmpty(endTime)) {
                basqSql.append("  and t.end_time>=:endTime");
                params.put("endTime", endTime);
            }
            basqSql.append("  group by t.cities, " +
                    "          t.equipment_manufacturer, " +
                    "          t.car_configu_ration, " +
                    "          t.license_plate, " +
                    "          t.car_cities");
            mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList(customDynamicWhere.queryNamedParameterForList(basqSql.toString(), paramMap));
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return mapList;

    }

    /**
     * 工单信息导出工单查询
     *
     * @param request
     * @param response
     * @param emergencyEquipment
     */
    @Override
    public void exportOrder(HttpServletRequest request, HttpServletResponse response, EmergencyEquipment emergencyEquipment) {
        String vehicleConfiguration = emergencyEquipment.getVehicleConfiguration();
        //获取车牌号
        String licenseNumber = emergencyEquipment.getLicenseNumber();
        //获取设备
        String equipmentManufacturer = emergencyEquipment.getEquipmentManufacturer();
        //获取归属地
        String placeOwnership = emergencyEquipment.getPlaceOwnership();
        //获取开始时间
        String startTime = emergencyEquipment.getStartTime();
        //获取结束时间
        String endTime = emergencyEquipment.getEndTime();
        Map<String, Object> params = CollectionUtil.newHashMap();
        params.put("vehicleConfiguration", vehicleConfiguration);
        params.put("licenseNumber", licenseNumber);
        params.put("placeOwnership", placeOwnership);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("equipmentManufacturer", equipmentManufacturer);
        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "-台账查询全量数据.xls";
        List<Map<String, Object>> formList = this.findByParameterQuery(params);
        List<ParameterQuery> list = CollectionUtil.newArrayList();
        for (Map<String, Object> map : formList) {
            ParameterQuery form = creatModel(map);
            list.add(form);
        }
        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<ParameterQuery> exportUtil = new ExcelUtil<ParameterQuery>(ParameterQuery.class);
            exportUtil.exportExcel(list, "台账查询全量数据", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 根据主单据ID删除 旧数据
     * @param pmInsId 主单据ID
     */
    @Override
    public void deleteBypmIntId(String pmInsId) {
        emergencyEquipmentRespository.deleteBypmIntId(pmInsId);
    }

    /**
     * 检车车辆是否可用
     *
     * @param params
     * @return
     */
    @Override
    public JsonResponse findByAvailable(Map<String, Object> params) {
        Boolean flagt = false;
        //获取车辆归属地市
        String cities = MapUtil.getStr(params, "cities");
        //获取车辆使用时间
        String licensePlate = MapUtil.getStr(params, "licensePlate");
        //获取车辆使用时间
        String startTime = MapUtil.getStr(params, "startTime");
        //获取车辆使用结束时间
        String endTime = MapUtil.getStr(params, "endTime");
        Specification<EmergencyEquipment> specification = Specifications.<EmergencyEquipment>and()
                .eq(StrUtil.isNotEmpty(cities), "cities", cities)
                .eq(StrUtil.isNotEmpty(licensePlate), "licensePlate", licensePlate)
                .le(StrUtil.isNotEmpty(startTime), "startTime", startTime)
                .ge(StrUtil.isNotEmpty(endTime), "endTime", endTime)
                .build();
        // 获取查询结果
        List<EmergencyEquipment> allNoPage = findAllNoPage(specification);
        if (CollectionUtil.isEmpty(allNoPage)) {
            flagt = true;
        }
        return JsonResponse.success(flagt);
    }

    /**
     * 根据车牌、地市、开始时间、结束时间找车
     *
     * @param licensePlate
     * @param belongCompanyName
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<Map<String, Object>> findByCarTime(String licensePlate, String belongCompanyName, String startTime, String endTime) {
        return emergencyEquipmentRespository.findByCarTime(licensePlate, belongCompanyName, startTime, endTime);
    }

    @Override
    public void updateArchiveTimeByPmInsId(String time, String pmInsId) {
        emergencyEquipmentRespository.updateArchiveTimeByPmInsId(time,pmInsId);
    }


    private ParameterQuery creatModel(Map<String, Object> map) {
        ParameterQuery parameterQuery = new ParameterQuery();
        //获取车辆归属地
        String placeownership = MapUtil.getStr(map, "placeownership");
        //获取设备信息
        String equipmentmanufacturer = MapUtil.getStr(map, "equipmentmanufacturer");
        //出车次数
        String carnumber = MapUtil.getStr(map, "carnumber");
        //无人机高空站
        String vehicleconfiguration = MapUtil.getStr(map, "vehicleconfiguration");
        //车牌号
        String licensenumber = MapUtil.getStr(map, "licensenumber");
        //出车地市
        String carcities = MapUtil.getStr(map, "carcities");
        //出车时长
        String cartime = MapUtil.getStr(map, "cartime");
        parameterQuery.setCarCities(carcities);
        parameterQuery.setPlaceOwnership(placeownership);
        parameterQuery.setEquipmentManufacturer(equipmentmanufacturer);
        parameterQuery.setCarNumber(carnumber);
        parameterQuery.setLicensePlate(licensenumber);
        parameterQuery.setCarTime(cartime);
        parameterQuery.setCarConfiguRation(vehicleconfiguration);
        return parameterQuery;
    }

    /**
     * 通过eeid查询车牌号
     * @param pmInsId
     * @return
     */
    @Override
    public EmergencyEquipment getEByPmInsId(String pmInsId ) {

        return emergencyEquipmentRespository.getEByPmInsId(pmInsId);
    }

    @Override
    public List<EmergencyEquipment> findByPmInsIdC(String pmInsId, String bxz) {
         return emergencyEquipmentRespository.findByPmInsIdC(pmInsId,bxz);
    }



    @Override
    public EmergencyEquipment findddd(String phoneNmu,String pmInsId) {
        return emergencyEquipmentRespository.findddd(phoneNmu,pmInsId);
    }

    @Override
    public List<EmergencyEquipment> finEmfindList(String phoneNmu, String pmInsId) {
        return emergencyEquipmentRespository.finEmfindList(phoneNmu,pmInsId);
    }

    @Override
    public List<EmergencyEquipment> findByPmInsIdAndD(String pmInsId ,String  bxz) {
        return emergencyEquipmentRespository.findByPmInsIdAndD(pmInsId ,bxz);
    }

    @Override
    public List<EmergencyEquipment> findByAll(String pmInsId) {
        return emergencyEquipmentRespository.findByAll(pmInsId);
    }

    @Override
    public List<EmergencyEquipment> findByPmInsIdAll(String pmInsId) {
        return emergencyEquipmentRespository.findByPmInsIdAll(pmInsId);
    }


}
