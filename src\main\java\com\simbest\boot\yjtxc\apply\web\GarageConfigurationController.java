package com.simbest.boot.yjtxc.apply.web;


import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.GarageConfiguration;
import com.simbest.boot.yjtxc.apply.service.IGarageConfigurationService;
import com.simbest.boot.yjtxc.util.FileTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:28
 * @describe 应急车库配置
 */
@Api(description = "应急车库配置")
@Slf4j
@RestController
@RequestMapping(value = "/action/garageConfiguration")
public class GarageConfigurationController extends LogicController<GarageConfiguration, String> {

    private IGarageConfigurationService service;

    public GarageConfigurationController(IGarageConfigurationService service) {
        super(service);
        this.service=service;
    }


    /**
     * 导入应急车库配置信息
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导入应急车库配置信息", notes = "导入应急车库配置信息")
    @PostMapping({"/importPerson", "/importPerson/sso"})
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return service.importPerson(request, response);
    }

    /**
     * 保存应急车库配置信息
     * @param garageConfigurationList
     * @return
     */
    @ApiOperation(value = "保存应急车库配置信息", notes = "保存应急车库配置信息")
    @PostMapping({"/saveGarageConfiguration", "/saveGarageConfiguration/sso"})
    public JsonResponse saveGarageConfiguration(@RequestBody(required = false) List<GarageConfiguration> garageConfigurationList) {
        return service.saveGarageConfiguration(garageConfigurationList);
    }

    /**
     * 删除应急车库配置信息
     * @param id
     * @return
     */
    @ApiOperation(value = "删除应急车库配置信息", notes = "删除应急车库配置信息")
    @PostMapping({"/deleteGarageConfiguration", "/deleteGarageConfiguration/sso"})
    public JsonResponse deleteGarageConfiguration(@RequestParam String id) {
        return service.deleteGarageConfiguration(id);
    }

    /**
     * 停用||启用、车库配置信息
     * @param id
     * @return
     */
    @ApiOperation(value = "停用||启用、车库配置信息", notes = "停用||启用、车库配置信息")
    @PostMapping({"/updateGarageConfiguration", "/updateGarageConfiguration/sso"})
    public JsonResponse updateGarageConfiguration(@RequestParam String id,
                                                  @RequestParam String flag) {
        return service.updateGarageConfiguration(id,flag);
    }


    /**
     * 条件查询车库配置信息
     * @param page
     * @param size
     * @param source
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @ApiOperation(value = "条件查询车库配置信息", notes = "条件查询车库配置信息")
    @PostMapping({"/findByAllGarageConfiguration", "/findByAllGarageConfiguration/sso"})
    public JsonResponse findByAllGarageConfiguration(@RequestParam Integer page,
                                                     @RequestParam Integer size,
                                                     @RequestParam(required = false) String source,
                                                     @RequestParam(required = false) String currentUserCode,
                                                     @RequestBody(required = false) Map<String, Object> paramMap) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, "desc", "createdTime");
        return service.findByAllGarageConfiguration(source, pageable, currentUserCode, paramMap);
    }
    /**
     * 下载应急设备配置库模板
     *
     * @param response
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = {"/downloadTemplate", "/downloadTemplate/sso"}, method = RequestMethod.POST)
    public Map<String, Object> downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/应急设备配置库模板导入.xls");
        FileTool.downloadIn(in, "应急设备配置库模板导入.xls", response);
        return null;
    }

    /**
     * 获取所有不重复的应急设备大类列表
     *
     * @return JsonResponse包含应急设备大类列表
     */
    @ApiOperation(value = "获取应急设备大类列表", notes = "获取GarageConfiguration表中所有不重复且未删除的应急设备大类数据")
    @PostMapping({"/getCarConfiguRationParentList", "/getCarConfiguRationParentList/sso", "/getCarConfiguRationParentList/api"})
    public JsonResponse getCarConfiguRationParentList() {
        return service.getCarConfiguRationParentList();
    }



    /**
     * 获取所有不重复的应急设备大类列表
     *
     * @return JsonResponse包含应急设备大类列表
     */
    @ApiOperation(value = "获取应急设备子类列表", notes = "获取GarageConfiguration表中所有不重复且未删除的应急设备子类数据")
    @PostMapping({"/getCarConfiguRationList", "/getCarConfiguRationList/sso"})
    public JsonResponse getCarConfiguRationList() {
        return service.getCarConfiguRationList();
    }

}
