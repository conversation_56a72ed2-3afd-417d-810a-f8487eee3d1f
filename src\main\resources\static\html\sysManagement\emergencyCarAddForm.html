<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>应急设备配置库</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            if(gps.id) {
                ajaxgeneral({
                    url: "action/garageConfiguration/findById?id="+gps.id,
                    success:function(data) {
                        formval(data.data, "form")
                    }
                })
            }
        });
        window.getchoosedata = function() {
            if(formValidate("form")) {
                return { data: getFormValue("form"), state: 1 }
            }
        }
    </script>
</head>
<body class="body_page">
<form id="form">
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>地市：</td>
            <td>
                <input id="cities" name="cities" type="text" value="" class="easyui-validatebox cities" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>设备厂家：</td>
            <td>
                <input id="equipmentManufacturer" name="equipmentManufacturer" type="text" value="" class="easyui-validatebox equipmentManufacturer" required="required" />
                <input id="id" name="id" type="hidden" value="" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>应急配置类型：</td>
            <td>
                <input id="carConfiguRation" name="carConfiguRation" type="text" value="" class="easyui-validatebox carConfiguRation" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>应急车车牌号：</td>
            <td>
                <input id="licensePlate" name="licensePlate" type="text" value="" class="easyui-validatebox licensePlate" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>司机：</td>
            <td>
                <input id="theDriver" name="theDriver" type="text" value="" class="easyui-validatebox theDriver" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>司机手机号：</td>
            <td>
                <input id="theDriverPhone" name="theDriverPhone" type="text" value="" class="easyui-validatebox theDriverPhone" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>负责开站人员：</td>
            <td>
                <input id="openingStation" name="openingStation" type="text" value="" class="easyui-validatebox openingStation" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>开站人员手机号：</td>
            <td>
                <input id="openingStationPhone" name="openingStationPhone" type="text" value="" class="easyui-validatebox openingStationPhone" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>5G改造情况：</td>
            <td>
                <input id="fivetransform" name="fivetransform" type="text" value="" class="easyui-validatebox fivetransform" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>5G基站站号：</td>
            <td>
                <input id="fiveBaseStation" name="fiveBaseStation" type="text" value="" class="easyui-validatebox fiveBaseStation" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>5G反开4G基站站号：</td>
            <td>
                <input id="fivereversefour" name="fivereversefour" type="text" value="" class="easyui-validatebox fivereversefour" required="required" />

            </td>
            <td width="110" align="right"><font class="col_r">*</font>4G FDD 基站站号：</td>
            <td>
                <input id="fourFDDBaseStation" name="fourFDDBaseStation" type="text" value="" class="easyui-validatebox fourFDDBaseStation" required="required" />

            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>4G TDD 基站站号：</td>
            <td>
                <input id="fourTDDBaseStation" name="fourTDDBaseStation" type="text" value="" class="easyui-validatebox fourTDDBaseStation" required="required" />
            </td>
            <td width="110" align="right"><font class="col_r">*</font>调度状态：</td>
            <td>
                <input id="schedulingCondition" name="schedulingCondition" type="text" value="" class="easyui-validatebox schedulingCondition" required="required" />
            </td>
            <td></td>
            <td>
        </tr>

    </table>
</form>
</body>
</html>
