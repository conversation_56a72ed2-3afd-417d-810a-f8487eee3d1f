package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/31 16:10
 * @describe 应急车调度台账
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_scheduling_ledger")
@ApiModel(value = "应急车调度台账")
public class SchedulingLedger extends LogicModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "sl") //主键前缀，此为可选项注解
    private String id;


    @Column(length = 100)
    @ApiModelProperty(value = "应急车配置类型")
    protected String vehicleConfiguration;

    @Column(length = 100)
    @ApiModelProperty(value = "车牌号")
    protected String licenseNumber;

    @Column(length = 100)
    @ApiModelProperty(value = "车辆归属地")
    protected String placeOwnership;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    protected String equipmentManufacturer;

    @Column(length = 100)
    @ApiModelProperty(value = "出车次数")
    protected Integer carNumber;

    @Column(length = 100)
    @ApiModelProperty(value = "出车时长")
    protected Integer carTime;

    @Column(length = 100)
    @ApiModelProperty(value = "出车地市")
    protected String carCities;


}
