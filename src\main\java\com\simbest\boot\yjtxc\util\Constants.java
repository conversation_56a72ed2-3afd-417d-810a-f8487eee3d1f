package com.simbest.boot.yjtxc.util;

import java.io.Serializable;

/**
 * <strong>Title :项目全局常量类，该类不允许new 继承等操作 </strong><br>
 * <strong>Description : 项目全局常量类，该类不允许new 继承等操作</strong><br>
 * <strong>Create on : 2018/6/26</strong><br>
 * <strong>Modify on : 2018/6/26</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public final class Constants implements Serializable {

    public static final String SOURCE_PC ="PC" ;

    public static final String PROCESS_BREATH_NAME = "日常应急调度-";
 public static final String PROCESS_BREATH_NAMEBS = "日常应急补收调度-";
    public static final String PROCESS_PROVINCE_NAME = "战时调度-";
    public static final String PROCESS_TYPE_A = "A";
    public static final String PROCESS_TYPE_B = "B";
    public static final String  PROCESS_TYPE_C = "C";

    public static final String RCDDSN = "RCDDSN"; //地市调度工单前缀
    public static final String RCDDSJ = "RCDDSJ"; //跨地市调度工单前缀
    public static final String ZSDD = "ZSDD"; //战时调度前缀


/*
   public static final String YJTXC_NOABRANCH ="com.yjtxc.flow.yjtxc_noabranch" ;  //分公司地市内调度
   public static final String YJTXC_PROVINCE ="com.yjtxc.flow.yjtxc_province" ;//分公司跨地市内调度
   public static final String YJTXC_BRANCH ="com.yjtxc.flow.yjtxc_branch" ;    //战时调度
*/

 public static final String YJTXC_NOABRANCH ="com.yjtxc.flow.yjtxc_noabranch" ;  //分公司地市内调度

   public static final String  YJTXC_BRANCH  ="com.yjtxc.flow.yjtxc_nobranch" ;//分公司跨地市内调度

   public static final String YJTXC_PROVINCE  ="com.yjtxc.flow.yjtxc_province" ;    //战时调度

    public static final String WURENJI = "无人机高空站";
    public static final String WEIXING = "卫星便携站";
    public static final String DAXINGYINGJICHE = "大型应急车";
    public static final String YINGJITONGXUNCHE  = "应急通讯车";

    public static final CharSequence SUCCESS_FLAG = "Y";


    /**
     * 私有构造方法，不允许new操作
     */
    private Constants() {

    }

    /**
     * 公司级别
     */
    public static final String COMPANY_TYPE_PROVINCE = "01";//01 省公司
    public static final String COMPANY_TYPE_BRANCH = "02";//02 地市分公司
    public static final String COMPANY_TYPE_COUNTY = "03";//03 县区分公司

    /**
     * 项目code
     */
    public static final String APP_CODE = "yjtxc";
    //出车地市应急车负责人配合调度
    public static final String COORDINATINGDISPATCHING = "yjtxc.coordinatingDispatching";
    //省网优应急车调度负责人审核
    public static final String SCHEDULINGCHIEFAUDIT = "yjtxc.schedulingChiefAudit";
    //战时调度--出车地市应急车负责人配合调度
    public static final String EMERGENCYVEHICLES = "yjtxc.emergencyVehicles";

    public static final String EMERGENCYVEHICLES_TO_COORDINATINGDISPATCHING = "yjtxc.schedulingChiefAudit_To_yjtxc.coordinatingDispatching"; // 省网优应急车调度负责人 同意配合调度




    public static final String A = "日常应急调度-地市内调动";
    public static final String B = "日常应急调度-跨地市调动";
    public static final String C = "战时调度";
     public static final String D = "应急通讯车分公司流程";


    /**
     * 项目中文名称
     */
    public static final String APP_NAME = "应急通讯车";


    /**
     * 流程最后环节,结束操作(申请人阅知、归档之类)
     */
    public static final String OUTCOME_END = "end";


 public static final String OUTCOME_END2 = "end2"; //废除归档

    /**
     * 统一待办中系统id
     */
    public static final String APP_SYS_ID = "1224";

    /**
     * 统一待办中待办标识
     */
    public static final String APP_WORK_TYPE = "1";

    /**
     * 操作成功
     */
    public static final String MESSAGE_SUCCESS = "操作成功";

    /**
     * 操作失败
     */
    public static final String MESSAGE_FAIL = "操作失败";

    /**
     * 手机端
     */
    public static final String MOBILE = "MOBILE";
    /**
     * PC端
     */
    public static final String PC = "PC";


    public static final String zone = "0";


    /**
     * 起草环节
     */
    public static final String STR_NULL = "null";

    public static final String USER_ROLE = "/action/user/role/";
    public static final String SSO = "/sso";
    /**
     * 抄送完成状态
     */
    public static final String COPY_STATUS = "1";


    /**
     * 起草环节
     */
    public static final String ACTIVITY_START = "yjtxc.start";



    /**
     * 应急车设备
     */

    public static final String WXC = "卫星车";
    public static final String BXZ = "大型应急车";
    public static final String WRJ = "无人机高空站";
    public static final String YJC = "卫星便携站";

}
