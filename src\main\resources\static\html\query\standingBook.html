
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待阅列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <style>
        .w100 a {
            min-width: 66px;
        }
    </style>

    <script type="text/javascript">
        var pageparam={
            "listtable":{
                "listname":"#standingBookTable",
                // "querycmd":"action/schedulingLedger/findBySchedulingLedger?source=PC",
                "querycmd":"action/emergencyEquipment/findByParameterQuery?source=PC",
                "contentType": "application/json; charset=utf-8",
                "nowrap": true,
                "styleClass":"noScroll",
                "frozenColumns":[],
                "columns":[[
                    { title: "应急车配置类型", field: "vehicleconfiguration", width: 200, rowspan: 1},
                    { title: "车牌号", field: "licensenumber", width: 200,tooltip:true},
                    { title: "车辆归属地", field: "placeownership", width: 100 },
                    { title: "设备厂家", field: "equipmentmanufacturer", width: 150 },
                    { title: "出车次数", field: "carnumber", width: 100 },
                    { title: "出车时长", field: "cartime", width: 100,
                        formatter: function(value, row, index) {
                            let hours = Math.floor(value / 60);
                            let remainingMinutes = value % 60;
                            return hours +"小时"+remainingMinutes+"分钟";
                        }},
                    { title: "出车地市", field: "carcities", width: 100}
                ]]
            }
        };
        $(function(){
            loadGrid(pageparam);
            $(".export").click(function () {
                $("#standingBookTableQueryForm").attr({"action":web.rootdir + "action/emergencyEquipment/exportOrder","method":"post"});
                $("#standingBookTableQueryForm").submit()
            })
        });
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="standingBookTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6">
        <tr>
            <td width="120" align="right">应急车配置类型：</td>
            <td width="300" class="select-td">
                <input id="type" name="type" class="easyui-combobox"
                       style="width: 100%; height: 32px;" data-options="
                        valueField: 'value',
                        textField: 'name',
                        panelHeight:'auto',
                        ischooseall:true,
                        editable:false,
                        queryParams:{'dictType':'car_type'},
                        url: web.rootdir+'action/queryDictValue/queryByType'" />
            </td>
            <td width="80" align="right">车牌号：</td>
            <td colspan="2">
                <input name="licenseNumber" type="text" value="" class="easyui-validatebox" />
            </td>
            <td width="105" align="right">车牌归属地市：</td>
            <td width="160">
                <input class="placeOwnership easyui-combobox" name="placeOwnership" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_type'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl ml15 searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
        <tr>
            <td width="120" align="right">设备厂家：</td>
            <td width="160">
                <input class="equipmentManufacturer easyui-combobox" name="equipmentManufacturer" style="width: 100%; height: 32px;"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       textField: 'name',
                       editable:false,
                       queryParams:{'dictType':'application_manufacturer'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
            <td width="80" align="right">出车区间：</td>
            <td width="120">
                <input name="startTime" type="text" class="easyui-datebox"
                       data-options="panelHeight:'auto',editable:false"
                       style="width:100%;height:32px;"/>
            </td>
            <td width="120">
                <input name="endTime" type="text" class="easyui-datebox"
                       data-options="panelHeight:'auto',editable:false"
                       style="width:100%;height:32px;"/>
            </td>
            <td></td>
            <td></td>
            <td>
                <div class="w100">
                    <a class="btn fl ml15 a_warning export"><font>导出</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="standingBookTable"><table id="standingBookTable"></table></div>
</body>
</html>
