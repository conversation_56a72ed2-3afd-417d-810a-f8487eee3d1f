package com.simbest.boot.yjtxc.wfquey.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.sys.model.SysDictValue;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 用途：数据字典
 * 时间：2018-10-18
 */
public interface QueryDictValueRepository extends LogicRepository<SysDictValue,String> {


    @Query(
            value = "select t.*  from SYS_DICT_VALUE t where t.dict_type =:dictType and t.value =:value",
            nativeQuery = true
    )
    List<SysDictValue> queryName(@Param("dictType") String dictType, @Param("value") String value);
}
