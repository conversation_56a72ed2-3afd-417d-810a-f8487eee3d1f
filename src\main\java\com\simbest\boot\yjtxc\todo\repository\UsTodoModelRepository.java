package com.simbest.boot.yjtxc.todo.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.todo.model.UsTodoModel;

/**
 * <strong>Title : UsTodoModelRepository</strong><br>
 * <strong>Description : 业务待办数据库操作</strong><br>
 * <strong>Create on : $date$</strong><br>
 * <strong>Modify on : $date$</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR>
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
public interface UsTodoModelRepository extends LogicRepository<UsTodoModel,Long> {
}
