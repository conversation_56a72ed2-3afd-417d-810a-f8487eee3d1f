package com.simbest.boot.yjtxc.apply.service;

import com.simbest.boot.base.service.ILogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.yjtxc.apply.model.InvolvesPersonnelConfiguration;
import com.simbest.boot.yjtxc.apply.model.SatellitePortableLibrary;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:14
 * @describe
 */
public interface ISatellitePortableLibraryService extends ILogicService<SatellitePortableLibrary, String> {


    /**
     * 导入卫星便携站配置库信息
     * @param request
     * @param response
     * @return
     */
    JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response);

    /**
     * 条件查询卫星便携站配置库
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    JsonResponse findByAllUavAerialConfigurationStation(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap);

    /**
     * 批量导入卫星便携战配置库
     * @param satellitePortableLibraryList
     * @return
     */
    JsonResponse saveSatellitePortableLibrary(List<SatellitePortableLibrary> satellitePortableLibraryList);

    /**
     * 修改卫星便携战配置库
     * @param satellitePortableLibraryList
     * @return
     */
    JsonResponse updateSatellitePortableLibrary(SatellitePortableLibrary satellitePortableLibraryList);

    /**
     * 删除卫星便携战配置库
     * @param id
     * @return
     */
    JsonResponse deleteBySatellitePortableLibrary(String id);

    List<Map<String, Object>> findBySatellite();

    /**
     * 卫星便携站按照地市条件查询
     * @return
     */
    List<Map<String, Object>> findBySatelliteAndCities(String cities);
}
