package com.simbest.boot.yjtxc.util;

import com.simbest.boot.security.IAuthService;
import com.simbest.boot.security.IUser;
import com.simbest.boot.security.auth.service.IAuthUserCacheService;
import com.simbest.boot.util.ApplicationContextProvider;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <strong>Title : </strong><br>
 * <strong>Description : </strong><br>
 * <strong>Create on : 2018/9/29/029</strong><br>
 * <strong>Modify on : 2018/9/29/029</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * @<NAME_EMAIL>
 * @version <strong>V1.0.0</strong><br>
 *          <strong>修改历史:</strong><br>
 *          修改人 修改日期 修改描述<br>
 *          -------------------------------------------<br>
 */
@Component
public class IuserNoSessionUtil implements Serializable {

    private static IAuthUserCacheService authUserCacheService;
    private static UumsSysUserinfoApi uumsSysUserinfoApi;

    static {
        authUserCacheService = ApplicationContextProvider.getBean(IAuthUserCacheService.class);
        uumsSysUserinfoApi = ApplicationContextProvider.getBean(UumsSysUserinfoApi.class);
    }

    public IUser getIuserByPreferredMobile( String preferredMobile) {
        IUser iUser = authUserCacheService.loadCacheUser(preferredMobile);
        if(null == iUser){
            iUser= uumsSysUserinfoApi.findByKey(preferredMobile, IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
            if(null != iUser){
                authUserCacheService.saveOrUpdateCacheUser(iUser);
            }
        }
        return iUser;
    }

    public IUser getIuserByUsername( String username) {
        IUser iUser = authUserCacheService.loadCacheUser(username);
        if(null == iUser){
            iUser= uumsSysUserinfoApi.findByKey(username, IAuthService.KeyType.username, Constants.APP_CODE);
            if(null != iUser){
                authUserCacheService.saveOrUpdateCacheUser(iUser);
            }
        }
        return iUser;
    }

}
