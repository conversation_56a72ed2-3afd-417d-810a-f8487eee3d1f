package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.base.model.LogicModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/31 15:51
 * @describe 应急设备
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_emergency_equipment")
@ApiModel(value = "应急设备")
public class EmergencyEquipment extends LogicModel {




    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "ee") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID")
    protected String eeId;

    @Column(length = 40)
    @ApiModelProperty(value = "回显车使用")
    protected String carId;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    protected String equipmentManufacturer;

    @Column(length = 140)
    @ApiModelProperty(value = "应急车配置类型")
    private String carConfiguRation;

    @Column(length = 140)
    @ApiModelProperty(value = "应急车配置大类")
    private String carConfiguRationParent;


    @Column(length = 140)
    @ApiModelProperty(value = "调度状态")
    protected String schedule;

    /**-------------------------------------------------------大型应急车||GSM卫星车------------------------------------*/
    @Column(length = 100)
    @ApiModelProperty(value = "调用应急车地市")
    private String cities;

    @Column(length = 140)
    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @Column(length = 100)
    @ApiModelProperty(value = "调用应急车联系人")
    private String theDriver;

    @Column(length = 100)
    @ApiModelProperty(value = "调用应急车电话号")
    private String theDriverPhone;

    @Column(length = 100)
    @ApiModelProperty(value = "调用应急车联系人")
    private String openingStation;

    @Column(length = 100)
    @ApiModelProperty(value = "调用应急车电话号")
    private String openingStationPhone;


    /**-------------------------------------------------------无人机字段-----------------------------------------------*/

    @Column(length = 100)
    @ApiModelProperty(value = "无人机高空基站负责人")
    @ExcelVOAttribute(name = "无人机高空基站负责人", column = "C")
    private String uavStationLeader;

    @Column(length = 100)
    @ApiModelProperty(value = "负责人手机号")
    @ExcelVOAttribute(name = "负责人手机号", column = "D")
    private String uavStationLeaderPhone;

    /**-------------------------------------------------------卫星字段------------------------------------*/

    @Column(length = 100)
    @ApiModelProperty(value = "卫星编写站负责人")
    @ExcelVOAttribute(name = "卫星编写站负责人", column = "C")
    private String chiefWritingStation;


    @Column(length = 100)
    @ApiModelProperty(value = "设备使用开始时间")
    protected String startTime;

    @Column(length = 100)
    @ApiModelProperty(value = "设备使用结束时间")
    protected String endTime;

    @Column(length = 100)
    @ApiModelProperty(value = "设备归档时间")
    protected String archiveTime;

    @Column(length = 10)
    @ApiModelProperty(value = "设备归档时间修改次数")
    protected String archiveTimeUpdateCount;//如果为空就是每改过，如果为1就是改过了

    @Column(length = 100)
    @ApiModelProperty(value = "出车地市")
    protected String carCities;

    @Column(length = 100)
    @ApiModelProperty(value = "出车时长")
    protected Integer carTime;

    @Column(length = 100)
    @ApiModelProperty(value = "废除归档标识" )
    protected String endState; //1废除归档 否则不废除

    /**----------------------------导出条件、不持久化存储----------------------------------*/
    @Transient
    @ApiModelProperty(value = "应急设备信息")
    protected String vehicleConfiguration;

    @Transient
    @ApiModelProperty(value = "应急设备联系人oa账号")
    protected String userName;

    @Transient
    @ApiModelProperty(value = "车牌号")
    protected String licenseNumber;

    @Transient
    @ApiModelProperty(value = "归属地")
    protected String placeOwnership;

    @Transient
    @ApiModelProperty(value = "开始时间")
    protected String guaranteeStartTime;

    @Transient
    @ApiModelProperty(value = "结束时间")
    protected String guaranteeEndTime;

    @Transient
    @ApiModelProperty(value = "调度状态")
    protected String schedule1;

    /**----------------------------导出条件、不持久化存储----------------------------------*/

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;







}
