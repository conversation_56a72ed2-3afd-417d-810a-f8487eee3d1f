package com.simbest.boot.yjtxc.apply.dto;


import com.simbest.boot.base.annotations.ExcelVOAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/3/21 14:01
 * @describe 工单查询导出xls数据
 */
@Data
public class ExportOrder implements Serializable {

    @ExcelVOAttribute(name = "工单标题", column = "A")
    private String orderTitle;  //工单标题

    @ExcelVOAttribute(name = "工单编号", column = "B")
    private String assignedBy; //工单编号

    @ExcelVOAttribute(name = "流程类型", column = "C")
    private String sourceOfMatters; //流程类型

    @ExcelVOAttribute(name = "申请应急设备配置", column = "D")
    protected String deviceConfiguration;

    @ExcelVOAttribute(name = "车牌号", column = "E")
    protected String licensePlate;

    @ExcelVOAttribute(name = "活动名称", column = "F")
    protected String nameEvent;

    @ExcelVOAttribute(name = "保障开始时间", column = "G")
    protected String guaranteeStartTime;

    @ExcelVOAttribute(name = "保障结束时间", column = "H")
    protected String guaranteeEndTime;

    @ExcelVOAttribute(name = "申请组织", column = "I")
    private String applyOrganization; //申请人

    @ExcelVOAttribute(name = "申请人", column = "J")
    private String leadershipUserName; //申请人

    @ExcelVOAttribute(name = "申请发起时间", column = "K")
    private String theOrganizerName;  //申请发起时间

    @ExcelVOAttribute(name = "当前环节", column = "L")
    private String organizerName; //当前环节






}
