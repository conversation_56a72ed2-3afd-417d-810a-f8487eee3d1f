package com.simbest.boot.yjtxc.mainbills.service.impl;

import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import com.simbest.boot.yjtxc.mainbills.repository.UsPmInstenceRepository;
import com.simbest.boot.yjtxc.mainbills.service.IUsPmInstenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <strong>Title : PmInstenceServiceImpl</strong><br>
 * <strong>Description : 业务主单据表业务操作</strong><br>
 * <strong>Create on : 2018/6/19</strong><br>
 * <strong>Modify on : 2018/6/19</strong><br>
 * <strong>Copyright (C) Ltd.</strong><br>
 *
 * <AUTHOR> zhang<PERSON><PERSON><PERSON>@simbest.com.cn
 * @version <strong>V1.0.0</strong><br>
 * <strong>修改历史:</strong><br>
 * 修改人 修改日期 修改描述<br>
 * -------------------------------------------<br>
 */
@Slf4j
@Service (value = "pmInstenceService")
public class UsPmInstenceServiceImpl extends LogicService<UsPmInstence,String> implements IUsPmInstenceService {

    private UsPmInstenceRepository pmInstenceRepository;

    @Autowired
    public UsPmInstenceServiceImpl ( UsPmInstenceRepository pmInstenceRepository ) {
        super( pmInstenceRepository );
        this.pmInstenceRepository = pmInstenceRepository;
    }

    /**
     * 逻辑删除操作
     * @param id
     * @return
     */
    @Override
    public int deleteByPmId(Long id) {
        return pmInstenceRepository.deleteByFromId(id);
    }

    @Override
    public int deleteByPmIdHexiao(String id) {
        return pmInstenceRepository.deleteByFromIdHexiao(id);
    }

    /**
     * 查找主单据
     * @param pmInsId 单据ID
     * @return
     */
    @Override
    public UsPmInstence findByPmInsId(String pmInsId) {
        return pmInstenceRepository.findByPmInsId(pmInsId);
    }
}
