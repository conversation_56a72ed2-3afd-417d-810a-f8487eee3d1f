﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org">
<head>
	<title>打印</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script type="text/javascript">
        $(function(){
            $(".ctable tbody").html("");
            loadForm("approvalInfoForm",gps);
            ajaxgeneral({
                url:"action/queryOpinionHistory/getWfOptMags",
                data:{"processInstId":gps.processInstId},
                success:function(data){
                    var html=[];
                    for(var i in data.data){
                        html.push('<tr><td>'+data.data[i].WORKITEMAPPROVERUSE+'</td><td>'+data.data[i].CONTENT+'</td><td>'+data.data[i].MODIFIEDTIME+'</td></tr>');
                    }
                    $(".ctable tbody").html(html.join(""));
                }
            });
        });
        window.getchoosedata=function(){
            //$("#approvalInfoForm").jqprint({
            //	debug: false,
            //	importCSS: true,
            //	printContainer: true,
            //	operaSupport: false
            //});
			window.print();
            return {"state":0};
		};
        function beforerender(data){

        };
	</script>
</head>
<body>
<form id="approvalInfoForm" method="post" contentType="application/json; charset=utf-8"
      cmd-select="action/currencyFrom/getFormDetail" beforerender="beforerender()">
	<h5 class="txtc f18">业务支撑工作流程申请表</h5>
	<table border="1" cellpadding="0" cellspacing="15" class="tabForm tabPrint">
        <tr>
            <td class="titInfo" width="150">申请人</td><td width="300" style="text-align:left;"><span id="applyUser">&nbsp;</span></td>
            <td class="titInfo" width="150">联系电话</td><td width="300" style="text-align:left;"><span id="phone">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">标题</td><td width="300" style="text-align:left;"><span id="title">&nbsp;</span></td>
            <td class="titInfo" width="150">申请人科室</td><td width="300" style="text-align:left;"><span id="departmentName">&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">缓急程度</td><td width="300" style="text-align:left;"><span id="urgencyDegree">&nbsp;</span></td>
            <td class="titInfo" width="150">选择类别</td><td width="300" style="text-align:left;"><span id="applyType">&nbsp;</span>&nbsp;&nbsp;</span></td>
        </tr>
        <tr>
            <td class="titInfo" width="150">申请内容</td><td colspan="3" style="text-align:left;"><span id="applyContent">&nbsp;</span>&nbsp;</td>
        </tr>

	</table>
	</br>
</form>
<!--table-->
<table border="0" cellpadding="0" cellspacing="0" class="ctable w100">
    <thead>
    <tr>
        <td width="50%">审批意见人</td>
        <td width="30%">审批意见</td>
        <td width="20%">审批时间</td>
    </tr>
    </thead>
    <tbody></tbody>
</table>
</body>
</html>
