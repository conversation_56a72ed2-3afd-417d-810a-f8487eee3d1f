﻿
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>已办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>

    <script type="text/javascript">

        function processJoin(appName,path,fields){
            var pageparam={
                "listtable":{
                    "listname":"#joinTable",
                    "querycmd":"action/queryActBusinessStatus/queryMyJoin?source=PC",
                    "nowrap": true,
                    "styleClass":"noScroll",
                    "frozenColumns":[],
                    "columns":[[//列
                        { title: "任务标题", field: "receiptTitle", width: 200, rowspan: 1,
                            formatter: function (value, row,index) {
                                var th=appNameTH(appName,path,row.pmInsType);
                                var g="<span class='audit col_b titleTooltipA' index='"+index+"' ptitle='"+th.type+"' path='"+th.html+"'>"+value+"</span>";
                                return g;
                            }
                        },
                        { title: "创建部门", field: "createOrgName", width: 140,tooltip:true},
                        { title: "创建人", field: "createUserName", width: 100 },
                        { title: "创建时间", field: "createTime", width: 150,
                            formatter: function(value, row, index) {
                                return getTimeDate(value, "yyyy-MM-dd hh:mm:ss")
                            }
                        },
                        { title: "当前已办人", field: "previousAssistantName", width: 100 },
                        { title: "办理时间", field: "previousAssistantDate", width: 150,
                            formatter: function(value, row, index) {
                                return getTimeDate(value, "yyyy-MM-dd hh:mm:ss")
                            }
                        },
                        { title: "当前办理环节", field: "activityInstName", width: 160,tooltip:true,
                            formatter: function (value, row,index) {
                                return row.currentState=="7"?"已归档":value;
                            }
                        },
                        { title: "申请应急设备配置", field: "deviceConfiguration", width: 100 },
                        { title: "应急车车牌号", field: "licensePlate", width: 100 },
                        { title: "活动名称", field: "nameEvent", width: 100 },
                        { title: "保障开始时间", field: "guaranteeStartTime", width: 150,
                            formatter: function(value, row, index) {
                                return value;
                            }
                        },
                        { title: "保障结束时间", field: "guaranteeEndTime", width: 150,
                            formatter: function(value, row, index) {
                                return value;
                            }
                        }

                    ] ]
                }
            };
            if(fields){
                fields=fields.split(",");
                for(var i in fields){
                    var fie=fields[i].split("-");
                    if(fie[0]=="pmInsType"){
                        pageparam.listtable.columns[0].push({ "title": fie[1], "field": fie[0], "width": 100,"formatter":function(value,row,index){
                                return appNameTH(appName,path,row.pmInsType).type;
                            } });
                    }else{
                        pageparam.listtable.columns[0].push({ "title": fie[1], "field": fie[0], "width": 100 });
                    }
                }
            }
            pageparam.listtable.columns[0].push({
                field: "opt", title: "操作", width: 160, rowspan: 1,
                formatter: function (value, row,index) {
                    var th=appNameTH(appName,path,row.pmInsType);
                    var g="<a class='audit' index='"+index+"' title='查看' ptitle='"+th.type+"' path='"+th.html+"'>【查看】</a>";
                    return g;
                }
            });
            loadGrid(pageparam);
            $(document).on("click","a.audit,span.audit",function(){
                var $t=$(this);
                var index=$t.attr("index");
                $("#joinTable").datagrid("clearSelections");
                $("#joinTable").datagrid("selectRow", index);
                var row = $("#joinTable").datagrid("getSelected");
                var url=$t.attr("path")+"?type=join&location="+row.activityDefId+"&processInstId="+row.processInstId+"&processDefName="+row.processDefName+"&workItemId="+row.workItemId+"&currentState="+row.currentState+"&pmInsId="+row.receiptCode+"&usPmInstenceId="+row.businessKey;
                var ptitle=$t.attr("ptitle")+"-"+row.receiptTitle+"-已办查看";
                top.dialogP(url,'processJoin',ptitle,'audit',true,"maximized","maximized",joinListLoad);
            });
        };

        $(function(){
            //ABC代表的是流程类型,后边的是对应的需要跳转的页面路径
            var htmls={"A":"html/apply/dailyLocal.html","B":"html/apply/dailyAcross.html","C":"html/apply/wartime.html"};
            //ABC代表的是流程类型,后边的是对应的是流程名，会显示在详情页面的头部
            var appName ={"A":"地市内调度","B":"跨地市调度","C":"战时调度"};
            processJoin(appName,htmls,"pmInsType-流程类型");
        });
    </script>
</head>
<body class="body_page">
<!--searchform-->
<form id="joinTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">

        <tr>
          <!--  <td width="90" align="right">工单编号：</td>
            <td width="150">
                <input name="pmInsId" type="text" value="" />
            </td>-->
            <td width="90" align="right">任务标题：</td>
            <td width="150">
                <input name="receiptTitle" type="text" value="" />
            </td>
            <td width="90" align="right">活动名称：</td>
            <td width="150">
                <input name="nameEvent" type="text" value="" />
            </td>
            <td width="90" align="right">车牌号：</td>
            <td width="150">
                <input name="licensePlate" type="text" value="" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<!--table-->
<div class="joinTable"><table id="joinTable"></table></div>
</body>
</html>
