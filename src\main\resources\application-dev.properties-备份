# \u914D\u7F6E\u53C2\u8003 https://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html

server.servlet.context-path=/wgzb
server.servlet.session.timeout=3600
server.servlet.session.cookie.max-age=3600
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;MODE=Oracle;AUTO_RECONNECT=TRUE;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.datasource.sql-script-encoding=UTF-8
#spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
#spring.jpa.properties.javax.persistence.schema-generation.scripts.action=create
#spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=yjtxc-ddl.sql
#\u914D\u7F6E\u521D\u59CB\u5316\u5927\u5C0F\u3001\u6700\u5C0F\u3001\u6700\u5927
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=10
spring.datasource.druid.max-active=20
#\u914D\u7F6E\u83B7\u53D6\u8FDE\u63A5\u7B49\u5F85\u8D85\u65F6\u7684\u65F6\u95F4
spring.datasource.druid.max-wait=60000
#\u914D\u7F6E\u95F4\u9694\u591A\u4E45\u624D\u8FDB\u884C\u4E00\u6B21\u68C0\u6D4B\uFF0C\u68C0\u6D4B\u9700\u8981\u5173\u95ED\u7684\u7A7A\u95F2\u8FDE\u63A5\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.time-between-eviction-runs-millis=60000  
#\u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.remove-abandoned=true
spring.datasource.druid.remove-abandoned-timeout=1800
spring.datasource.druid.log-abandoned=true
spring.datasource.pool-prepared-statements=true  
spring.datasource.max-pool-prepared-statement-per-connection-size=20
# \u914D\u7F6EStatFilter
spring.datasource.druid.filter.stat.db-type=h2
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=5000
# \u914D\u7F6EWallFilter
spring.datasource.druid.filter.wall.enabled=false
spring.datasource.druid.filter.wall.db-type=h2
spring.datasource.druid.filter.wall.config.delete-allow=true
spring.datasource.druid.filter.wall.config.drop-table-allow=true
# \u914D\u7F6ESlf4jLogFilter(\u6253\u5F00\u65F6\uFF0C\u9996\u6B21\u542F\u52A8\u5EFA\u8868\u65F6\uFF0C\u5220\u9664\u552F\u4E00\u952E\u7D22\u5F15\u62A5\u9519)
#spring.datasource.druid.filter.slf4j.enabled=true
#spring.datasource.druid.filter.slf4j.statement-sql-pretty-format=true
#spring.datasource.druid.filter.slf4j.statement-log-enabled=true
#spring.datasource.druid.filter.slf4j.statement-execute-query-after-log-enabled=false
# WebStatFilter\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_\u914D\u7F6EWebStatFilter
spring.datasource.druid.web-stat-filter.profile-enable=true
spring.datasource.druid.web-stat-filter.url-pattern=/druid/*
spring.datasource.druid.web-stat-filter.exclusions=*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
spring.datasource.druid.web-stat-filter.session-stat-enable=true
spring.datasource.druid.web-stat-filter.session-stat-max-count=10
# StatViewServlet\u914D\u7F6E\uFF0C\u8BF4\u660E\u8BF7\u53C2\u8003Druid Wiki\uFF0C\u914D\u7F6E_StatViewServlet\u914D\u7F6E
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.url-pattern=/druid/*
spring.datasource.druid.stat-view-servlet.reset-enable=true
spring.datasource.druid.stat-view-servlet.login-username=hadmin
spring.datasource.druid.stat-view-servlet.login-password=pass180418

spring.redis.cluster.nodes=localhost:6379
spring.redis.cluster.password=123456
spring.redis.cluster.max-redirects=3
spring.cache.redis.key-prefix=cache:key:wgzb:
spring.console.enabled=true
spring.console.path=/h2-console
spring.jpa.show-sql=true
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=h2
spring.messages.encoding=UTF-8
spring.http.encoding.charset=UTF-8
spring.thymeleaf.cache=false
spring.thymeleaf.enable-spring-el-compiler=true
spring.session.store-type=redis
spring.session.redis.cleanup-cron=0 * * * * *
spring.session.redis.namespace=spring:session:wgzb

management.endpoints.web.exposure.include=*

logback.groupId=com.simbest.boot
logback.artifactId=wgzb
app.file.upload.path=D:/file
app.file.upload.location=disk
app.oa.portal.token=SIMBEST_SSO
app.uums.address=http://************:8088/uums
app.swagger.address=http://localhost:8080/wgzb/swagger-ui.html
app.druid.monitor.address=http://localhost:8080/wgzb/druid
app.actuator.monitor.address=http://localhost:8080/wgzb/actuator
app.task.heart.test.job=0 0/5 * * * ?
app.captcha.enable=false
app.host.port=http://************:8088
app.host.port.bps=http://************:8088

app.bps.tenant=true
app.bps.tenant_id=wgzb

#\u7EDF\u4E00\u5F85\u529E-\u63A5\u53E3\u5E73\u53F0WebService\u5730\u5740
cxf.openTodoUrl=http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoOpenListInfoSrv?wsdl
cxf.closeTodoUrl=http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCloseListInfoSrv?wsdl
cxf.cancelTodoUrl=http://***********:8011/OA/proxy/SB_OA_OA_ImportToDoCancelListInfoSrv?wsdl


#Spring Boot Admin\u76D1\u63A7
#spring.application.name=\u4E1A\u52A1\u652F\u6491\u4E2D\u5FC3\u516C\u7AE0\u5BA1\u6279\u6D41\u7A0B-yjtxc-10081
#spring.boot.admin.client.url=http://localhost:8088/admin
#spring.boot.admin.client.username=hadmin
#spring.boot.admin.client.password=111.com

#Csrf \u9632\u76D7\u94FE\u4FDD\u62A4\u4E3B\u673A\u767D\u540D\u5355
app.security.white.hosts=localhost,hrpamgt,************,portaltest.ha.cmcc,***********,portal.ha.cmcc,iportal.ha.cmcc,***********,***********,************,************,************,************,************,************
