<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:th="http://www.thymeleaf.org"
	  xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">
<head>
	<title>应急通信调度情况及调度流程</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css}" rel="stylesheet"/>
    -->
	<link href="../fonts/iconfont/iconfont.css" th:href="@{/fonts/iconfont/iconfont.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/js/themes/icon.css" th:href="@{http://************:8088/simbestui/js/themes/icon.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/css/public.css" th:href="@{http://************:8088/simbestui/css/public.css}" rel="stylesheet"/>
	<link href="http://************:8088/simbestui/css/index.css" th:href="@{http://************:8088/simbestui/css/index.css}" rel="stylesheet"/>
	<script src="http://************:8088/simbestui/js/jquery.min.js" th:src="@{http://************:8088/simbestui/js/jquery.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.calendar.js" th:src="@{http://************:8088/simbestui/js/jquery.calendar.js}" type="text/javascript"></script>
	<script src="../js/jquery.config.js" th:src="@{/js/jquery.config.js}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js}" type="text/javascript"></script>
	<style>
		.right_tab a font img {
			display: none;
		}
	</style>
</head>
<body>
<!--top-->
<div class="top">
	<div class="top_left">
		<div class="top_left_top"><a href=""><img class="fl" src="http://************:8088/simbestui/images/logo_all.png" th:src="@{http://************:8088/simbestui/images/logo_all.png}" /></a><h1 class="fl">应急通信调度情况及调度流程</h1></div>
	</div>
	<div class="top_right">
		<!-- <img id="logo" src="${ctx}/images/users_images.png" />-->
		<strong>您好：<font class="nickname" th:text="${indexModel.iuser.nickname}">aa</font><font class="indexDate"></font></strong>
		<input id="userName" th:value="${indexModel.iuser.username}" type="hidden"/><input id="phone" th:value="${indexModel.iuser.preferredMobile}" type="hidden"/>
		<!--  <i class="iconfont" id="setup" path="setup.html" title="设置">&#xe62f;</i><span></span>-->
		<i class="iconfont" id="home" th:attr="path=@{/html/process/processTask.html}" title="首页">&#xe691;</i><span></span>
		<!--<i class="iconfont" id="password" th:attr="path=@{/html/updatepassword.html}" title="修改密码">&#xe682;</i><span></span>-->
		<a th:href="@{/logout}" title="退出"><i class="iconfont" id="exit">&#xe68a;</i></a>
	</div>
</div>
<!--center-->
<div class="center">
	<!--left-->
	<div class="left">
		<div class="left_menu">
		</div>
	</div>
	<!--right-->
	<div class="right">
		<div class="right_tab"><a id="li_home" class="a_hover"><i class="fl iconfont">&#xe691;</i><font>首页</font></a></div>
		<div>
			<iframe id="li_home_if" name="home" class="iframe_first" src="/html/process/processTask.html" th:src="@{/html/process/processTask.html}" frameborder="0" scrolling="auto"></iframe>
			<!--<iframe id="rightiframe" src="apply/meetingForm.html" frameborder="0" scrolling="auto"></iframe>-->
		</div>
	</div>
</div>
<script th:inline="javascript">
	var ih;
    function menuH(data,ii,outH,outToken){
        var html=["<ul class='nav"+ii+"'>"];
        for (var i in data) {
            html.push("<li class='work'>");
            if(data[i].children){
                html.push("<a id='"+data[i].aid+"' path=''><i class='fr iconfont down'>&#xe673;</i><i class='fl iconfont'>"+(data[i].icon || "&#xe604;")+"</i><font>"+data[i].description+"</font></a>");
                html.push(menuH(data[i].children,(ii+1),outH,outToken));
            }else{
                var urlgps=data[i].url?data[i].url.split("/"):[];
                data[i].aid=urlgps.length>0?urlgps[urlgps.length-1].split(".")[0]:"";
                if(data[i].url.indexOf("?furl=")>-1){
                    data[i].aid=new Date().getTime();
				}
                if(data[i].permissionCode && data[i].permissionCode.indexOf("out:")>-1){
                    html.push("<a id='" + data[i].aid + "' isOut='true' path='"+outH + data[i].url+"?ACCESSTOKEN="+outToken + "'><i class='fl iconfont'>" + (data[i].icon || "&#xe604;") + "</i><font>" + data[i].description + "</font></a>");
                }else {
                    html.push("<a id='" + data[i].aid + "' path='" + data[i].url + "'><i class='fl iconfont'>" + (data[i].icon || "&#xe604;") + "</i><font>" + data[i].description + "</font></a>");
                }
            }
            html.push("</li>");
        }
        html.push("</ul>");
        return html.join("");
    };
    // 给待办、待阅加上小喇叭
	function addTips(type) {
		ajaxgeneral({
			url: "action/queryActBusinessStatus/"+(type == "read" ? "queryMyPending" : "myTaskToDo")+"?source=PC&page=1&rows=10&size=10",
			data: {page: 1, rows: 10},
			success: function(data) {
				if(data.data && data.data.totalElements > 0) {
					$(".nav1>li:eq(0)>.nav2>li:eq("+(type == "read" ? 2 : 0)+")>a>font").append($("<img style='margin-left: 10px;'>").attr("src", "http://portalui.ha.cmcc/portal/wps_appnav/images/ico-tishi.gif"))
				}
			}
		})
	}
	$(function () {
        $(".indexDate").html(getNow("&nbsp;&nbsp;yyyy年MM月dd日&nbsp;&nbsp;星期weekday",true)+"&nbsp;&nbsp;农历&nbsp;&nbsp;"+showCal()+"&nbsp;&nbsp;");
        ajaxgeneral({
            url:"uums/sys/userinfo/findPermissionByAppUser?appcode="+web.appCode+"&username="+[[${indexModel.iuser.username}]],
            success:function(data){
                if(data.data) {
                    var datai = data.data.sort(function (a, b) {
                        return (a.displayOrder > b.displayOrder) ? 1 : -1;
                    });
                    var datas = toTreeData(datai, "id", "parentId", "id,parentId,description,url,icon");
                    var htmls = menuH(datas, 1);
                    $(".left_menu").html(htmls);

                    // 隐藏指定的系统配置子模块
                    hideSpecificMenuItems();

                    // 修改应急车配置库模块名称为应急设备配置库
                    updateMenuItemNames();

					// addTips("task")
					// addTips("read")
                    // 默认打开我的工作
                    // $(".nav1>li:eq(0)>a").trigger("click");
                }
            }
        });
        //打开首页计算高度
		$(".right").css("width", $(window).width() - 218);
		calculateHeight();
		$(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));
		//菜单事件
		$(document).on("click",".nav1 a",function (e) {
			var path = $(this).attr("path");
			//为空表示有多级菜单
			var $sib = $(this).parent().siblings();
			$(".nav1 a").removeClass("a_active");
			$sib.children("a").removeClass("a_hover").children("i.down").html("&#xe673;");
			$sib.children("ul").hide();
			$(this).addClass("a_active a_hover");
			if (path != "") {
				$(this).parents("ul").prev("a").addClass("a_hover");
				if ($("#li_" + $(this).attr("id")).length > 0) {
					$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
					$("#li_" + $(this).attr("id") + "_if").show().siblings().hide();
					$(".right_tab a i.fr").removeClass("i_color");
					$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");

					$("#li_" + $(this).attr("id")).insertAfter("#li_home");
					var tourl = path + (path.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
					$("#li_" + $(this).attr("id") + "_if").insertAfter("#li_home_if").attr("src",tourl);
				}
				else {
					$(".right iframe").hide();
					//IE6、IE7样式
					if ($.support.msie) {//($.browser.msie && ($.browser.version == "6.0") && !$.support.style) || ($.browser.msie && ($.browser.version == "7.0"))
						$("<a id='li_" + $(this).attr("id") + "' class='a_hover'><i class='iconfont fr i_color'>&#xe63e;</i>" + $(this).html() + "</a>").insertAfter("#li_home");
						$("#li_" + $(this).attr("id")).css("width", ($(this).children("font").text().length) * 18 + 15);
					} else {
						$("<a id='li_" + $(this).attr("id") + "' class='a_hover'>" + $(this).html() + "<i class='iconfont fr i_color'>&#xe63e;</i></a>").insertAfter("#li_home");
					}
					$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
					$(".right_tab a i.fr").removeClass("i_color");
					$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");
					var url = $(this).attr("path");
					var tourl = url + (url.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
					$("<iframe name='" + $(this).attr("id") + "' id='li_" + $(this).attr("id") + "_if' style='height:"+ih+"px;' src='" + tourl + "' frameborder='0'></iframe>").insertAfter("#li_home_if");
					//$("#rightiframe").attr("src","${ctx}/" + tourl);
				}
			} else {
				$(this).children("i.down").html("&#xe674;");
				var $nextli = $(this).next("ul").children("li");
				$nextli.children("ul").hide();
				$nextli.children("a").removeClass("a_hover").children("i.down").html("&#xe674;");
				$next = $(this).next("ul");
				$next.slideDown(600, function () {
					$next.show();
				});
			}
			e.stopPropagation();
		});
		//点击设置和修改密码
		$(document).on("click",".top_right i", function (e) {
			if ($(this).attr("path") != null) {
				if ($("#li_" + $(this).attr("id")).length > 0) {
					$("#li_" + $(this).attr("id")).trigger("click");
					$("#li_" + $(this).attr("id")).insertAfter("#li_home");

					var path = $(this).attr("path");
					var tourl = path + (path.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
					$("#li_" + $(this).attr("id") + "_if").insertAfter("#li_home_if").attr("src",tourl);
				}else {
					$(".right_nr iframe").hide();
					//IE6、IE7样式
					if ($.support.msie) {//($.browser.msie && ($.browser.version == "6.0") && !$.support.style) || ($.browser.msie && ($.browser.version == "7.0"))
						$("<a id='li_" + $(this).attr("id") + "' class='a_hover'><i class='iconfont fr i_color'>&#xe63e;</i>" + $(this).attr("title") + "</a>").insertAfter("#li_home");
						$("#li_" + $(this).attr("id")).css("width", ($(this).attr("title").length) * 18 + 15);
					} else {
						$("<a id='li_" + $(this).attr("id") + "' class='a_hover'>" + $(this).attr("title") + "<i class='iconfont fr i_color'>&#xe63e;</i></a>").insertAfter("#li_home");
					}
					$("#li_" + $(this).attr("id")).addClass("a_hover").siblings().removeClass("a_hover");
					$(".right_tab a i.fr").removeClass("i_color");
					$("#li_" + $(this).attr("id") + " i.fr").addClass("i_color");
					$(".right iframe").hide();
					var url = $(this).attr("path");
					var tourl = url + (url.indexOf("?") > 0 ? "&" : "?") + "tm=" + (new Date()).getTime();
					$("<iframe name='" + $(this).attr("id") + "' id='li_" + $(this).attr("id") + "_if' style='height:"+ih+"px;' src='" + tourl + "' frameborder='0'></iframe>").insertAfter("#li_home_if");
				}
				e.stopPropagation();
			}
		});
		//单击右侧顶部菜单选项
		$(document).on("click",".right_tab a", function () {
			$(".nav1 a").removeClass("a_hover");
			$(".nav1 a#" + $(this).attr("id").substr(3)).addClass("a_hover");
			$(".nav1 li ul").hide();
			$(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").show();
			$(".nav1 a#" + $(this).attr("id").substr(3)).parents("ul").prev().addClass("a_hover");
			$(this).addClass("a_hover").siblings().removeClass("a_hover");
			$(".right_tab a i.fr").removeClass("i_color");
			$("#" + $(this).attr("id") + " i.fr").addClass("i_color");
			$("#" + $(this).attr("id") + "_if").show().siblings().hide();
			//$(".nav1 a#" + $(this).attr("id").substr(3)).trigger("click");
		});
		//单击右侧顶部菜单关闭按钮
		$(document).on("click",".right_tab a i.fr", function (e) {
			if ($(".right iframe:visible").attr("id") == $(this).parent().attr("id") + "_if") {
				if ($(this).parent().next().length > 0) {
					$(this).parent().next().trigger("click");
				}
				else if ($(this).parent().prev().length > 0) {
					$(this).parent().prev().trigger("click");
				}
			}
			$(this).parent().remove();
			$("#" + $(this).parent().attr("id") + "_if").remove();
			e.stopPropagation();
		});
	});

	// 隐藏指定的系统配置子模块
	function hideSpecificMenuItems() {
	    // 隐藏无人机高空站配置库
	    $(".left_menu a[path*='uavConfig.html']").parent().hide();

	    // 隐藏卫星便携站配置库
	    $(".left_menu a[path*='satelliteConfig.html']").parent().hide();
	}

	// 修改菜单项名称
	function updateMenuItemNames() {
	    // 将"应急车配置库"修改为"应急设备配置库"
	    $(".left_menu a[path*='emergencyCarConfig.html'] font").text("应急设备配置库");
	}

	//计算高度
	function calculateHeight() {
		//var a = $(".left").height();
		var a = 335;
		var b = $(".right").height();
		var c = $(window).height() - 60;
		if (c < a || c < b) {
			if (a < b) {
				$(".center").css("height", b);
				$(".left").css("height", b);
				$(".left_menu").css("height", b);
				$(".right").css("height", b);
				$(".right iframe").css("height", b-47);
				ih=b-47;
			} else {
				$(".center").css("height", a);
				$(".left").css("height", a);
				$(".left_menu").css("height", a);
				$(".right").css("height", a);
				$(".right iframe").css("height", a-47);
				ih=a-47;
			}
		} else {
			$(".center").css("height", c);
			$(".left").css("height", c);
			$(".left_menu").css("height", c);
			$(".right").css("height", c);
			$(".right iframe").css("height", c-47);
			ih=c-47;
		}
	};
	$(window).resize(function () {
		$(".right").css("width", $(window).width() - 218);
		calculateHeight();
		$(".top_left").css("width", ($(".top").width() - $(".top_right").width() - 21));
	});
</script>
</body>
</html>