package com.simbest.boot.yjtxc.apply.web;

import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.yjtxc.apply.model.SatellitePortableLibrary;
import com.simbest.boot.yjtxc.apply.model.UavAerialConfigurationStation;
import com.simbest.boot.yjtxc.apply.service.IUavAerialConfigurationStationService;
import com.simbest.boot.yjtxc.util.FileTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:19
 * @describe
 */
@Api(description = "无人机高空配置库")
@Slf4j
@RestController
@RequestMapping(value = "/action/uavAerialConfigurationStation")
public class UavAerialConfigurationStationController extends LogicController<UavAerialConfigurationStation, String> {

    private IUavAerialConfigurationStationService service;

    public UavAerialConfigurationStationController(IUavAerialConfigurationStationService service) {
        super(service);
        this.service = service;
    }

    @Autowired
    private SysFileService fileService;


    /**
     * 导入流程固化涉及人员配置信息
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导入无人机高空配置库信息", notes = "导入无人机高空配置库信息")
    @PostMapping({"/importPerson", "/importPerson/sso"})
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return service.importPerson(request, response);

    }


    @ApiOperation(value = "条件查询无人机高空配置库信息", notes = "条件查询无人机高空配置库信息")
    @PostMapping({"/findByAlluavAerialConfigurationStation", "/uavAerialConfigurationStation/sso"})
    public JsonResponse findByAllUavAerialConfigurationStation(@RequestParam Integer page,
                                                               @RequestParam Integer size,
                                                               @RequestParam(required = false) String source,
                                                               @RequestParam(required = false) String currentUserCode,
                                                               @RequestBody(required = false) Map<String, Object> paramMap) {
        //获取分页规则, page第几页 size每页多少条 direction升序还是降序 properties排序规则（属性名称）
        Pageable pageable = service.getPageable(page, size, "desc", "createdTime");
        return service.findByAllUavAerialConfigurationStation(source, pageable, currentUserCode, paramMap);
    }


    /**
     * 下载无人机高空配置库信息模板
     *
     * @param response
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = {"/downloadTemplate", "/downloadTemplate/sso"}, method = RequestMethod.POST)
    public Map<String, Object> downloadTemplate(HttpServletResponse response, HttpServletRequest request) throws Exception {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("model/无人机高空战配置库.xls");
        FileTool.downloadIn(in, "无人机高空战配置库.xls", response);
        return null;
    }

    /**
     * 无人机高空配置库信息
     * @param uavAerialConfigurationStation
     * @return
     */
    @ApiOperation(value = "保存无人机高空配置库信息", notes = "保存卫星便携战配置库")
    @PostMapping({"/saveUavAerialConfigurationStation", "/saveUavAerialConfigurationStation/sso"})
    public JsonResponse saveUavAerialConfigurationStation(@RequestBody List<UavAerialConfigurationStation> uavAerialConfigurationStation) {
        return service.saveUavAerialConfigurationStation(uavAerialConfigurationStation);
    }

    @ApiOperation(value = "修改无人机高空配置库信息", notes = "修改无人机高空配置库信息")
    @PostMapping({"/updateUavAerialConfigurationStation", "/updateUavAerialConfigurationStation/sso"})
    public JsonResponse updateUavAerialConfigurationStation(@RequestBody UavAerialConfigurationStation uavAerialConfigurationStation) {
        return service.updateUavAerialConfigurationStation(uavAerialConfigurationStation);
    }

    @ApiOperation(value = "删除无人机高空配置库信息", notes = "删除无人机高空配置库信息")
    @PostMapping({"/deleteBySatellitePortableLibrary", "/deleteBySatellitePortableLibrary/sso"})
    public JsonResponse deleteByUavAerialConfigurationStation(@RequestParam String id ) {
        return service.deleteByUavAerialConfigurationStation(id);
    }

}
