package com.simbest.boot.yjtxc.attachment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.yjtxc.attachment.repository.FileExtendRepository;
import com.simbest.boot.yjtxc.attachment.service.IFileExtendService;
import com.simbest.boot.yjtxc.token.request.ValueConstants;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.config.AppConfig;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.exceptions.AppRuntimeException;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.util.AppFileUtil;
import com.simbest.boot.util.CodeGenerator;
import com.simbest.boot.util.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 作用：附件扩展类
 * 作者：zsf
 * 时间：2018/07/28
 */
@Slf4j
@Service
public class FileExtendServiceImpl extends LogicService<SysFile, String> implements IFileExtendService {
    public static final String FILE_ERROR = "文件操作异常【%s】";
    private FileExtendRepository fileExtendRepository;

    @Autowired
    private AppFileUtil appFileUtil;

    @Autowired
    private AppConfig config;

    @Autowired
    private ValueConstants valueConstants;

    @Autowired
    public FileExtendServiceImpl(FileExtendRepository repository) {
        super(repository);
        this.fileExtendRepository = repository;
    }

    /**
     * 更新附件
     *
     * @param pmInsId 主单据id
     * @param id      附近id
     * @return
     */
    @Override
    public int updatePmInsId(String pmInsId, String id) {
        return fileExtendRepository.updatePmInsId(pmInsId, id);
    }
    @Override
    public List<SysFile> getFileById(String[] id) {
        return fileExtendRepository.getFileById(id);
    }
    /**
     * 查询区域附件
     *
     * @param pmInsId  流程实例id
     * @param filePart 区域标识
     * @return
     */
    @Override
    public List<SysFile> getPartFile(String pmInsId, String filePart) {
        return fileExtendRepository.getPartFile(pmInsId, filePart);
    }

    @Override
    public int deleteByPmInsId(String pmInsId) {
        return fileExtendRepository.deleteByPmInsId(pmInsId);
    }

    /**
     * 上传视频到共享存储
     *
     * @param multipartFiles
     * @param pmInsType
     * @param pmInsId
     * @param pmInsTypePart
     * @return
     */

    @Override
    public List<SysFile> uploadProcessFiles(Collection<MultipartFile> multipartFiles, String pmInsType, String pmInsId, String pmInsTypePart) {
        List<SysFile> sysFileList = Lists.newArrayList();
        try {
            sysFileList = appFileUtil.uploadFiles(prepareDirectory(pmInsType, pmInsId, pmInsTypePart), multipartFiles);
            //saveSysFileList(pmInsType, pmInsId, pmInsTypePart, sysFileList);
            for (SysFile sysFile : sysFileList) {
                sysFile.setPmInsType(pmInsType);
                sysFile.setPmInsId(pmInsId);
                sysFile.setPmInsTypePart(pmInsTypePart);
                String mobileFilePath = config.getAppHostPort() + sysFile.getFilePath();
                //保存共享存储能访问的路径
                String filePath = sysFile.getFilePath();
                String preFilePath = valueConstants.getPreFilePath();
                String substring = filePath.substring(preFilePath.length());
                sysFile.setMobileFilePath(config.getAppHostPort()+substring);
                this.insert(sysFile);

                sysFile.setDownLoadUrl(sysFile.getDownLoadUrl().concat("?id=" + sysFile.getId())); //修改下载URL，追加ID
                sysFile.setApiFilePath(config.getAppHostPort() + "/" + Constants.APP_CODE + sysFile.getApiFilePath().concat("?id=" + sysFile.getId()));
                sysFile.setAnonymousFilePath(config.getAppHostPort() + "/" + Constants.APP_CODE + sysFile.getAnonymousFilePath().concat("?id=" + sysFile.getId()));
                //保存真实路径
                /*sysFile.setApiFilePath(mobileFilePath);
                 sysFile.setAnonymousFilePath(mobileFilePath);*/
                this.update(sysFile);
            }

        } catch (Exception e) {
            throw new AppRuntimeException(String.format(FILE_ERROR, e.getMessage()));
        }
        return sysFileList;
    }

    @Override
    public void updatePmInsIdTwo(String id, String id1) {
        fileExtendRepository.updatePmInsIdTwo(id,id1);
    }

    @Override
    public List<SysFile> finListSysFile(String formId) {
        return fileExtendRepository.finListSysFile(formId);
    }

    private String prepareDirectory(String pmInsType, String pmInsId, String pmInsTypePart) {
        String pmInsTypePath = StrUtil.isEmpty(pmInsType) ? "" : pmInsType.concat(ApplicationConstants.SLASH);
        String pmInsIdPath = StrUtil.isEmpty(pmInsId) ? "" : pmInsId.concat(ApplicationConstants.SLASH);
        String pmInsTypePartPath = StrUtil.isEmpty(pmInsTypePart) ? "" : pmInsTypePart.concat(ApplicationConstants.SLASH);
        String username = SecurityUtils.getCurrentUserName();
        String directory = StringUtils.removeEnd(pmInsTypePath + username + ApplicationConstants.SLASH
                + CodeGenerator.systemUUID() + ApplicationConstants.SLASH
                + pmInsTypePartPath + pmInsIdPath, ApplicationConstants.SLASH);
        log.debug("上传路径地址为【{}】", directory);
        return directory;
    }
}
