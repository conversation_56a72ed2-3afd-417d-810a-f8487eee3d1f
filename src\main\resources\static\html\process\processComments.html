﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
	<title>查看意见</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<script type="text/javascript">

		function loadProcessComments() {
			var urld = { "processInstId": gps.processInstId };
			var pageparam = {
				"listtable": {
					"listname": "#commentsTable",//table列表的id名称，需加#
					"querycmd": "action/queryOpinionHistory/getWfOptMags",//table列表的查询命令+(gps.from?"/sso":"")
					//"contentType":"application/json; charset=utf-8",//table列表的传参方式，如果是json就写，不写默认kv
					"nowrap": true,//把数据显示在一行里,默认true
					"queryParams": urld,
					"dataName": "approval",
					"styleClass": "noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
					"pagination": false,
					"frozenColumns": [],//固定在左侧的列
					"columns": [[//列
						{ title: "环节名称", field: "activityInstName", width: 25, tooltip: true },
						{ title: "审批意见人", field: "WORKITEMAPPROVERUSE", width: 25, tooltip: true },
						{ title: "审批意见", field: "CONTENT", width: 25, tooltip: true },
						{ title: "审批时间", field: "MODIFIEDTIME", width: 25 }//排序sortable: true
					]],
					"onLoadSuccess": function (data) {
						if (data.data.copy && data.data.copy.length > 0) {
							$(".copyCommentsTable,.copyT").show();
							var copyparam = {
								"listtable": {
									"listname": "#copyCommentsTable",//table列表的id名称，需加#
									"nowrap": true,//把数据显示在一行里,默认true
									"data": data,
									"styleClass": "noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
									"pagination": false,
									"dataName": "copy",
									"frozenColumns": [],//固定在左侧的列
									"columns": [[//列
										{ title: "抄送人", field: "recipientName", width: 200, tooltip: true },
										{ title: "抄送意见", field: "content", width: 200, tooltip: true },
										{ title: "抄送时间", field: "modifiedTime", width: 150, tooltip: true }//排序sortable: true
									]]
								}
							};
							loadGrid(copyparam);
						}
						if (data.data.checkup && data.data.checkup.length > 0) {
							$(".checkupCommentsTable,.checkupT").show();
							top.dialogSetTitle("processComments", $(".checkupT").text());
							$(".checkupT b").text("审批意见");
							var checkupparam = {
								"listtable": {
									"listname": "#checkupCommentsTable",//table列表的id名称，需加#
									"nowrap": true,//把数据显示在一行里,默认true
									"data": data,
									"styleClass": "noScroll",//"scrollbarSize":0,scrollbarSize参数不管用，easyui写死为18了
									"pagination": false,
									"dataName": "checkup",
									"frozenColumns": [],//固定在左侧的列
									"columns": [[//列
										{ title: "环节名称", field: "CONTENT", width: 200, tooltip: true },
										{ title: "审批意见人", field: "WORKITEMAPPROVERUSE", width: 200, tooltip: true },
										{ title: "审批意见", field: "CONTENT", width: 200, tooltip: true },
										{ title: "审批时间", field: "MODIFIEDTIME", width: 150 }//排序sortable: true
									]]
								}
							};
							loadGrid(checkupparam);
						}
					}
				}
			};
			loadGrid(pageparam);
		};

		$(function () {
			loadProcessComments();
		});
	</script>
</head>

<body>
	<!--table-->
	<div class="commentsTable">
		<table id="commentsTable"></table>
	</div>

	<h6 class="hide copyT bor_b txtc p10"><b>抄报意见</b></h6>
	<!--table-->
	<div class="copyCommentsTable hide">
		<table id="copyCommentsTable"></table>
	</div>
</body>

</html>