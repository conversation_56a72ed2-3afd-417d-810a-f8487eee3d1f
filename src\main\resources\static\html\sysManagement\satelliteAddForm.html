<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){
            if(gps.id) {
                ajaxgeneral({
                    url: "action/satellitePortableLibrary/findById?id="+gps.id,
                    success:function(data) {
                        formval(data.data, "form")
                    }
                })
            }
            $(".chooseUser").click(function() {
                top.chooseWeb = {}
                top.dialogP("html/choose/chooseUsersMy.html?multi=1&isReview="+($(".chiefWritingStation").val()&&!gps.id?1:0), window.name, "选择", "chooseUserCB", false, 800, 600)
            })
        });
        function chooseUserCB(data) {
            var ids = '';
            var idss = '';
            for (var i = 0; i < data.data.length; i++){
                ids  += data.data[i].name + ','
            }
            idss = ids.substring(0, ids.length - 1)
            $(".chiefWritingStation").val(idss)
        }


        window.getchoosedata = function() {
            if(formValidate("form")) {
                return { data: getFormValue("form"), state: 1 }
            }
        }
    </script>
</head>
<body class="body_page">
<form id="form">
    <table border="0" cellpadding="0" cellspacing="10" width="100%">
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>地市：</td>
            <td>
                <input class="cities easyui-combobox" name="cities" style="width: 100%; height: 32px;" required="required"
                       data-options="
                       valueField: 'value',
                       ischooseall:true,
                       editable:false,
                       textField: 'name',
                       queryParams:{'dictType':'application_type'},
                       url: web.rootdir+'action/queryDictValue/queryByType'"/>
            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>设备厂家：</td>
            <td>
                <input id="equipmentManufacturer" name="equipmentManufacturer" class="easyui-validatebox equipmentManufacturer" required="required" type="text" value="" />
                <input id="id" name="id" type="hidden" value="" />
            </td>
        </tr>

        <tr>
            <td width="110" align="right"><font class="col_r">*</font>卫星便携站配置类型：</td>
            <td>
                <input id="carConfiguRation" name="carConfiguRation" class="easyui-validatebox" required="required" type="text" value="" />
            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>卫星便携站编号：</td>
            <td>
                <input id="licensePlate" name="licensePlate" class="easyui-validatebox" required="required" type="text" value="" />
            </td>
        </tr>




        <tr>
            <td width="110" align="right"><font class="col_r">*</font>卫星便携站负责人：</td>
            <td>
                <input name="chiefWritingStation" type="text" value="" style="width: 85%;" readonly="readonly" required="required"class="easyui-validatebox textAndInput_readonly chiefWritingStation" />
                <a class="btn f a_warning chooseUser" style="width:50px;float: right;"><i class=" iconfont"></i></a>
            </td>
        </tr>
        <tr>
            <td width="110" align="right"><font class="col_r">*</font>负责人手机号：</td>
            <td>
                <input id="uavStationLeaderPhone" class="easyui-validatebox" name="uavStationLeaderPhone" type="text" value=""  required="required" />
            </td>
        </tr>
    </table>
</form>
</body>
</html>
