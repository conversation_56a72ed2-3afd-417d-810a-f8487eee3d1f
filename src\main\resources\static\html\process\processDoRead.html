
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>已阅列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        $(function(){

            function processDoRead(appName,path,fields){
                var pageparam={
                    "listtable":{
                        "listname":"#doReadTable",
                        "querycmd":"action/queryActBusinessStatus/queryMyRead?source=PC",
                        "nowrap": true,
                        "styleClass":"noScroll",
                        "frozenColumns":[],
                        "columns":[[
                            { title: "任务标题", field: "receiptTitle", width: 200, rowspan: 1,
                                formatter: function (value, row,index) {
                                    var th=appNameTH(appName,path,row.pmInsType);
                                    var g="<span class='audit col_b titleTooltipA' index='"+index+"' ptitle='"+th.type+"' path='"+th.html+"'>"+value+"</span>";
                                    return g;
                                }
                            },
                            { title: "创建部门", field: "createOrgName", width: 200,tooltip:true},
                            { title: "创建人", field: "createUserName", width: 100 },
                            { title: "提交时间", field: "createTime", width: 150 },
                            { title: "已处理人", field: "previousAssistantName", width: 100 },
                            { title: "处理时间", field: "previousAssistantDate", width: 100},
                            { title: "当前办理环节", field: "activityInstName", width: 150,tooltip:true}
                        ] ]
                    }
                };
                if(fields){
                    fields=fields.split(",");
                    for(var i in fields){
                        var fie=fields[i].split("-");
                        if(fie[0]=="pmInsType"){
                            pageparam.listtable.columns[0].push({ "title": fie[1], "field": fie[0], "width": 100,"formatter":function(value,row,index){
                                    return appNameTH(appName,path,row.pmInsType).type;
                                } });
                        }else{
                            pageparam.listtable.columns[0].push({ "title": fie[1], "field": fie[0], "width": 100 });
                        }
                    }
                }
                pageparam.listtable.columns[0].push({
                    field: "opt", title: "操作", width: 100, rowspan: 1,
                    formatter: function (value, row,index) {
                        var th=appNameTH(appName,path,row.pmInsType);
                        var g="<a class='audit' index='"+index+"' title='查看' ptitle='"+th.type+"' path='"+th.html+"'>【查看】</a>";
                        return g;
                    }
                });
                loadGrid(pageparam);
                $(document).on("click","a.audit,span.audit",function(){
                    var $t=$(this);
                    var index=$t.attr("index");
                    $("#doReadTable").datagrid("clearSelections");
                    $("#doReadTable").datagrid("selectRow", index);
                    var row = $("#doReadTable").datagrid("getSelected");
                    var url=$t.attr("path")+"?type=doRead&location="+row.NEXTACTIVITYDEFID+"&processInstId="+row.processInstId+"&processDefName="+row.processDefName+"&notificationId="+row.notificationId+"&currentState="+row.currentState+"&pmInsId="+row.receiptCode;
                    var ptitle=$t.attr("ptitle")+"-"+row.receiptTitle+"-已阅查看";
                    top.dialogP(url,'processDoRead',ptitle,'audit',true,"maximized","maximized",doReadListLoad);
                });
            };

            processDoRead(web.appName,web.appHtml,web.procesType);
        });
    </script>
</head>
<body class="body_page">
<form id="doReadTableQueryForm">
    <table border="0" cellpadding="0" cellspacing="6" width="100%">
        <tr>
            <td width="90" align="right">任务标题：</td>
            <td width="150">
                <input name="title" type="text" value="" />
            </td>
            <td>
                <div class="w100">
                    <a class="btn fl searchtable"><font>查询</font></a>
                </div>
            </td>
        </tr>
    </table>
</form>
<div class="doReadTable"><table id="doReadTable"></table></div>
</body>
</html>
