package com.simbest.boot.yjtxc.apply.repository;

import com.simbest.boot.base.repository.LogicRepository;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:35
 * @describe 日常应急-地市内调动
 */
public interface ApplicationFormRepository extends LogicRepository<ApplicationForm, String> {


    @Query(value = " select af.* from US_APPLICATION_FORM af  where af.enabled=1 and  af.pm_ins_id=:pmInsId", nativeQuery = true)
    ApplicationForm getFormDetailByPmInsId(@Param("pmInsId") String pmInsId);


    @Query(value = " select af.* from US_APPLICATION_FORM af  where af.enabled=1 and  af.pm_ins_id=:pmInsId and  af.name_event like concat(concat('%', :nameEvent), '%') and  af.license_plate like concat(concat('%', :licensePlate), '%')", nativeQuery = true)
    ApplicationForm findAllFormDetailByPmInsId(@Param("pmInsId") String pmInsId ,@Param("nameEvent")String nameEvent,@Param("licensePlate") String licensePlate);


    @Query(value = "select count(1) from US_APPLICATION_FORM t where t.process_type=:processType", nativeQuery = true)
    int findByDayCount(@Param("processType") String processType);

    @Query(value = "select distinct t.receipt_code from WF_WORKITEM_MODEL t where t.process_inst_id=:processInstId and t.enabled='1' ", nativeQuery = true)
    String findByWorkItemProcessInstId(@Param("processInstId") String processInstId);


    @Query(value = "select t.* from WF_WORKITEM_MODEL t where t.receipt_code= :pmInsId and t.enabled='1' and t.current_state=:currentState ", nativeQuery = true)
    List<Map<String,Object>> findWfByPmInsIdCurrentState(@Param("pmInsId") String pmInsId,@Param("currentState") String currentState);

    @Query(value = "select t.* from US_APPLICATION_FORM t where t.pm_ins_id=:pmInsId and t.enabled='1' ", nativeQuery = true)
    ApplicationForm findByPmIndId(@Param("pmInsId") String pmInsId);

    @Query(value = "select t.* from US_APPLICATION_FORM t where t.pm_ins_id=:pmInsId and t.enabled='1' ", nativeQuery = true)
    ApplicationForm findByapplicationForm(@Param("pmInsId") String pmInsId);

    @Query(value = "select t.* from US_APPLICATION_FORM t where t.order_number=:eeid and t.enabled='1' ", nativeQuery = true)
    ApplicationForm findByapplicationFormByEeId(@Param("eeid") String eeid);

    @Query(value = "select t.user_name as userName from US_PERSONNEL_MAINTAIN t where t.company_name_code=:belongCompanyCode ", nativeQuery = true)
    List<String> findByCopyPeople(@Param("belongCompanyCode") String belongCompanyCode);

    @Query(value = "select t.participant from WF_WORKITEM_MODEL t  WHERE t.receipt_code = :receiptcode ", nativeQuery = true)
    List<String> findByCopyPeopleCode(@Param("receiptcode") String receiptcode);


    @Query(value = "select ww.activity_def_id    as workItemName," +
            "                   ww.work_item_id       as workItemId," +
            "                   ww.activity_inst_name as activityInstName," +
            "                   ww.created_time       as createdTime," +
            "                   ww.parti_name         as partiName," +
            "                   ww.participant        as participant" +
            "              from WF_WORKITEM_MODEL ww" +
            "             where ww.work_item_id =" +
            "                   (select max(t.work_item_id)" +
            "                      from WF_WORKITEM_MODEL t" +
            "                     where t.receipt_code=:receiptCode)", nativeQuery = true)
    Map<String, Object> findByWorkItemName(@Param("receiptCode") String receiptCode);


    @Query(value = "select ww.activity_def_id    as workItemName," +
            "                   ww.work_item_id       as workItemId," +
            "                   ww.activity_inst_name as activityInstName," +
            "                   ww.created_time       as createdTime," +
            "                   ww.parti_name         as partiName," +
            "                   ww.participant        as participant" +
            "              from WF_WORKITEM_MODEL ww" +
            "             where ww.work_item_id =" +
            "                   (select max(t.work_item_id)" +
            "                      from WF_WORKITEM_MODEL t" +
            "                     where t.process_inst_id=:processInsId)", nativeQuery = true)
    Map<String, Object> findByWorkItemNameProcessInsId(@Param("processInsId") String processInsId);

    @Query(value = "select ww.activity_def_id    as workItemName," +
            "       ww.work_item_id       as workItemId," +
            "       ww.activity_inst_name as activityInstName," +
            "       ww.created_time       as createdTime," +
            "       ww.parti_name         as partiName," +
            "       ww.participant        as participant from WF_WORKITEM_MODEL ww where ww.enabled='1' and ww.work_item_id=:workitemId", nativeQuery = true)
    Map<String, Object> findByWorkItemNameWorkitemId(@Param("workitemId") String workitemId);


    @Modifying
    @Query(
            value = " update US_APPLICATION_FORM set enabled=0 where id =:id ",
            nativeQuery = true
    )
    void deleteByFromId(String id);


    @Query(
            value = "select act.business_key,act.receipt_title,act.process_inst_id,wf.work_item_id" +
                    "  from ACT_BUSINESS_STATUS act," +
                    "       (SELECT wm.process_inst_id, wm.work_item_id" +
                    "          FROM WF_WORKITEM_MODEL wm" +
                    "         WHERE wm.work_item_id in" +
                    "               (select max(t.work_item_id)" +
                    "                  from WF_WORKITEM_MODEL t" +
                    "                 WHERE t.process_inst_id =?1" +
                    "                   and t.enabled = '1')) wf WHERE act.process_inst_id=wf.process_inst_id",
            nativeQuery = true
    )
    Map<String, Object> findByActVusiness(Long processInstId);

    @Query(value = "select t.process_type from US_APPLICATION_FORM t WHERE  t.pm_ins_id=?1 and t.enabled='1' ", nativeQuery = true)
    String findByApplicationForm(String receiptCode);

    @Query(value = "select t.username from uums.v_user_org_position t   WHERE  t.belongCompanyCode  = ?1  and   t.orgCode in ('4772167799548513930','4772421137382209712','4772432670559819061','4772142106514648158','4772245735097588535','4772181010611174827','4772303214116027364'\n" +
            ",'4772326548543528586','4772367415604714838','4772379136054520283','4772442243146862760','4772257473019199155','4772202533031687338'," +
            "'4772361849480304045','4772285121205615416','4772229942051546798','4772405259043562720','4772391923214816474')  and  t.companyTypeDict = '02' and t.position_code = '5'   ",
            nativeQuery = true)
    String finBwangLuoBuJingli(String belongCompanyCode);




    @Query(value = " select form.*\n" +
            "  from us_application_form form\n" +
            " where form.enabled = 1\n" +
            "   and form.pm_ins_id in\n" +
            "       (select distinct t.receipt_code\n" +
            "          from WF_WORKITEM_MODEL t\n" +
            "         where t.enabled = 1\n" +
            "           and t.activity_def_id in\n" +
            "               ('yjtxc.coordinatingDispatching', 'yjtxc.emergencyVehicles')\n" +
            "           and t.current_state = '10')\n" +
            "   and to_date(form.guarantee_end_time, 'yyyy-MM-dd HH24:mi:ss') <= to_date(:dateStr, 'yyyy-MM-dd HH24:mi:ss')\n", nativeQuery = true)
    List<ApplicationForm> getspecial01(@Param("dateStr")String dateStr);


    @Query(value = " select t.*\n" +
            "  from WF_WORKITEM_MODEL t\n" +
            " where t.enabled = 1\n" +
            "   and t.activity_def_id in\n" +
            "       ('yjtxc.coordinatingDispatching', 'yjtxc.emergencyVehicles')\n" +
            "   and t.current_state = '10'\n" +
            "   and t.receipt_code = :pmInsId ", nativeQuery = true)
    List<Map<String,Object>> getspecial02(@Param("pmInsId")String pmInsId);
}
