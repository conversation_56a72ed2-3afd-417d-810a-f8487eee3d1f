package com.simbest.boot.yjtxc.msg.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.GenericService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.cmcc.nmsg.model.Content;
import com.simbest.boot.util.security.LoginUtils;
import com.simbest.boot.yjtxc.msg.service.IShortMessageService;
import com.simbest.boot.yjtxc.util.Constants;
import com.simbest.boot.yjtxc.util.OperateLogTool;
import com.simbest.boot.yjtxc.util.SMSTool;
import com.simbest.boot.cmcc.nmsg.MsgPostOperatorService;
import com.simbest.boot.security.SimpleApp;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.uums.api.app.UumsSysAppApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @description:
 * @author: jingwenhao
 * @date: $date$
 */
@Slf4j
@Service
public class ShortMessageServiceImpl extends GenericService<ActBusinessStatus, Long> implements IShortMessageService {


    @Autowired
    private UumsSysAppApi uumsSysAppApi;

    @Autowired
    private LoginUtils loginUtils;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private SMSTool smsTool;

    @Autowired
    private MsgPostOperatorService msgPostOperatorService;


    String param1 = "/action/smsurged";

    /**
     * 待办短信催办
     *
     * @param list 待办列表
     * @return
     */
    @Override
    public JsonResponse sendShortMessage(String source, String currentUserCode, List<Map<String, Object>> list) {
        Boolean isPostMsg = false;   //短信开关 false 短信不发送 true 发送
        Boolean isOk = false;//记录是否发送成功
        int faCount = 0;//失败记录
        String strName = "";
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/sendShortMessage";
        String params = "source=" + source + ",currentUserCode" + currentUserCode + ",list" + list.toString();
        operateLog.setInterfaceParam(params);
        try {
            operateLogTool.userLogin(source, currentUserCode);
            /**判断是否是从手机端 true是，false否**/
            JsonResponse returnObj = operationSource(source, currentUserCode);
            if (returnObj != null) {
                return returnObj;
            }
            /**设置当前要发送短信的人**/
            //String fromUserCode = "";
            //String fromUserName = "";
//            if ("PC".equals(source)) {
//                fromUserCode = SecurityUtils.getCurrentUserName();
//                fromUserName = SecurityUtils.getCurrentUser().getTruename();
//            } else {
//                fromUserCode = currentUserCode;
//                fromUserName = uumsSysUserinfoApi.findByKey(currentUserCode, IAuthService.KeyType.username, Constants.APP_CODE).getTruename();
//            }
            /**查询发送短信开关是否开始**/
            SimpleApp simpleApp = uumsSysAppApi.findAppByAppCode(Constants.APP_CODE, currentUserCode);
            if (simpleApp != null) {
                isPostMsg = simpleApp.getIsSendMsg();
            }
            /**如果开关开启且待发列表不为空则发短信**/
//            if (isPostMsg && CollectionUtil.isNotEmpty(list)) {
            if (CollectionUtil.isNotEmpty(list)) {
                /**准备发送的参数**/
                for (Map<String, Object> map : list) {
                    String sendName = map.get("PARTI_NAME") != null ? map.get("PARTI_NAME").toString() : "";
                    isOk = this.sendSMS(map);
                    /**发送失败记录**/
                    if (!isOk) {
                        faCount++;
                        strName = sendName + ",";
                    }
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            /**操作日志记录**/
            //operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(isOk, StringUtils.isEmpty(strName) ? "发送成功" : strName + "等" + faCount + "人发送失败");
    }

    /**
     * 发送短信
     *
     * @param map 准备发送参数
     * @return
     */
    public Boolean sendSMS(Map<String, Object> map) {
        Boolean isPostMsgOK = false; //是否发送成功标志

        /**发送短信操作**/
        try {
            /**参数不为空情况下**/
            if (map != null && map.size() > 0) {
                String sendUser = map.get("PARTI_NAME") != null ? map.get("PARTI_NAME").toString() : "";
                String itemSubject = map.get("TITLE") != null ? map.get("TITLE").toString() : "";
                String title = MapUtil.getStr(map, "TITLE");
                String msg = "督办派单：关于" + title + "的督办事项，请尽快登录督办系统处理";
                /**准备参数**/
                Map<String, Object> mapParam = Maps.newHashMap();

                mapParam.put("msg", msg);
                mapParam.put("sendUser", sendUser);
                Content content = new Content();
                content.setUsername(sendUser);
                Set<Content> contents = CollectionUtil.newHashSet();
                contents.add(content);
                mapParam.put("contents", contents);
                isPostMsgOK = msgPostOperatorService.postMsg(smsTool.readyParams(mapParam));
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return isPostMsgOK;
    }

    /**
     * 判断手机端来源
     *
     * @param source   来源
     * @param userCode 用户账户
     * @return
     */
    public JsonResponse operationSource(String source, String userCode) {
        JsonResponse jsonResponse = null;
        /**判断是否是从手机端还是PC端记录操作日志**/
        try {
            if (Constants.MOBILE.equals(source)) {
                if (StringUtils.isNotEmpty(userCode)) {
                    loginUtils.manualLogin(userCode, Constants.APP_CODE);
                } else {
                    jsonResponse = JsonResponse.fail("OA账户不能为空!");
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return jsonResponse;
    }
}
