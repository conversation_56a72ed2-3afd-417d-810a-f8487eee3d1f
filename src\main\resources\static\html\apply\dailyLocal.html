<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">

<head>
	<title>日常应急-地市内调动</title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
	<link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}"
		rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/public.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet" />
	<link href="http://************:8088/simbestui/css/pages.css?v=svn.revision"
		th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet" />
	<script src="../../js/jquery.config.js?v=svn.revision" th:src="@{/js/jquery.config.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}"
		type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
	<script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision"
		th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
	<style>
		/* body{
			height: 100%;
			overflow-y: auto;
			overflow-x: hidden;
			background-color: #f5f5f5;
		} */
		.splite {
			border-left: 3px solid rgb(57, 174, 245);
			padding-left: 15px;
			font-size: 16px;
			font-weight: 600;
		}

		.myTitle {
			text-align: center;
			font-size: 20px;
			position: relative;
		}

		a.textbox-icon .combo-arrow{
			height: 15px !important;
		}
		.textbox.combo.datebox{
			height: 30px !important;
		}
	</style>
	<script type="text/javascript">
		getCurrent()
		var returnType = ""
		var returnData = []
		var carTableA = [];
		var carTableB = [];
		var carTableC = [];
		var carTableD = [];

		var startTime=''
		var endTime=''
		// var myEditorDate = {
		// 	type: 'datebox',
		// 	options: {
		// 		onShowPanel : function(){
		// 			$(this).datebox('calendar').calendar({
		// 				validator :function(date){
		// 					var date1 = new Date(new Date(startTime)-(1000*60*60*24));
		// 					var date2 = new Date(endTime);
		// 					return (date1<=date && date<=date2);
		// 				}
		// 			});
		// 		}
		// 	}
		// };
		var myEditorDate = {
			type: 'datetimebox',
			options: {
				showSeconds: false,
				onShowPanel: function(){
					var startDate = new Date(new Date(startTime)-(1000*60*60*24)); // 开始日期时间
					var endDate = new Date(endTime); // 结束日期时间
					$(this).datetimebox('calendar').calendar({
						validator: function(date){
						return (startDate <= date && date <= endDate);
						}
					});
				}
			}
		};

		var currentPhone=web.currentUser.preferredMobile
		function carCols() {
			return [
				{ title: "调用应急车地市", field: "cities", width: 150, align: "center" },
				{ title: "调用应急车车牌号", field: "licensePlate", width: 150, align: "center" },
				{ title: "负责开站人员", field: "theDriver", width: 150, align: "center" },
				{ title: "开站人员手机号", field: "theDriverPhone", width: 150, align: "center" }
			]
		}
		function uavCols(param) {
			return [
				{ title: "地市", field: "cities", width: 150, align: "center" },
				{ title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center" },
				{ title: param.title, field: param.field, width: 150, align: "center" },
				{ title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center" }
			]
		}
		function loadTable(cols) {
			$(".carTable").empty()
			$(".carTable").append($("<table>").attr("id", "carTable"))
			if (gps.location == "yjtxc.coordinatingDispatching") {
				cols.push({
					title: "使用开始时间", field: "startTime", width: 150, align: "center",
					formatter: function (value, row, index) {
						if (gps.type == "task" && gps.location == "yjtxc.coordinatingDispatching") {
							var phoneType = row.theDriverPhone ? row.theDriverPhone : row.uavStationLeaderPhone
							if (phoneType && phoneType.split(",")[0] == web.currentUser.preferredMobile) {
								return '<input type="text" index="' + index + '" field="startTime" class="easyui-datebox start' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.startTime + '" style="width:100%;height:32px;"/>'
							}
						}
						return value
					}
				})
				cols.push({
					title: "使用结束时间", field: "endTime", width: 150, align: "center",
					formatter: function (value, row, index) {
						if (gps.type == "task" && gps.location == "yjtxc.coordinatingDispatching") {
							var phoneType = row.theDriverPhone ? row.theDriverPhone : row.uavStationLeaderPhone
							if (phoneType && phoneType.split(",")[0] == web.currentUser.preferredMobile) {
								return '<input type="text" index="' + index + '" field="endTime" class="easyui-datebox end' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.endTime + '" style="width:100%;height:32px;"/>'
							}
						}
						return value
					}
				})
			}
			if (!gps.location || gps.location == "yjtxc.start") {
				cols.push({
					title: "操作", field: "opt", width: 50, align: "center",
					formatter: function (value, row, index) {
						return "<a href='#' index='" + index + "' data-type='" + row.carConfiguRation + "' onclick='deleteInfo(this)'>删除</a>"
					}
				})
			}
			loadGrid({
				"listtable": {
					"listname": "#carTable",
					"data": { data: { rows: rows, total: rows.length } },
					"nowrap": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [cols],
					"onLoadSuccess": function () {
						var startHorizon = $("#guaranteeStartTime").datebox("getValue")
						var endHorizon = $("#guaranteeEndTime").datebox("getValue")
						setTimeout(function () {
							dateHorizon("easyuidate", startHorizon, endHorizon)
						}, 1500)
						$(".easyuidate").datebox({
							onSelect: function (data) {
								var row = rows[$(this).attr("index")]
								row[$(this).attr("field")] = getTimeDate(data, "yyyy-MM-dd")
								if ($(this).attr("field") == "startTime") {
									dateHorizon("end" + $(this).attr("index"), getTimeDate(data, "yyyy-MM-dd"), endHorizon)
								} else {
									dateHorizon("start" + $(this).attr("index"), startHorizon, getTimeDate(data, "yyyy-MM-dd"))
								}
							}
						})
						$("#carTable").datagrid("resize")
					}
				}
			})
		}
		var rows = []
		function initProcess() {
			var param = {
				"htmlName": window.name,
				"formId": "dailyLocalForm",
				"processNextLoading":true,
				"processNextCmd": "action/applicationForm/startSubmitProcess",
				"processDeleteCmd": "action/applicationForm/deleteProcess",
				"processDraftDeleteCmd": "action/applicationForm/deleteDraft"
			};
			loadProcess(param);
			if (gps.type == "workOrder") {
				$(".pageInfo").hide()
				$(".body_page").css("padding-top", 0)
			}
			if (!gps.location) {
				$(".userName").val(web.currentUser.truename)
				$(".trueName").val(web.currentUser.username)
				$(".applicantUnit").val(web.currentUser.belongCompanyName)
				$(".applicantPhone").val(web.currentUser.preferredMobile)
			}
			if (!gps.location || gps.location == "yjtxc.start") {
				$(".guaranteeStartTime").datebox({
					onSelect: function (data) {
						var date = new Date();
						var month = (date.getMonth() + 1) > 9 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
						var day = date.getDate() > 9 ? date.getDate() : '0' + date.getDate();
						var today = date.getFullYear() + '' + month + '' + day;

						var dataMonth = (data.getMonth() + 1) > 9 ? data.getMonth() + 1 : '0' + (data.getMonth() + 1);
						var dataDay = data.getDate() > 9 ? data.getDate() : '0' + data.getDate();
						var dataDate = date.getFullYear() + '' + dataMonth + '' + dataDay;

						/*if (Number(dataDate) < Number(today)) {
							$('.myTitle').text('日常应急补收调度申请表')
						} else {
							$('.myTitle').text('日常应急调度申请表')
						}*/
						dateHorizon("guaranteeEndTime", getTimeDate(data, "yyyy-MM-dd"))
						rows = []
						$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
					}
				})

				$(".guaranteeEndTime").datebox({
					onSelect: function (data) {
						dateHorizon("guaranteeStartTime", "0001-01-01", getTimeDate(data, "yyyy-MM-dd"))
						rows = []
						$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
					}
				})

				$(".chooseCar").click(function () {
					var type = $(".deviceConfiguration").combobox("getValue")
					if (!type) {
						return top.mesShow("温馨提示！", "请选择 申请应急设备配置 !", 2000, "red")
					}
					var startTime = $("#guaranteeStartTime").datebox("getValue")
					if (!startTime) {
						return top.mesShow("温馨提示！", "请选择 保障开始时间 !", 2000, "red")
					}
					var endTime = $("#guaranteeEndTime").datebox("getValue")
					if (!endTime) {
						return top.mesShow("温馨提示！", "请选择 保障结束时间 !", 2000, "red")
					}
					var ids = []
					for (var i in rows) {
						if (!gps.location) {
							ids.push(rows[i].id)
						} else {
							ids.push(rows[i].carId)
						}
					}
					top.dialogP("html/apply/chooseCar.html?processType=A&ids=" + ids.join("/") +
						"&city=" + encodeURI(web.currentUser.belongCompanyName) +
						"&startTime=" + startTime +
						"&endTime=" + endTime +
						"&pmInsId=" + $("#pmInsId").val() +
						"&type=" + encodeURI(type),
						window.name, "选择应急设备", "chooseCarCB", false, 1100, 700)
				})
			}
		}
		$(function () {
			initProcess()
			if (!gps.location) loadCarTable({ showEmpty: true, edit: true })
			$(document).on('click', '.addBtn', function () {
				var type = $(this).attr("data-type")
				var startTime = $("#guaranteeStartTime").datebox("getValue")
				if (!startTime) {
					return top.mesShow("温馨提示！", "请选择 保障开始时间 !", 2000, "red")
				}
				var endTime = $("#guaranteeEndTime").datebox("getValue")
				if (!endTime) {
					return top.mesShow("温馨提示！", "请选择 保障结束时间 !", 2000, "red")
				}
				var ids = []
				var rows = []
				if (type == "大型应急车") {
					rows = carTableA
				} else if (type == "卫星车") {
					rows = carTableB
				} else if (type == "无人机高空站") {
					rows = carTableC
				} else if (type == "卫星便携站") {
					rows = carTableD
				}
				for (var i in rows) {
					ids.push(rows[i].id || rows[i].carId)
				}
				top.dialogP("html/apply/chooseCar.html?processType=A&ids=" + ids.join("/") +
					"&city=" + encodeURI(web.currentUser.belongCompanyName) +
					"&startTime=" + startTime +
					"&endTime=" + endTime +
					"&pmInsId=" + $("#pmInsId").val() +
					"&type=" + encodeURI(type),
					window.name, "选择应急设备", "chooseCar", false, 1100, 700)
			});


			//时间发生变化时 给下面表格置空
			$('#guaranteeStartTime').datebox({
				onSelect: function(date){
					carTableA = [];
					carTableB = [];
					carTableC = [];
					carTableD = [];
					loadCarTable({ showEmpty: true, edit: true})
				}
			});
			$('#guaranteeEndTime').datebox({
				onSelect: function(date){
					carTableA = [];
					carTableB = [];
					carTableC = [];
					carTableD = [];
					loadCarTable({ showEmpty: true, edit: true})
				}
			});

			// 控制保障时间只显示时分
			$('#guaranteeEndTime').datetimebox({
			  showSeconds: false,
			  showMillis: false
			});
			$('#guaranteeStartTime').datetimebox({
			  showSeconds: false,
			  showMillis: false
			});

			// 动态加载申请应急设备配置选项
			loadDeviceConfigurationOptions();
		});

		// 动态加载申请应急设备配置选项
		function loadDeviceConfigurationOptions() {
			ajaxgeneral({
				url: "action/garageConfiguration/getCarConfiguRationParentList",
				type: "POST",
				contentType: "application/json; charset=utf-8",
				success: function(response) {
					try {
						if (response && response.errcode === 0 && response.data) {
							var options = [];

							// 遍历返回的数据数组，构建下拉框选项
							for (var i = 0; i < response.data.length; i++) {
								var item = response.data[i];
								if (item && item.trim() !== '') {
									options.push({value: item, name: item});
								}
							}

							// 更新下拉框数据，不设置默认选中
							$('.deviceConfiguration').combobox('loadData', options);

							console.log('申请应急设备配置选项加载成功');
						} else {
							console.error('获取申请应急设备配置数据失败：', response);
							// 如果接口调用失败，使用默认选项
							loadDefaultDeviceConfigurationOptions();
						}
					} catch (e) {
						console.error('处理申请应急设备配置数据时出错：', e);
						// 如果处理数据时出错，使用默认选项
						loadDefaultDeviceConfigurationOptions();
					}
				},
				error: function(xhr, status, error) {
					console.error('调用申请应急设备配置接口失败：', error);
					// 如果接口调用失败，使用默认选项
					loadDefaultDeviceConfigurationOptions();
				}
			});
		}

		// 加载默认的申请应急设备配置选项（作为备用方案）
		function loadDefaultDeviceConfigurationOptions() {
			var defaultOptions = [
				{value: '大型应急车', name: '大型应急车'},
				{value: '卫星车', name: '卫星车'},
				{value: '无人机高空站', name: '无人机高空站'},
				{value: '卫星便携站', name: '卫星便携站'}
			];

			// 更新下拉框数据，不设置默认选中
			$('.deviceConfiguration').combobox('loadData', defaultOptions);

			console.log('使用默认申请应急设备配置选项');
		}

		function loadCarTable(param) {
			// 表格
			var pageparamA = {
				"listtable": {
					"listname": "#carTableA",
					"data": { data: { rows: carTableA, total: 0 } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [[
						{ title: "调用应急设备地市", field: "cities", width: 150, align: "center" },
						{ title: "应急设备分类", field: "carConfiguRationParent", width: 120, align: "center" },
						{ title: "应急设备子类", field: "carConfiguRation", width: 120, align: "center" },
						{ title: "调用应急设备车牌号", field: "licensePlate", width: 150, align: "center" },
						{ title: "调用应急设备联系人", field: "openingStation", width: 150, align: "center" },
						{ title: "调用应急设备联系电话", field: "openingStationPhone", width: 170, align: "center" }
					]],
					"onClickRow":function (index,rows){
						if(!rows.openingStationPhone || rows.openingStationPhone.indexOf(currentPhone)=="-1"){
							$('#carTableA').datagrid('endEdit', index)
						}
					}
				}
			};

			var pageparamB = {
				"listtable": {
					"listname": "#carTableB",
					"data": { data: { rows: carTableB, total: 0 } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [[
						{ title: "调用应急车地市", field: "cities", width: 150, align: "center" },
						{ title: "调用应急车车牌号", field: "licensePlate", width: 150, align: "center" },
						{ title: "调用应急车联系人", field: "openingStation", width: 150, align: "center" },
						{ title: "调用应急车联系电话", field: "openingStationPhone", width: 150, align: "center" }
					]],
					"onClickRow":function (index,rows){
						if(!rows.openingStationPhone || rows.openingStationPhone.indexOf(currentPhone)=="-1"){
							$('#carTableB').datagrid('endEdit', index)
						}
					}
				}
			};

			var pageparamC = {
				"listtable": {
					"listname": "#carTableC",
					"data": { data: { rows: carTableC, total: 0 } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [[
						{ title: "地市", field: "cities", width: 150, align: "center" },
						{ title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center" },
						{ title: "无人机高空站类型", field: "carConfiguRation", width: 150, align: "center" },
						{ title: "无人机高空站编号", field: "licensePlate", width: 150, align: "center" },
						{ title: "无人机高空基站负责人", field: "uavStationLeader", width: 150, align: "center" },
						{ title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center" }
					]],
					"onClickRow":function (index,rows){
						if(!rows.uavStationLeaderPhone || rows.uavStationLeaderPhone.indexOf(currentPhone)=="-1"){
							$('#carTableC').datagrid('endEdit', index)
						}
					}
				}
			};

			var pageparamD = {
				"listtable": {
					"listname": "#carTableD",
					"data": { data: { rows: carTableD, total: 0 } },
					"nowrap": true,
					"fitColumns": true,
					"styleClass": "noScroll",
					"pagination": false,
					"frozenColumns": [],
					"columns": [[
						{ title: "地市", field: "cities", width: 150, align: "center" },
						{ title: "设备厂家", field: "equipmentManufacturer", width: 150, align: "center" },
						{ title: "卫星便携站类型", field: "carConfiguRation", width: 150, align: "center" },
						{ title: "卫星便携站编号", field: "licensePlate", width: 150, align: "center" },
						{ title: "卫星便携站负责人", field: "chiefWritingStation", width: 150, align: "center" },
						{ title: "负责人手机号", field: "uavStationLeaderPhone", width: 150, align: "center" },
					]],
					"onClickRow":function (index,rows){
						if(!rows.uavStationLeaderPhone || rows.uavStationLeaderPhone.indexOf(currentPhone)=="-1"){
							$('#carTableD').datagrid('endEdit', index)
						}
					}
				}
			};

			if (gps.location == 'yjtxc.coordinatingDispatching') {
				var date2 = { title: "使用结束时间", field: "endTime", width: 150, align: "center", editor: myEditorDate,
					formatter:function(value,row,index){
						if(row.openingStationPhone!=null && row.openingStationPhone.indexOf(currentPhone)!="-1"){
							return '<input type="text" index="' + index + '" field="endTime" class="easyui-datebox end' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.endTime + '" style="width:100%;height:30px;"/>'
						}else if(row.uavStationLeaderPhone!=null && row.uavStationLeaderPhone.indexOf(currentPhone)!="-1"){
							return '<input type="text" index="' + index + '" field="endTime" class=" easyui-datebox end' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.endTime + '" style="width:100%;height:30px;"/>'
						}else{
							return value
						}
					}
				}
				var date1 = {
					title: "使用开始时间", field: "startTime", width: 150, align: "center",editor: myEditorDate,
					formatter: function (value, row, index) {
						if(row.openingStationPhone!=null && row.openingStationPhone.indexOf(currentPhone)!="-1"){
							return '<input type="text" index="' + index + '" field="startTime" class="easyui-datebox start' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.startTime + '" style="width:100%;height:30px;"/>'
						}else if(row.uavStationLeaderPhone!=null && row.uavStationLeaderPhone.indexOf(currentPhone)!="-1"){
							return '<input type="text" index="' + index + '" field="startTime" class="easyui-datebox start' + index + ' easyuidate"\n' +
									'data-options="panelHeight:\'auto\',editable:false" value="' + row.startTime + '" style="width:100%;height:30px;"/>'
						}else{
							return value
						}
					}
				}
				pageparamA.listtable.columns[0].push(date1)
				pageparamA.listtable.columns[0].push(date2)
				pageparamB.listtable.columns[0].push(date1)
				pageparamB.listtable.columns[0].push(date2)
				pageparamC.listtable.columns[0].push(date1)
				pageparamC.listtable.columns[0].push(date2)
				pageparamD.listtable.columns[0].push(date1)
				pageparamD.listtable.columns[0].push(date2)
			}
			if (param.edit) {
				var opt = {
					title: "操作", field: "opt", width: 50, align: "center",
					formatter: function (value, row, index) {
						return "<a href='#' index='" + index + "' data-type='" + row.carConfiguRation + "' onclick='deleteInfo(this)'>删除</a>"
					}
				}
				pageparamA.listtable.columns[0].push(opt)
				pageparamB.listtable.columns[0].push(opt)
				pageparamC.listtable.columns[0].push(opt)
				pageparamD.listtable.columns[0].push(opt)
				// pageparamA.listtable.columns[0].splice(-1, 1);
				// pageparamB.listtable.columns[0].splice(-1, 1);
				// pageparamC.listtable.columns[0].splice(-1, 1);
				// pageparamD.listtable.columns[0].splice(-1, 1);
				$('td .addBtn').show();
			} else {
				$('td .addBtn').hide();
			}


			if (param.showEmpty) {
				loadGrid(pageparamA)
				loadGrid(pageparamB)
				loadGrid(pageparamC)
				loadGrid(pageparamD)
				$("#carTableA,#carTableB,#carTableC,#carTableD").datagrid("resize");
				$("#carTableA,#carTableB,#carTableC,#carTableD").datagrid('enableCellEditing')
			} else {
				if (carTableA.length > 0) {
					loadGrid(pageparamA)
					$("#carTableA").datagrid("resize");
					$("#carTableA").datagrid('enableCellEditing')
				}
				if (carTableB.length > 0) {
					loadGrid(pageparamB)
					$("#carTableB").datagrid("resize");
					$("#carTableB").datagrid('enableCellEditing')
				}
				if (carTableC.length > 0) {
					loadGrid(pageparamC)
					$("#carTableC").datagrid("resize");
					$("#carTableC").datagrid('enableCellEditing')
				}
				if (carTableD.length > 0) {
					loadGrid(pageparamD)
					$("#carTableD").datagrid("resize");
					$("#carTableD").datagrid('enableCellEditing')
				}
			}

			if(gps.type=='task' && gps.location == 'yjtxc.start'){
				$('td .addBtn').show();
				var opt = {
					title: "操作", field: "opt", width: 50, align: "center",
					formatter: function (value, row, index) {
						return "<a href='#' index='" + index + "' data-type='" + row.carConfiguRation + "' onclick='deleteInfo(this)'>删除</a>"
					}
				}
				pageparamA.listtable.columns[0].push(opt)
				pageparamB.listtable.columns[0].push(opt)
				pageparamC.listtable.columns[0].push(opt)
				pageparamD.listtable.columns[0].push(opt)

				$('td .addBtn').show();
				$('#carTableA').show()
				$('#carTableB').show()
				$('#carTableC').show()
				$('#carTableD').show()

				loadGrid(pageparamA)
				loadGrid(pageparamB)
				loadGrid(pageparamC)
				loadGrid(pageparamD)
				$("#carTableA").datagrid("resize");
				$("#carTableB").datagrid("resize");
				$("#carTableC").datagrid("resize");
				$("#carTableD").datagrid("resize");
			
			}
		}

		// 选择应急车辆回显
		function chooseCar(data) {
			var tableID = "";
			var carTable = [];
			if (data.type == "大型应急车") {
				tableID = "#carTableA"
				for (var i in data.data) {
					if (includes(carTableA, data.data[i].id)) {
						carTableA.push(data.data[i])
					}
				}
				carTable = carTableA
			} else if (data.type == "卫星车") {
				tableID = "#carTableB"
				for (var i in data.data) {
					if (includes(carTableB, data.data[i].id)) {
						carTableB.push(data.data[i])
					}
				}
				carTable = carTableB
			} else if (data.type == "无人机高空站") {
				tableID = "#carTableC"
				for (var i in data.data) {
					if (includes(carTableC, data.data[i].id)) {
						carTableC.push(data.data[i])
					}
				}
				carTable = carTableC
			} else if (data.type == "卫星便携站") {
				tableID = "#carTableD"
				for (var i in data.data) {
					if (includes(carTableD, data.data[i].id)) {
						carTableD.push(data.data[i])
					}
				}
				carTable = carTableD
			}

			$(tableID).datagrid("loadData", { data: { rows: carTable, total: carTable.length } })
		}

		// 删除应急车辆
		function deleteInfo(v) {
			var index = $(v).attr("index");
			var type = $(v).attr("data-type");
			var tableID = "";
			var carTable = [];
			if (type == "大型应急车") {
				tableID = "#carTableA"
				carTableA.splice(index, 1)
				carTable = carTableA
			} else if (type == "卫星车") {
				tableID = "#carTableB"
				carTableB.splice(index, 1)
				carTable = carTableB
			} else if (type == "无人机高空站") {
				tableID = "#carTableC"
				carTableC.splice(index, 1)
				carTable = carTableC
			} else if (type == "卫星便携站") {
				tableID = "#carTableD"
				carTableD.splice(index, 1)
				carTable = carTableD
			}
			$(tableID).datagrid("loadData", { data: { rows: carTable, total: carTable.length } })
		}

		function getcallback(data) {
			startTime=data.guaranteeStartTime
			endTime=data.guaranteeEndTime
			rows = data.equipmentList
			//设置不可编辑
			if (gps.location == "yjtxc.coordinatingDispatching") {
				for (var i in rows) {
					if (gps.type == "task" && (rows[i].theDriverPhone == web.currentUser.preferredMobile || rows[i].uavStationLeaderPhone == web.currentUser.preferredMobile)) {
						rows[i].startTime = data.guaranteeStartTime
						rows[i].endTime = data.guaranteeEndTime

					}
				}
			}

			// debugger
				$('#guaranteeStartTime').datebox('readonly',true)
				$('#guaranteeEndTime').datebox('readonly',true)

			// if (data.equipmentList) {
			// 	carTableA = data.equipmentList;
			// 	$("#carTableA").datagrid("loadData", {
			// 		data: { rows: data.equipmentList, total: data.equipmentList.length }
			// 	})
			// } else {
			// 	$('.carTableA').hide();
			// }
			// if (data.equipmentMoonList) {
			// 	carTableB = data.equipmentMoonList;
			// 	$("#carTableB").datagrid("loadData", {
			// 		data: { rows: data.equipmentMoonList, total: data.equipmentMoonList.length }
			// 	})
			// } else {
			// 	$('.carTableB').hide();
			// }
			// if (data.equipmentUavList) {
			// 	carTableC = data.equipmentUavList;
			// 	$("#carTableC").datagrid("loadData", {
			// 		data: { rows: data.equipmentUavList, total: data.equipmentUavList.length }
			// 	})
			// } else {
			// 	$('.carTableC').hide();
			// }
			// if (data.equipmentBleList) {
			// 	carTableD = data.equipmentBleList;
			// 	$("#carTableD").datagrid("loadData", {
			// 		data: { rows: data.equipmentBleList, total: data.equipmentBleList.length }
			// 	})
			// } else {
			// 	$('.carTableD').hide();
			// }
			carTableA = data.equipmentList ? data.equipmentList : [];
			carTableB = data.equipmentMoonList ? data.equipmentMoonList : [];
			carTableC = data.equipmentUavList ? data.equipmentUavList : [];
			carTableD = data.equipmentBleList ? data.equipmentBleList : [];
			loadCarTable({ edit: false })
			if (!data.equipmentList && !data.equipmentMoonList && !data.equipmentUavList && !data.equipmentBleList) {
				$('.chooseCarHide').hide();
			}
			// $("#carTableA,#carTableB,#carTableC,#carTableD").datagrid("resize");

			//不知道是哪里的提示
			if(gps.type=='task'||gps.type=='join'){
				$('.tooltip').css('display','none')
			}
		}

		function nextBtnOther() {
			var phoneTmp = [];
			var belongCompanyCodeTmp = [];

			for (var i in carTableA) {
				phoneTmp.push(carTableA[i].theDriverPhone?carTableA[i].theDriverPhone.split(",")[0]+','+carTableA[i].openingStationPhone.split(",")[0]:carTableA[i].openingStationPhone.split(",")[0])
				belongCompanyCodeTmp.push(carTableA[i].citiesCode)
			}


			for (var i in carTableB) {
				if (carTableB[i].theDriverPhone) {
					phoneTmp.push(carTableB[i].theDriverPhone.split(",")[0])
				}
				belongCompanyCodeTmp.push(carTableB[i].citiesCode)
			}


			for (var i in carTableC) {
				if (carTableC[i].uavStationLeaderPhone) {
					phoneTmp.push(carTableC[i].uavStationLeaderPhone.split(",")[0])
				}
				belongCompanyCodeTmp.push(carTableC[i].citiesCode)
			}

			for (var i in carTableD) {
				if (carTableD[i].uavStationLeaderPhone) {
					phoneTmp.push(carTableD[i].uavStationLeaderPhone.split(",")[0])
				}
				belongCompanyCodeTmp.push(carTableD[i].citiesCode)
			}
			var phone=[]
			for(var i=0;i<phoneTmp.length;i++) {
				var items1 = phoneTmp[i];
				if($.inArray(items1,phone)===-1) {
					phone.push(items1);
				}
			}

			var belongCompanyCode=[]
			for(var i=0;i<belongCompanyCodeTmp.length;i++) {
				var items=belongCompanyCodeTmp[i];
				if($.inArray(items ,belongCompanyCode)===-1) {
					belongCompanyCode.push(items);
				}
			}
			return {
				phone: phone.join(","),
				belongCompanyCode : belongCompanyCode.join(","),
				applyType: "A"
			}
		}

		function onSubmit(data) {
			if (carTableA.length + carTableB.length + carTableC.length + carTableD.length == 0) {
				return top.mesShow("温馨提示！", "请至少添加一个应急设备！", 2000, "red")
			}
			var start = $("#guaranteeStartTime").datebox("getValue")
			var end = $("#guaranteeEndTime").datebox("getValue")
			var days = (new Date(end).getTime() - new Date(start).getTime()) / 1000 / 60 / 60 / 24
			if (days >= 3 && data.attachmentList.length == 0) {
				return top.mesShow("温馨提示！", "调度天数大于等于3天需要添加附件！", 2000, "red")
			}
			// if (rows.length == 0) {
			// 	return top.mesShow("温馨提示！", "请至少添加一个应急设备！", 2000, "red")
			// }
			// if (gps.location == "yjtxc.coordinatingDispatching") {
			// 	var dateValidate = true
			// 	$(".easyuidate").each(function (i, v) {
			// 		if (!$(v).datebox("getValue")) {
			// 			dateValidate = false
			// 			return false
			// 		}
			// 	})
			// 	if (!dateValidate) {
			// 		return top.mesShow("温馨提示！", "请填写 使用开始时间 和 使用结束时间 ！", 2000, "red")
			// 	}
			// }
			// var datas = []
			// var configType = $(".deviceConfiguration").combobox("getValue")
			// for (var i in rows) {
			// 	if (configType == "卫星车" || configType == "大型应急车") {
			// 		datas.push({
			// 			theDriver: rows[i].theDriver,
			// 			theDriverPhone: rows[i].theDriverPhone,
			// 			licensePlate: rows[i].licensePlate
			// 		})
			// 	} else if (configType == "无人机高空站") {
			// 		datas.push({
			// 			uavStationLeader: rows[i].uavStationLeader,
			// 			uavStationLeaderPhone: rows[i].uavStationLeaderPhone,
			// 			licensePlate: configType
			// 		})
			// 	} else {
			// 		datas.push({
			// 			chiefWritingStation: rows[i].chiefWritingStation,
			// 			uavStationLeaderPhone: rows[i].uavStationLeaderPhone,
			// 			licensePlate: configType
			// 		})
			// 	}
			// 	datas[i].cities = rows[i].cities
			// 	datas[i].equipmentManufacturer = rows[i].equipmentManufacturer
			// 	datas[i].startTime = rows[i].startTime
			// 	datas[i].endTime = rows[i].endTime
			// 	datas[i].carConfiguRation = configType
			// 	if (gps.location && gps.location != "yjtxc.start") {
			// 		datas[i].id = rows[i].id
			// 	}
			// 	if (!gps.location) {
			// 		datas[i].carId = rows[i].id
			// 	} else {
			// 		datas[i].carId = rows[i].carId
			// 	}
			// }
			// data.equipmentList = datas
			data.equipmentList = [];
			data.equipmentMoonList = [];
			data.equipmentUavList = [];
			data.equipmentBleList = [];
			var timeFlag = 1;
			for (var i in carTableA) {
				$("#carTableA").datagrid("endEdit", i)
				var item = {
					carConfiguRationParent:carTableA[i].carConfiguRationParent || '',
					carConfiguRation: carTableA[i].carConfiguRation || '',
					carId: carTableA[i].carId,
					cities: carTableA[i].cities,
					equipmentManufacturer: carTableA[i].equipmentManufacturer,
					licensePlate: carTableA[i].licensePlate,
					theDriver: carTableA[i].theDriver,
					theDriverPhone: carTableA[i].theDriverPhone,
					openingStationPhone: carTableA[i].openingStationPhone,
					openingStation: carTableA[i].openingStation,
					id:carTableA[i].id
				}
				if (carTableA[i].startTime) item.startTime = carTableA[i].startTime;
				if (carTableA[i].endTime) item.endTime = carTableA[i].endTime;
				timeFlag = calcTime(item);
				if(timeFlag < 0) return false;
				data.equipmentList.push(item)
			}
			for (var i in carTableB) {
				$("#carTableB").datagrid("endEdit", i)
				var item = {
					carConfiguRation: '卫星车',
					carId: carTableB[i].carId,
					cities: carTableB[i].cities,
					equipmentManufacturer: carTableB[i].equipmentManufacturer,
					licensePlate: carTableB[i].licensePlate,
					theDriver: carTableB[i].theDriver,
					theDriverPhone: carTableB[i].theDriverPhone,
					openingStationPhone: carTableB[i].openingStationPhone,
					openingStation: carTableB[i].openingStation,
					id:carTableB[i].id
				}
				if (carTableB[i].startTime) item.startTime = carTableB[i].startTime;
				if (carTableB[i].endTime) item.endTime = carTableB[i].endTime;
				timeFlag = calcTime(item);
				if (timeFlag < 0) return false;
				data.equipmentMoonList.push(item)
			}
			for (var i in carTableC) {
				$("#carTableC").datagrid("endEdit", i)
				var item = {
					carConfiguRation: '无人机高空站',
					carId: carTableC[i].carId,
					cities: carTableC[i].cities,
					equipmentManufacturer: carTableC[i].equipmentManufacturer,
					licensePlate: carTableC[i].licensePlate,
					uavStationLeader: carTableC[i].uavStationLeader,
					uavStationLeaderPhone: carTableC[i].uavStationLeaderPhone,
					id:carTableC[i].id
				}
				if (carTableC[i].startTime) item.startTime = carTableC[i].startTime;
				if (carTableC[i].endTime) item.endTime = carTableC[i].endTime;
				timeFlag = calcTime(item);
				if (timeFlag < 0) return false;
				data.equipmentUavList.push(item)
			}
			for (var i in carTableD) {
				$("#carTableD").datagrid("endEdit", i)
				var item = {
					carConfiguRation: '卫星便携站',
					carId: carTableD[i].carId,
					cities: carTableD[i].cities,
					equipmentManufacturer: carTableD[i].equipmentManufacturer,
					licensePlate: carTableD[i].licensePlate,
					chiefWritingStation: carTableD[i].chiefWritingStation,
					uavStationLeaderPhone: carTableD[i].uavStationLeaderPhone,
					startTime: carTableD[i].startTime,
					guaranteeEndTime: carTableD[i].guaranteeEndTime,
					id:carTableD[i].id
				}
				if (carTableD[i].startTime) item.startTime = carTableD[i].startTime;
				if (carTableD[i].endTime) item.endTime = carTableD[i].endTime;
				timeFlag = calcTime(item);
				if (timeFlag < 0) return false;
				data.equipmentBleList.push(item)
			}
			return data

		}

		// 校验表格时间
		function calcTime(item) {
			var guaranteeStartTimeDate = new Date($('#guaranteeStartTime').datebox('getValue')).getTime();
			var guaranteeEndTimeDate = new Date($('#guaranteeEndTime').datebox('getValue')).getTime();
			if (item.endTime && item.startTime) {
				var startTimeDate = new Date(item.startTime).getTime()
				var endTimeDate = new Date(item.endTime).getTime()
				if (endTimeDate < startTimeDate) {
					mesShow('温馨提示', '使用结束时间不能早于使用开始时间！', 1000, 'orange');
					return -2
				} else if (startTimeDate < guaranteeStartTimeDate || startTimeDate > guaranteeEndTimeDate || endTimeDate < guaranteeStartTimeDate || endTimeDate > guaranteeEndTimeDate) {
					mesShow('温馨提示', '使用开始时间或使用结束时间要在保障时间范围内！', 1000, 'orange');
					return -3
				} else {
					return 1
				}
			} else {
				if(gps.location == 'yjtxc.coordinatingDispatching'){
					mesShow('温馨提示', '使用开始时间或使用结束时间不能为空！', 1000, 'orange');
					return -1
				}else{
					return 1
				}
			}
		}

		function deviceChange(data) {
			rows = []
			if (data.value == returnType) {
				rows = returnData
			}
			reloadTable(data.value)
		}

		function chooseCarCB(data) {
			var type = $(".deviceConfiguration").combobox("getValue")
			for (var i in data.data) {
				if (includes(rows, data.data[i].id)) {
					if (type == "卫星车" || type == "大型应急车") {
						data.data[i].theDriver = data.data[i].openingStation
						data.data[i].theDriverPhone = data.data[i].openingStationPhone
						//	data.data[i].openingStationPhone = data.data[i].openingStationPhone
					}
					rows.push(data.data[i])
				}
			}
			$("#carTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
		}

		//只能选择日期范围内的日期间隔
		function dateHorizon(id, startDate, endDate) {
			$('td[field="' + id + '"] input').each(function (i, v) {
				var opts = {
					styler: function (date) {
						var ifP, starttime, start, endtime, end;
						starttime = startDate + ' ' + '00:00:00';
						start = new Date(starttime.replace("-", "/").replace("-", "/"));
						endtime = endDate + ' ' + '00:00:00';
						end = new Date(endtime.replace("-", "/").replace("-", "/"));
						if (startDate && startDate != "" && endDate && endDate != "") {
							ifP = (date <= end && date >= start);
						} else if (startDate || startDate != "") {
							ifP = date >= start;
						} else if (endDate || endDate != "") {
							ifP = date <= end;
						}
						return ifP ? "color:#39aef5" : "color:#cdc4c4";
					},
					validator: function (date) {
						var ifP, starttime, start, endtime, end;
						starttime = startDate + ' ' + '00:00:00';
						start = new Date(starttime.replace("-", "/").replace("-", "/"));
						endtime = endDate + ' ' + '00:00:00';
						end = new Date(endtime.replace("-", "/").replace("-", "/"));
						if (startDate && startDate != "" && endDate && endDate != "") {
							ifP = (date <= end && date >= start);
						} else if (startDate || startDate != "") {
							ifP = date >= start;
						} else if (endDate || endDate != "") {
							ifP = date <= end;
						}
						return ifP;
					}
				};
				if ($(v).hasClass("easyui-datebox")) {
					$(v).datebox('calendar').calendar(opts);
				}
				if ($(v).hasClass("easyui-datetimebox")) {
					$(v).datetimebox('calendar').calendar(opts);
				}
				$(v).next().removeClass('textbox-invalid');
				$(v).next().find("input").removeClass('validatebox-invalid');
			});
		};

		function includes(arr, key) {
			for (var i in arr) {
				if (key == arr[i][gps.location ? "carId" : "id"]) {
					return false
				}
			}
			return true
		}

		function reloadTable(data) {
			if (data == "卫星车" || data == "大型应急车") {
				loadTable(carCols())
			} else if (data == "无人机高空站") {
				loadTable(uavCols({ title: "无人机高空基站负责人", field: "uavStationLeader" }))
			} else {
				loadTable(uavCols({ title: "卫星便携站负责人", field: "chiefWritingStation" }))
			}
		}
	</script>
</head>

<body class="body_page" style="padding:85px 0;">
	<form id="dailyLocalForm" method="post" formLocation="yjtxc.start" noNextUserDecisionId="end"
		contentType="application/json; charset=utf-8" cmd-select="action/applicationForm/getFormDetail"
		getcallback="getcallback()" nextBtnOther="nextBtnOther()" onSubmit="onSubmit()">
		<div class="pageInfo">
			<div class="pageInfoD">
				<a class="hide btn small fl mr15 nextBtn"><i class="iconfont">&#xe688;</i>
					<font>流转下一步</font>
				</a>
				<a class="hide btn small fl mr15 flowTrack"><i class="iconfont">&#xe68c;</i>
					<font>流程跟踪</font>
				</a>
				<a class="hide btn small fl mr15 viewComments"><i class="iconfont">&#xe629;</i>
					<font>查看意见</font>
				</a>
				<a class="hide btn small fl mr15 processImg"><i class="iconfont">&#xe6bd;</i>
					<font>流程图</font>
				</a>
			</div>
		</div>
		<input type="hidden" name="id" id="id" class="id" noReset="true">
		<input type="hidden" name="pmInsId" id="pmInsId" class="pmInsId" noReset="true">
		<input type="hidden" name="processType" id="processType" class="processType" value="A" noReset="true">

		<table border="0" cellpadding="0" th:colspan="6" cellspacing="18">
			<tr>
				<td colspan="4" class="myTitle">
					日常应急调度申请表
				</td>
			</tr>
			<tr>
				<td colspan="4" class="splite">申请信息</td>
			</tr>
			<tr>
				<td align="right" width="135">工单编号：</td>
				<td width="300">
					<input name="orderNumber" class="textAndInput_readonly orderNumber" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="135">申请人：</td>
				<td width="300">
					<input name="userName" class="textAndInput_readonly userName" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
					<input class="trueName" name="trueName" type="hidden" />
				</td>
			</tr>
			<tr>
				<td align="right" width="135">申请人所在单位：</td>
				<td width="300">
					<input name="applicantUnit" class="textAndInput_readonly applicantUnit" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="135">申请人联系方式：</td>
				<td width="300">
					<input name="applicantPhone" class="textAndInput_readonly applicantPhone" readonly="readonly" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="135">
					<font class='col_r'>*</font>保障开始时间：
				</td>
				<td width="300">
					<input id="guaranteeStartTime" name="guaranteeStartTime" type="text" class="easyui-datetimebox guaranteeStartTime" editable="false"
						required="required" data-options="panelHeight:'auto'" validType="startDateCheck['guaranteeEndTime','guaranteeStartTime']" style="width:100%;height:32px;" />
				</td>
				<td align="right" width="135">
					<font class='col_r'>*</font>保障结束时间：
				</td>
				<td width="300">
					<input id="guaranteeEndTime" name="guaranteeEndTime" type="text" class="easyui-datetimebox guaranteeEndTime"
						required="required" validType="endDateCheck['guaranteeStartTime','guaranteeEndTime']" data-options="panelHeight:'auto',editable:false" style="width:100%;height:32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="135">保障具体地址：</td>
				<td width="300">
					<input class="easyui-validatebox specificAddress" name="specificAddress" type="text"
						style="width:300px; height: 32px;" />
				</td>
				<td align="right" width="135">预计人数：</td>
				<td width="300">
					<input class="easyui-validatebox numberExpected" name="numberExpected" validType="zinteger" type="text"
						style="width:300px; height: 32px;" />
				</td>
			</tr>
			<tr>
				<td align="right" width="135">
					<font class='col_r'>*</font>活动名称：
				</td>
				<td colspan="3">
					<input class="easyui-validatebox nameEvent" required="required" name="nameEvent" type="text"
						style="width:100%; height: 32px;" />
				</td>
<!--				<td align="right" width="135">-->
<!--					<font class='col_r'>*</font>申请应急设备配置：-->
<!--				</td>-->
<!--				<td width="300">-->
<!--					<input class="easyui-combobox deviceConfiguration" name="deviceConfiguration" required="required"-->
<!--						style="width:300px; height: 32px;"-->
<!--						data-options="-->
<!--						valueField: 'value',-->
<!--						editable: false,-->
<!--						ischooseall: true,-->
<!--						panelHeight: 'auto',-->
<!--						textField: 'name',-->
<!--						onSelect: deviceChange,-->
<!--						data: [] " />-->
<!--				</td>-->
			</tr>
			<tr>
				<td align="right" width="135">
					<font class='col_r'>*</font>事件概要：
				</td>
				<td colspan="3">
					<textarea name="itemOverview" class="easyui-validatebox itemOverview"
						validType="maxLength[300,'itemOverviewRest']" required="required"
						style="width:100%;height:107px;resize:none;line-height: 18px;"
						placeholder="活动简要说明，文字不超过300字，如因疫情原因跨地市调度，需提供出入该地市的相关政策信息"></textarea>
					<span class="itemOverviewRest"></span>
				</td>
			</tr>
			<tr>
				<td align="right" width="135">
					<font class='col_r'>*</font>附件：
				</td>
				<td colspan="3">
					<input name="attachmentList" id="attachmentList" type="text" file="true"
						class="cselectorImageUpload attachmentList" mulaccept="true"
						btnmsg="<i class='iconfont' title='添加'>&#xe641;</i>"
						href="sys/file/uploadProcessFiles?pmInsType=A&pmInsTypePart=1" />
				</td>
			</tr>
			<!-- <tr>
				<td colspan="4"><a class="btn small fr chooseCar">
						<font>选择应急设备</font>
					</a></td>
			</tr>
			<tr>
				<td colspan="4" class="carTable">
					<table id="carTable"></table>
				</td>
			</tr> -->
			<tr>
				<td colspan="4" class="splite">应急设备信息</td>
			</tr>
			<tr>
				<td colspan="4" class="carTableA">
					<table id="carTableA"></table>
				</td>
				<td style="vertical-align: top;">
					<a class="btn small addBtn" data-type="大型应急车">选择应急设备</a>
				</td>
			</tr>
			<tr style="display: none;" >
				<td colspan="4" class="carTableB">
					<table id="carTableB"></table>
				</td>
				<td style="vertical-align: top;">
					<a class="btn small addBtn" data-type="卫星车">选择卫星车</a>
				</td>
			</tr>
			<tr style="display: none;">
				<td colspan="4" class="carTableC">
					<table id="carTableC"></table>
				</td>
				<td style="vertical-align: top;">
					<a class="btn small addBtn" data-type="无人机高空站">选择无人机高空站</a>
				</td>
			</tr>
			<tr style="display: none;">
				<td colspan="4" class="carTableD">
					<table id="carTableD"></table>
				</td>
				<td style="vertical-align: top;">
					<a class="btn small addBtn" data-type="卫星便携站">选择卫星便携站</a>
				</td>
			</tr>
		</table>
	</form>
</body>

</html>