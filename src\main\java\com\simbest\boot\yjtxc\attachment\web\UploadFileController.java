package com.simbest.boot.yjtxc.attachment.web;

import com.google.common.collect.Sets;
import com.simbest.boot.base.web.controller.LogicController;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysFileService;
import com.simbest.boot.yjtxc.attachment.service.IFileExtendService;
import com.simbest.boot.util.json.JacksonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @用途:
 * @作者：zsf
 * @时间: 2020/9/24
 * 视频上报
 */
@Api(description = "")
@Slf4j
@RestController
@RequestMapping(value = "/action/uploadFile")
public class UploadFileController extends LogicController<SysFile, String> {

    @Autowired
    private ISysFileService fileService;

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    public UploadFileController(ISysFileService fileService) {
        super(fileService);
        this.fileService = fileService;
    }

    @ApiOperation(value = "传统方式上传附件（支持IE8）,支持关联流程", notes = "会保存到数据库SYS_FILE")
    @PostMapping(value = {"/uploadProcessFiles", "/uploadProcessFiles/sso", "/uploadProcessFiles/api"})
    @ResponseBody
    public void uploadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        JsonResponse jsonResponse = doUploadFile(request, response);
        String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        out.println(result);
        out.close();
    }

    /**
     * 上传文件,支持关联流程
     */
    private JsonResponse doUploadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Set<MultipartFile> uploadingFileSet = Sets.newHashSet();
        MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
        //优先通过指定参数名称file获取文件
        Collection<MultipartFile> uploadingFileList = mureq.getFiles("file");
        uploadingFileList.forEach(f -> uploadingFileSet.add(f));
        //再通过不指定参数名称获取文件
        Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
        multipartFiles.values().forEach(f -> uploadingFileSet.add(f));
        List<SysFile> sysFiles = fileExtendService.uploadProcessFiles(uploadingFileSet,
                request.getParameter("pmInsType"),
                request.getParameter("pmInsId"),
                request.getParameter("pmInsTypePart"));
        JsonResponse jsonResponse;
        if (!sysFiles.isEmpty()) {
            UploadFileResponse uploadFileResponse = new UploadFileResponse();
            uploadFileResponse.setSysFiles(sysFiles);
            jsonResponse = JsonResponse.success(uploadFileResponse);
        } else {
            jsonResponse = JsonResponse.defaultErrorResponse();
        }
        return jsonResponse;
    }

}
