package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.yjtxc.apply.model.GarageConfiguration;
import com.simbest.boot.yjtxc.apply.model.SatellitePortableLibrary;
import com.simbest.boot.yjtxc.apply.model.UavAerialConfigurationStation;
import com.simbest.boot.yjtxc.apply.repository.UavAerialConfigurationStationRepository;
import com.simbest.boot.yjtxc.apply.service.IUavAerialConfigurationStationService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.*;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:20
 * @describe 无人机高空配置库
 */
@Slf4j
@Service
public class UavAerialConfigurationStationServiceImpl extends LogicService<UavAerialConfigurationStation, String> implements IUavAerialConfigurationStationService {

    private UavAerialConfigurationStationRepository repository;

    public UavAerialConfigurationStationServiceImpl(UavAerialConfigurationStationRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    @Autowired
    private SysFileService fileService;

    final String param1 = "action/uavAerialConfigurationStation/";

    /**
     * 导入流程固化涉及人员配置信息
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        List<UavAerialConfigurationStation> garageConfigurationList = null;
        try {
            PrintWriter out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            for (MultipartFile uploadfile : multipartFiles.values()) {
                /**
                 * 这里要区分是“外置设备”还是“电脑终端设备”,处理之后返回最终的设备信息集合
                 */
                String originalFilename = uploadfile.getOriginalFilename();
                // 如果为电脑终端设备
                if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("xls")) {
                    // 先上传至sys_file表,注意sheetName名要与excel保持一致
                    UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"),
                            request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), UavAerialConfigurationStation.class,
                            "无人机高空战配置库");
                    jsonResponse.setData(uploadFileResponse);
                    jsonResponse.setMessage("上传成功");
                } else {
                    jsonResponse.setMessage("请按照模板上传相关数据!");
                }
            }
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(garageConfigurationList);
    }

    /**
     * 条件查询无人机高空战配置库
     *
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @Override
    public JsonResponse findByAllUavAerialConfigurationStation(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap) {


        Page<UavAerialConfigurationStation> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");
        try {
            //获取地市
            String cities = MapUtil.getStr(paramMap, "cities");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            Specification<UavAerialConfigurationStation> specification = (root, query, criteriaBuilder) -> {
                List<javax.persistence.criteria.Predicate> predicateList = Lists.newArrayList();
                if (StrUtil.isNotEmpty(cities)) {
                    predicateList.add(criteriaBuilder.like(root.get("cities"), "%" + cities + "%"));
                }
                if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                    predicateList.add(criteriaBuilder.like(root.get("equipmentManufacturer"), "%" + equipmentManufacturer + "%"));
                }
                if (true) {
                    predicateList.add(criteriaBuilder.equal(root.get("enabled"), true));
                }
                javax.persistence.criteria.Predicate[] predicates = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicates));
            };
            findAllGarageConfiguration = repository.findAll(specification, pageable);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);
    }

    /**
     * 无人机高空配置库信息
     *
     * @param uavAerialConfigurationStationList
     * @return
     */
    //@Transactional
    @Override
    public JsonResponse saveUavAerialConfigurationStation(List<UavAerialConfigurationStation> uavAerialConfigurationStationList) {
        List<UavAerialConfigurationStation> uavAerial = CollectionUtil.newLinkedList();
        Iterator<UavAerialConfigurationStation> iterator = uavAerialConfigurationStationList.iterator();
        while (iterator.hasNext()) {
            UavAerialConfigurationStation uavAerialConfigurationStation = iterator.next();
            uavAerialConfigurationStation.setSchedulingCondition("可调度");
            uavAerialConfigurationStation.setCarConfiguRationParent("无人机高空站");
            uavAerial.add(uavAerialConfigurationStation);
        }
        saveAll(uavAerial);
        return JsonResponse.success("数据保存成功");
    }

    /**
     * 修改无人机高空配置库信息
     *
     * @param uavAerialConfigurationStation
     * @return
     */
    @Override
    public JsonResponse updateUavAerialConfigurationStation(UavAerialConfigurationStation uavAerialConfigurationStation) {
        update(uavAerialConfigurationStation);
        return JsonResponse.success("数据修改成功");
    }

    /**
     * 删除无人机高空配置库信息
     *
     * @param id
     * @return
     */
    @Override
    public JsonResponse deleteByUavAerialConfigurationStation(String id) {
        deleteById(id);
        return JsonResponse.success("数据删除成功");
    }

    /**
     * @return
     */
    @Override
    public List<Map<String, Object>> findByDrone() {
        return repository.findByDrone();
    }

    /**
     * 无人机高空站按照地市条件查询
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findByDroneAndCities(String cities) {
        return repository.findByDroneAndCities(cities);
    }
}
