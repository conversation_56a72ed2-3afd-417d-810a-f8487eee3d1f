package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.yjtxc.apply.model.FiveBaseStation;
import com.simbest.boot.yjtxc.apply.repository.FiveBaseStationRespository;
import com.simbest.boot.yjtxc.apply.service.IFiveBaseStationService;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 15:05
 * @describe 5G基站站号
 */
@Slf4j
@Service
public class FiveBaseStationServiceImpl extends LogicService<FiveBaseStation, String> implements IFiveBaseStationService {


    private FiveBaseStationRespository respository;

    public FiveBaseStationServiceImpl(FiveBaseStationRespository respository) {
        super(respository);
        this.respository = respository;
    }


    @Autowired
    private ISysOperateLogService sysOperateLogService;

    final String param1 = "action/fiveBaseStation/";


    /**
     * @param pmInsId
     */
    @Override
    public void deleteByFiveBaseStationPmInsId(String pmInsId) {
        respository.deleteByPmInsId(pmInsId);
    }

    /**
     * 维护5G基站号
     *
     * @param fiveBaseStation
     * @return
     */
    //@Transactional
    @Override
    public JsonResponse updateFiveBaseStation(FiveBaseStation fiveBaseStation) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("FiveBaseStation", fiveBaseStation);
        optLogParam.put("operateInterface", param1 + "updateFiveBaseStation");
        try {
            deleteById(fiveBaseStation.getId());
            insert(fiveBaseStation);
        } catch (Exception e) {
            Exceptions.printException(e);
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 查询5G基站号
     *
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse findByPmInsIdFiveBaseStation(String pmInsId) {
        return JsonResponse.success(respository.findByPmInsIdFiveBaseStation(pmInsId));
    }

    /**
     * 批量添加5G基站号
     *
     * @param fiveBaseStationList
     * @return
     */
    @Override
    public JsonResponse saveFiveBaseStation(List<FiveBaseStation> fiveBaseStationList, String pmInsId) {
        List<FiveBaseStation> list = CollectionUtil.newArrayList();
        Iterator<FiveBaseStation> iterator = fiveBaseStationList.iterator();
        while (iterator.hasNext()) {
            FiveBaseStation fiveBaseStation = iterator.next();
            String fiveBaseId = fiveBaseStation.getFiveBaseId();
            if (StrUtil.isNotEmpty(fiveBaseId)) {
                respository.deleteByPmInsId(fiveBaseId);
            }
            fiveBaseStation.setId("");
            list.add(fiveBaseStation);
        }
        if (ObjectUtil.isNotEmpty(list)) {
            saveAll(list);
        } else {
            respository.deleteByPmInsId(pmInsId);
        }
        return JsonResponse.success("数据保存成功");
    }


}
