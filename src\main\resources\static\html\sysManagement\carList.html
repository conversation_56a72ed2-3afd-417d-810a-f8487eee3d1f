<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <title>待办列表</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <!--
    <link href="http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{http://************:8088/simbestui/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    -->
    <link href="../../fonts/iconfont/iconfont.css?v=svn.revision" th:href="@{/fonts/iconfont/iconfont.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/default/easyui.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/js/themes/icon.css?v=svn.revision" th:href="@{http://************:8088/simbestui/js/themes/icon.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/public.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/public.css?v=svn.revision}" rel="stylesheet"/>
    <link href="http://************:8088/simbestui/css/pages.css?v=svn.revision" th:href="@{http://************:8088/simbestui/css/pages.css?v=svn.revision}" rel="stylesheet"/>
    <script src="http://************:8088/simbestui/js/jquery.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.easyui.min.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/easyui-lang-zh_CN.js?v=svn.revision}" type="text/javascript"></script>
    <script src="../../js/jquery.config.js?v=$svn.revision" th:src="@{/js/jquery.config.js?v=$svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.zjsfile.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.aduq.js?v=svn.revision}" type="text/javascript"></script>
    <script src="http://************:8088/simbestui/js/jquery.process.js?v=svn.revision" th:src="@{http://************:8088/simbestui/js/jquery.process.js?v=svn.revision}" type="text/javascript"></script>
    <script type="text/javascript">
        var editRow = 0
        var rows = []
        var textEditor = {
            type: "textbox",
            options: {
                height:30
            }
        }
        var numberEditor = {
            type: "numberbox",
            options: {
                height:30
            }
        }
        var typeA = [
            { title: "5G设备厂家", field: "equipmentManufacturer", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "BBU型号", field: "bbuModel", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板是否支持反开4G", field: "basebandSupports", width: 250,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板型号1", field: "basebandBoardModelOne", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板1数量", field: "basebandBoardNumberOne", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "基带板型号2", field: "basebandBoardModelTwo", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板2数量", field: "basebandBoardNumberTwo", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "AAU型号", field: "aauModel", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "AAU数量", field: "aauNumber", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "操作", field: "opt", width: 100, align: "center",
                formatter: function(value, row, index) {
                    return "<a href='#' onclick='deleteList(this)' index='"+index+"' id='"+row.id+"'>删除</a>"
                }
            }
        ]
        var BCTitle = gps.type == "B" ? "LTE FDD设备厂家" : "LTE TDD设备厂家"
        var typeB = [
            { title: BCTitle, field: "lteFddManufacturer", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "BBU型号", field: "bbuModel", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板型号1", field: "basebandBoardModelOne", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板1数量", field: "basebandBoardNumberOne", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "基带板型号2", field: "basebandBoardModelTwo", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "基带板2数量", field: "basebandBoardNumberTwo", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "RRU型号1", field: "rruModelOne", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "RRU型号1数量", field: "rruNumberOne", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "RRU型号2", field: "rruModelTwo", width: 200,tooltip:true, align: "center",editor:textEditor },
            { title: "RRU型号2数量", field: "rruNumberTwo", width: 200,tooltip:true, align: "center",editor:numberEditor },
            { title: "操作", field: "opt", width: 100, align: "center",
                formatter: function(value, row, index) {
                    return "<a href='#' onclick='deleteList(this)' index='"+index+"' id='"+row.id+"'>删除</a>"
                }
            }
        ]
        function initTable() {
            var url = "action/fiveBaseStation/findByPmInsIdFiveBaseStation"
            if(gps.type == "B") {
                url = "action/fourFDDBaseStation/findByPmInsIdFourFDDBaseStation"
            }
            if(gps.type == "C") {
                url = "action/fourTDDBaseStation/findByPmInsIdFourTDDBaseStation"
            }
            ajaxgeneral({
                url: url + "?pmInsId="+gps.pmInsId,
                success: function(data) {
                    rows = data.data ? data.data : []
                    loadGrid({
                        "listtable":{
                            "listname":"#listTable",
                            "nowrap": true,
                            "data": { data: { rows: rows, total: rows?rows.length:0 } },
                            "styleClass":"noScroll",
                            "pagination": false,
                            "frozenColumns":[],
                            "columns":[gps.type == "A" ? typeA : typeB],
                            "beginEdit": function(index) {
                                editRow = index
                            }
                        }
                    })
                    $("#listTable").datagrid('enableCellEditing');
                }
            })
        }

        $(function(){
            initTable()
            $(".addList").click(function() {
                $("#listTable").datagrid("endEdit", editRow)
                rows.push({})
                $("#listTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
            })
            $(".submit").click(function() {
                $("#listTable").datagrid("endEdit", editRow)
                var url = "action/fiveBaseStation/saveFiveBaseStation"
                if(gps.type == "B") {
                    url = "action/fourFDDBaseStation/saveFourFDDBaseStation"
                }
                if(gps.type == "C") {
                    url = "action/fourTDDBaseStation/saveFourTDDBaseStation"
                }
                for(var i in rows) {
                    if(gps.type == "A") {
                        rows[i].fiveBaseId = gps.pmInsId
                    } else if(gps.type == "B") {
                        rows[i].fourFddId = gps.pmInsId
                    } else {
                        rows[i].fourTddId = gps.pmInsId
                    }
                }
                ajaxgeneral({
                    url: url+"?pmInsId="+gps.pmInsId,
                    contentType: "application/json; charset=utf-8",
                    data: rows,
                    success: function(data) {
                        top.dialogClose("listCB")
                    }
                })
            })
        });
        function deleteList(param) {
            rows.splice($(param).attr("index"), 1)
            $("#listTable").datagrid("loadData", { data: { rows: rows, total: rows.length } })
        }
    </script>
</head>
<body class="body_page">
    <div class="w100" style="height: 40px;">
        <a class="btn fr ml15 addList"><font>添加</font></a>
        <a class="btn fr submit"><font>保存</font></a>
    </div>
    <div class="listTable"><table id="listTable"></table></div>
</body>
</html>
