package com.simbest.boot.yjtxc.apply.model;

import com.simbest.boot.base.annotations.EntityIdPrefix;
import com.simbest.boot.base.annotations.ExcelVOAttribute;
import com.simbest.boot.cmcc.wf.model.WfFormModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.List;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/28 14:04
 * @describe 应急车库配置
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "us_garage_confi")
@ApiModel(value = "应急车库配置")
public class GarageConfiguration extends WfFormModel {


    @Id
    @Column(name = "id", length = 40)
    @GeneratedValue(generator = "snowFlakeId")
    @GenericGenerator(name = "snowFlakeId", strategy = "com.simbest.boot.util.distribution.id.SnowflakeId")
    @EntityIdPrefix(prefix = "gc") //主键前缀，此为可选项注解
    private String id;

    @Column(length = 40)
    @ApiModelProperty(value = "单据ID")  //关联5G基站站号，4GTDD基站站号
    protected String pmInsId;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    @ExcelVOAttribute(name = "地市", column = "A")
    private String cities;

    @Column(length = 100)
    @ApiModelProperty(value = "地市")
    private String citiesCode;

    @Column(length = 100)
    @ApiModelProperty(value = "设备厂家")
    @ExcelVOAttribute(name = "设备厂家", column = "B")
    private String equipmentManufacturer;

    @Column(length = 140)
    @ApiModelProperty(value = "应急设备分类")
    @ExcelVOAttribute(name = "应急设备分类", column = "C")
    private String carConfiguRationParent;

    @Column(length = 140)
    @ApiModelProperty(value = "应急设备子类")
    @ExcelVOAttribute(name = "应急设备子类", column = "D")
    private String carConfiguRation;

    @Column(length = 140)
    @ApiModelProperty(value = "应急车车牌")
    @ExcelVOAttribute(name = "应急车车牌", column = "E")
    private String licensePlate;

    @Column(length = 100)
    @ApiModelProperty(value = "司机")
    @ExcelVOAttribute(name = "司机", column = "F")
    private String theDriver;

    @Column(length = 100)
    @ApiModelProperty(value = "大型应急车+卫星车司机手机号")
    @ExcelVOAttribute(name = "司机手机号", column = "G")
    private String theDriverPhone;

    @Column(length = 60)
    @ApiModelProperty(value = "负责开站人员")
    @ExcelVOAttribute(name = "负责开站人员", column = "H")
    private String openingStation;

    @Column(length = 60)
    @ApiModelProperty(value = "负责开站人员手机号")
    @ExcelVOAttribute(name = "负责开站人员手机号", column = "I")
    private String openingStationPhone;

    @Column(length = 60)
    @ApiModelProperty(value = "5G改造情况")
    @ExcelVOAttribute(name = "5G改造情况", column = "J")
    private String fivetransform;

    @Column(length = 40)
    @ApiModelProperty(value = "5G基站站号")
    @ExcelVOAttribute(name = "5G基站站号", column = "K")
    private String fiveBaseStation;

    @Column(length = 100)
    @ApiModelProperty(value = "5G反开4G基站站号")
    @ExcelVOAttribute(name = "5G反开4G基站站号", column = "L")
    private String fivereversefour;

    @Column(length = 100)
    @ApiModelProperty(value = "4GFDD基站站号")
    @ExcelVOAttribute(name = "4GFDD基站站号", column = "M")
    private String fourFDDBaseStation;

    @Column(length = 100)
    @ApiModelProperty(value = "4GTDD基站站号")
    @ExcelVOAttribute(name = "4GTDD基站站号", column = "N")
    private String fourTDDBaseStation;

    @Column(length = 40)
    @ApiModelProperty(value = "调度状态")
    @ExcelVOAttribute(name = "调度状态", column = "O")
    private String schedulingCondition;

    @Column(length = 40)
    @ApiModelProperty(value = "启用||停用")
    private String enableLocalhost;

    @ApiModelProperty(value = "扩展字段01")
    @Column(name = "spare01")
    private String spare01;

    @ApiModelProperty(value = "扩展字段02")
    @Column(name = "spare02")
    private String spare02;

    @ApiModelProperty(value = "扩展字段03")
    @Column(name = "spare03")
    private String spare03;



    @Transient
    @ApiModelProperty(value = "5G基站站号")
    List<FiveBaseStation> fiveBaseStationList;

    @Transient
    @ApiModelProperty(value = "4GFDD基站站号") // 4GFDD基站站号,区别F
    List<FourFDDBaseStation> FourFDDBaseStationList;

    @Transient
    @ApiModelProperty(value = "4GTDD基站站号") // 4GFDD基站站号,区别T
    List<FourTDDBaseStation> FourTDDBaseStationList;


}
